# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.

#混淆时采用的算法
-optimizations !code/simplification/arithmetic,!field/*,!class/merging/*

-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
-renamesourcefileattribute SourceFile

#指定压缩级别
-optimizationpasses 5

#不跳过非公共的库的类成员
-dontskipnonpubliclibraryclassmembers


-dontusemixedcaseclassnames
-dontskipnonpubliclibraryclasses
-dontpreverify
-verbose
# 把混淆类中的方法名也混淆了
-useuniqueclassmembernames

# 【重要】以下3行可排除R资源文件及其及类的混淆，否则运行时将报找不到资源
-keepattributes InnerClasses
-keep class **.R
-keep class **.R$* {
    <fields>;
}

# 保留Android的一些默认API
-keep class android.support.v4.** { *; }
-keep class android.support.v7.** { *; }
-keep class android.support.annotation.** { *; }
#-keep public class * extends android.app.Activity
-keep public class * extends android.app.Application
-keep public class * extends android.app.Service
-keep public class * extends android.content.BroadcastReceiver
-keep public class * extends android.content.ContentProvider
-keep public class * extends android.app.backup.BackupAgentHelper
-keep public class * extends android.preference.Preference
-keepattributes *Annotation*
# 避免使用泛型的位置混淆后出现类型转换错误:
-keepattributes Signature
-keep public class com.google.vending.licensing.ILicensingService
-keep public class com.android.vending.licensing.ILicensingService


# Picasso图片缓存库的混淆配置
-keep class com.parse.*{ *; }
-dontwarn com.parse.**
-dontwarn com.squareup.picasso.**
-keepclasseswithmembernames class * {
    native <methods>;
}

# 加入以下两行可以解决proguard的"Error:Execution failed for
#   task ':app:transformClassesAndResourcesWithProguardForRelease'"错误提示
-dontwarn com.amap.api.**
-dontwarn com.autonavi.**

# 保留Apache http相关API:
-keep class org.apache.http.** { *; }
-keep class android.net.http.** { *; }



# 保留所有自定义View的相关API（这些View可能用于了layout的xml文件里了）
-keepclasseswithmembers class * {
  public <init>(android.content.Context, android.util.AttributeSet);
}
-keepclasseswithmembers class * {
  public <init>(android.content.Context, android.util.AttributeSet, int);
}

## 保留所有Acvitity子类
#-keepclassmembers class * extends android.app.Activity {
#    public void *(android.view.View);
#}
-keep public class * extends android.app.Application
-keep public class * extends android.app.Service
-keep public class * extends android.content.BroadcastReceiver
-keep public class * extends android.content.ContentProvider

#Fragment不需要在AndroidManifest.xml中注册，需要额外保护下
-keep public class * extends android.support.v4.app.Fragment
-keep public class * extends androidx.fragment.app.Fragment


# 保留所有枚举
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

-keep class * implements android.os.Parcelable {
  public static final android.os.Parcelable$Creator *;
}
# 保留JSON、Parcelable、Serailizable等对象（否则代码中序列化、反序列化会失败）:
-keepclassmembers class * {
   public <init>(org.json.JSONObject);
}
-keepnames class * implements java.io.Serializable
-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    !static !transient <fields>;
    !private <fields>;
    !private <methods>;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}

# 忽略警告
-dontwarn android.support.v4.**
-dontwarn android.support.v7.**
-dontwarn com.eva.epc.common.file.**
-dontwarn com.paypal.android.sdk.**
-dontwarn io.card.payment.**

# 忽略警告(针对Android X)
-keep class com.google.android.material.** {*;}
-keep class androidx.** {*;}
-keep public class * extends androidx.**
-keep interface androidx.** {*;}
-dontwarn com.google.android.material.**
-dontnote com.google.android.material.**
-dontwarn androidx.**

# 排除netty的混淆
-keepattributes Signature,InnerClasses
-keepclasseswithmembers class io.netty.** { *;}
-keepnames class io.netty.** { *;}

# 排除google相关的混淆（比如google分析的包等）
-keep class com.google.**
# 保留Google GSON相关API:
-keep class sun.misc.Unsafe { *; }
-keep class com.google.gson.stream.** { *; }
-keep class com.google.gson.** { *;}

# 保留fastjson相关API:
-keep class com.alibaba.fastjson.** { *;}


## glide start
-keep public class * implements com.bumptech.glide.module.GlideModule
-keep public enum com.bumptech.glide.load.resource.bitmap.ImageHeaderParser$** {
  **[] $VALUES;
  public *;
}


# 内部WebView混淆过滤
-keepclassmembers class * {
    @android.webkit.JavascriptInterface <methods>;
}

-dontwarn net.sourceforge.pinyin4j.**
-keep class net.sourceforge.pinyin4j.**{*;}
-keep class net.sourceforge.pinyin4j.format.**{*;}
-keep class net.sourceforge.pinyin4j.format.exception.**{*;}


# ButterKnife混淆配置开始
-keep class butterknife.** { *; }
-dontwarn butterknife.internal.**
-keep class **$$ViewBinder { *; }
-keepclasseswithmembernames class * {
    @butterknife.* <fields>;
}
-keepclasseswithmembernames class * {
    @butterknife.* <methods>;
}
# ButterKnife混淆配置结束



## Retrofit start
-dontwarn retrofit2.**
-keep class retrofit2.** { *; }
-dontnote retrofit2.Platform
-dontnote retrofit2.Platform$IOS$MainThreadExecutor
-dontwarn retrofit2.Platform$Java8
-keepattributes Signature
-keepattributes Exceptions

# OkHttp
-dontwarn okhttp3.**
-keep class okhttp3.** { *; }
-keep interface okhttp3.** { *; }
-keepattributes Signature
-keepattributes Exceptions

# Okio
-dontwarn okio.**
-keep class okio.** { *; }
-keepattributes Signature
-keepattributes Exceptions

-keep class androidx.security.crypto.** { *; }

# RxJava RxAndroid
-dontwarn sun.misc.**
-keepclassmembers class rx.internal.util.unsafe.*ArrayQueue*Field* {
    long producerIndex;
    long consumerIndex;
}
-keepclassmembers class rx.internal.util.unsafe.BaseLinkedQueueProducerNodeRef {
    rx.internal.util.atomic.LinkedQueueNode producerNode;
}
-keepclassmembers class rx.internal.util.unsafe.BaseLinkedQueueConsumerNodeRef {
    rx.internal.util.atomic.LinkedQueueNode consumerNode;
}



## glide start
-keep public class * implements com.bumptech.glide.module.GlideModule
-keep public enum com.bumptech.glide.load.resource.bitmap.ImageHeaderParser$** {
  **[] $VALUES;
  public *;
}


#eventbus-2.4.0.jar 开始
-dontwarn  de.greenrobot.event.util.**
-keepclassmembers  class * {
   public void onEvent*(***);
}
-dontnote com.clean.eventbus.event.**
-dontnote **.event.**
#eventbus-2.4.0.jar 结束

# liveeventbus
-dontwarn com.jeremyliao.liveeventbus.**
-keep class com.jeremyliao.liveeventbus.** { *; }
-keep class android.arch.lifecycle.** { *; }
-keep class android.arch.core.** { *; }

# Android Room
-keep class * extends androidx.room.RoomDatabase {*;}
-keep class androidx.room.** { *; }
-keep public class * extends androidx.room.RoomDatabase
-keep public class * extends androidx.room.migration.Migration
-dontwarn androidx.room.paging.**

# MPAndroidChart
-keep class com.github.mikephil.charting.** { *; }
-dontwarn com.github.mikephil.charting.**

# Lottie
-keep class com.airbnb.lottie.** { *; }
-dontwarn com.airbnb.lottie.**

-keep class com.zhpan.indicator.** { *; }
-dontwarn com.zhpan.indicator.**


# Kotlin Coroutines
-keepclassmembers class kotlinx.coroutines.internal.MainDispatcherFactory {}
-keepclassmembers class kotlinx.coroutines.CoroutineExceptionHandler {}
-keepclassmembers class kotlinx.coroutines.android.AndroidExceptionPreHandler {}
-keepclassmembers class kotlinx.coroutines.android.AndroidDispatcherFactory {}

# kotlinx.coroutines
-keep class kotlinx.coroutines.internal.MainDispatcherFactory { *; }
-keep class kotlinx.coroutines.CoroutineExceptionHandler { *; }
-keep class kotlinx.coroutines.android.AndroidExceptionPreHandler { *; }
-keep class kotlinx.coroutines.android.AndroidDispatcherFactory { *; }
-dontwarn kotlinx.coroutines.**

# kotlinx.coroutines.flow.internal
-keep class kotlinx.coroutines.flow.internal.SafeCollectorKt { *; }


#R 资源文件
-keep public class com.example.newdc.R$*{
public static final int *;
}

#aidl
-keep class android.content.pm.** { *; }
-keep public class java.lang.reflect.Method{ *; }

#-keep class com.blankj.utilcode.util.** { *; }
#-keep class com.hm.ck.** { *; }
#-keep class h.** { *; }
#-keep class q.** { *; }
#-keep class wc.evfwcis.knti.** { *; }
#保活结束

#指定package模糊字典
-packageobfuscationdictionary ./proguard-keys.txt
#指定外部模糊字典
-obfuscationdictionary ./proguard-keys.txt
#指定class模糊字典
-classobfuscationdictionary ./proguard-keys.txt

-keep class com.work.saving.syncmanager.** { *; }
-keep class com.google.firebase.** { *; }
#tenjin
-keep class com.tenjin.** { *; }
-keep public class com.google.android.gms.ads.identifier.** { *; }
-keep public class com.google.android.gms.common.** { *; }
-keep public class com.android.installreferrer.** { *; }
-keep class * extends java.util.ListResourceBundle {
    protected java.lang.Object[][] getContents();
}
-keepattributes *Annotation*

#oaid
-keep class XI.CA.XI.**{*;}
-keep class XI.K0.XI.**{*;}
-keep class XI.XI.K0.**{*;}
-keep class XI.xo.XI.XI.**{*;}
-keep class com.asus.msa.SupplementaryDID.**{*;}
-keep class com.asus.msa.sdid.**{*;}
-keep class com.bun.lib.**{*;}
-keep class com.bun.miitmdid.**{*;}
-keep class com.huawei.hms.ads.identifier.**{*;}
-keep class com.samsung.android.deviceidservice.**{*;}
-keep class com.zui.opendeviceidlibrary.**{*;}
-keep class org.json.**{*;}
-keep public class com.netease.nis.sdkwrapper.Utils {public <methods>;}

################## 外广sdk 开始#####################

-keep class com.google.android.gms.** { *; }
-keep public class com.google.android.gms.ads.**{
   public *;
}

# For old ads classes
-keep public class com.google.ads.**{
   public *;
}

# For mediation
-keepattributes *Annotation*

# Other required classes for Google Play Services
# Read more at http://developer.android.com/google/play-services/setup.html
-keep class * extends java.util.ListResourceBundle {
   protected Object[][] getContents();
}

-keep public class com.google.android.gms.common.internal.safeparcel.SafeParcelable {
   public static final *** NULL;
}

-keepnames @com.google.android.gms.common.annotation.KeepName class *
-keepclassmembernames class * {
   @com.google.android.gms.common.annotation.KeepName *;
}

-keepnames class * implements android.os.Parcelable {
   public static final ** CREATOR;
}
-keep class com.google.android.gms.** { *; }

-keepclasseswithmembers,includedescriptorclasses class com.tencent.mmkv.** {
    native <methods>;
    long nativeHandle;
    private static *** onMMKVCRCCheckFail(***);
    private static *** onMMKVFileLengthError(***);
    private static *** mmkvLogImp(...);
    private static *** onContentChangedByOuterProcess(***);
}



#facebook
-keep class androidx.recyclerview.widget.RecyclerView { *; }
-keep class androidx.appcompat.app.AppCompatActivity { *; }
-keep class com.facebook.ads.BuildConfig { *; }

################## 外广sdk 结束#####################

#MAX SDK 处理开始

-keep class com.applovin.** {*;}
-keep class com.adcolony.** {*;}
-keep class com.inmobi.** {*;}
-keep class com.facebook.** {*;}
-keep class com.chartboost.** {*;}
-keep class com.google.android.gms.** {*;}
-keep class com.vungle.** {*;}
-keep class com.smaato.** {*;}
-keep class com.bytedance.** {*;}
-keep class com.mbridge.** {*;}
-keep class com.unity3d.** {*;}


#MAX SDK 处理结束

#Tradplus
-keep public class com.tradplus.** { *; }
-keep class com.tradplus.ads.** { *; }



-keep class java.io.** { *; }
-keep class java.net.** { *; }

-keep class androidx.camera.** { *; }

-keep interface com.mbridge.** {*; }
-dontwarn com.mbridge.**
-keepclassmembers class **.R$* { public static final int mbridge*; }

-keep public class com.mbridge.* extends androidx.** { *; }
-keep public class androidx.viewpager.widget.PagerAdapter{*;}
-keep public class androidx.viewpager.widget.ViewPager.OnPageChangeListener{*;}
-keep interface androidx.annotation.IntDef{*;}
-keep interface androidx.annotation.Nullable{*;}
-keep interface androidx.annotation.CheckResult{*;}
-keep interface androidx.annotation.NonNull{*;}
-keep public class androidx.fragment.app.Fragment{*;}
-keep public class androidx.core.content.FileProvider{*;}
-keep public class androidx.core.app.NotificationCompat{*;}
-keep public class androidx.appcompat.widget.AppCompatImageView {*;}
-keep public class androidx.recyclerview.*{*;}
-keep class com.mbridge.msdk.foundation.tools.FastKV{*;}
-keep class com.mbridge.msdk.foundation.tools.FastKV$Builder{*;}

-keep class com.appsflyer.** { *; }
-keep class kotlin.jvm.internal.** { *; }

-keep class kotlin.jvm.internal.** { *; }
-keep class com.walk.logcat.** { *;}

-keepattributes Signature
-if class *
-keepclassmembers class <1> {
    <init>(...);
    @com.google.gson.annotations.SerializedName <fields>;
}
-keep class com.google.gson.reflect.TypeToken { *; }
-keep class * extends com.google.gson.reflect.TypeToken
-keep class kotlinx.android.parcel.Parcelize { *; }
-keep class * implements android.os.Parcelable { *; }
-keepnames class * { @kotlinx.android.parcel.Parcelize *; }