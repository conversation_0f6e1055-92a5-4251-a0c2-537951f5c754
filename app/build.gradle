plugins {
    id("com.android.application")
    id("org.jetbrains.kotlin.android")
    id("kotlin-parcelize")
    id("kotlin-kapt")
}
apply from: "${rootProject.projectDir}/agent.gradle"

android {
    namespace = "com.example.newdc"
    compileSdk = 34

    defaultConfig {
        applicationId = "com.example.newdc"
        minSdk = 24
        targetSdk = 34
        versionCode = 1
        versionName = "1.0.0"

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"

        signingConfigs {
            release {
                storeFile file('release.jks')
                storePassword '123456'
                keyAlias = 'deviceTree'
                keyPassword '123456'
                v1SigningEnabled true
                v2SigningEnabled true
            }

        }

        buildConfigField "String","appsflyer_id","\"${appsflyers_id}\""
        buildConfigField "String","encrype_key","\"${encrype_key}\""
        buildConfigField "String","config_decrype_key","\"${config_decrype_key}\""
        buildConfigField "String","max_sdk_key","\"${max_sdk_key}\""
        buildConfigField "String","max_ad_inters_ley","\"${max_ad_inters_ley}\""
        buildConfigField "String","max_ad_open_key","\"${max_ad_open_key}\""
        buildConfigField "String","max_ad_native_key","\"${max_ad_native_key}\""
        buildConfigField "String","max_ad_banenr_key","\"${max_ad_banenr_key}\""
    }

    buildTypes {
        buildTypes {
            release {
                minifyEnabled true
                proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
                signingConfig signingConfigs.release
                //压缩对齐生成的apk包
                zipAlignEnabled true
                //移除无用的资源文件 想被忽略资源请在 keep.xml 中配置
                shrinkResources true
            }

            debug {
                debuggable true
                minifyEnabled false
                proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
                //signingConfig signingConfigs.debug
            }
        }
    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = "1.8"
    }

    buildFeatures {
        buildConfig = true
        dataBinding = true
    }
}

dependencies {
    implementation(fileTree("libs") { include("*.jar","*.aar") })

    implementation("androidx.core:core-ktx:1.10.1")
    implementation("androidx.appcompat:appcompat:1.6.1")
    implementation("com.google.android.material:material:1.10.0")
    implementation("androidx.constraintlayout:constraintlayout:2.1.4")
    testImplementation("junit:junit:4.13.2")
    androidTestImplementation("androidx.test.ext:junit:1.1.5")
    androidTestImplementation("androidx.test.espresso:espresso-core:3.5.1")

    implementation("androidx.fragment:fragment-ktx:1.6.0")

    //liveEventBus
    implementation("com.github.michaellee123:LiveEventBus:1.8.14")
    //MMKV
    implementation("com.tencent:mmkv:1.3.9")

    //glide
    implementation("com.github.bumptech.glide:glide:4.15.1")
    annotationProcessor("com.github.bumptech.glide:compiler:4.15.1")

    //banner
    implementation("com.github.zhpanvip:viewpagerindicator:1.2.2")
    implementation("com.github.zhpanvip:bannerviewpager:3.5.11")

    //lottie
    implementation("com.airbnb.android:lottie:5.2.0")

    implementation("com.github.PhilJay:MPAndroidChart:v3.1.0")
    //gson
    implementation("com.google.code.gson:gson:2.10.1")

    //retrofit
    implementation 'com.squareup.retrofit2:retrofit:2.11.0'
    implementation 'com.squareup.retrofit2:converter-gson:2.11.0'
    implementation 'com.squareup.okhttp3:logging-interceptor:4.9.0'

    // room
    implementation "androidx.room:room-runtime:2.5.1"
    implementation "androidx.room:room-ktx:2.5.1"
    kapt("androidx.room:room-compiler:2.5.1")


    //Firebase
    //noinspection BomWithoutPlatform
    implementation(platform("com.google.firebase:firebase-bom:32.8.0"))
    implementation("com.google.firebase:firebase-analytics-ktx")
    implementation("com.google.firebase:firebase-config-ktx")
    implementation("com.google.firebase:firebase-crashlytics-ktx")
    implementation("com.google.firebase:firebase-messaging-ktx")

    //google应用内评价
    implementation("com.google.android.play:review-ktx:2.0.1")

    //appsflyer
    implementation ("com.appsflyer:af-android-sdk:6.15.1")
    implementation ("com.android.installreferrer:installreferrer:2.2")

    //max
    implementation("com.applovin:applovin-sdk:+")

    //UMP
    implementation("com.google.android.ump:user-messaging-platform:3.1.0")
}