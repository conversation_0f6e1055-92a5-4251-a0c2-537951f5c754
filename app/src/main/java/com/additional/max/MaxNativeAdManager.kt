package com.additional.max

import android.content.Context
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import com.additional.AppsflyerTool
import com.additional.biservice.BiServiceClass
import com.additional.firebase.FirebaseEventPost
import com.applovin.mediation.MaxAd
import com.applovin.mediation.MaxError
import com.applovin.mediation.nativeAds.MaxNativeAdListener
import com.applovin.mediation.nativeAds.MaxNativeAdLoader
import com.applovin.mediation.nativeAds.MaxNativeAdView
import com.applovin.mediation.nativeAds.MaxNativeAdViewBinder
import com.example.newdc.BaseLifeCycle
import com.example.newdc.R
import com.example.newdc.modelutil.tool.ExtraTool.debug
import kotlin.math.abs

class MaxNativeAdManager(private val context: Context,private val adUnitKey: String = AdConfig.getKeyNative()) {
    private val Tag = "MaxNativeAdManager"
    private var _maxNativeAdView: MaxNativeAdView? = null
    private var _maxNativeAdLoader: MaxNativeAdLoader? = null
    private var _maxAd: MaxAd? = null
    private var _adStatusListener: AdStatusListener? = null
    private var _nativeContainer: FrameLayout? = null

    private var _flagAdLoaded = false
    private var _flagAdShowed = false
    private var _flagAdClicked = false
    private var _flagAdIsLoading = false
    private var _timeAdStartLoad = 0L

    fun adLoad(adContainer: FrameLayout){
        if (!AdConfig.getSwitchAdNative() || adUnitKey.isBlank()) {
            if (adUnitKey.isBlank()){
                debug(Tag,"native Key is blank")
                FirebaseEventPost.event("key_blank_native")
            }
            return
        }
        if (abs(System.currentTimeMillis() - _timeAdStartLoad) < 15 * 60 * 1000) {
            debug(Tag,"ad is loading now..")
            return
        }

        _nativeContainer = adContainer
        _flagAdIsLoading = true
        _timeAdStartLoad = System.currentTimeMillis()

        _maxNativeAdLoader = MaxNativeAdLoader(adUnitKey,context).apply {
            setNativeAdListener(object : MaxNativeAdListener(){
                override fun onNativeAdLoaded(p0: MaxNativeAdView?, p1: MaxAd) {
                    _flagAdLoaded = true
                    _flagAdIsLoading = false
                    if (_maxAd != null){
                        _maxNativeAdLoader?.destroy(_maxAd)
                    }
                    _maxAd = p1
                    _adStatusListener?.onAdLoaded(p1)
                    debug(Tag,"native ad is loaded")
                    FirebaseEventPost.event("value_native_loaded")
                    adShow()
                }

                override fun onNativeAdLoadFailed(p0: String, p1: MaxError) {
                    _adStatusListener?.onAdLoadFailed(p1)
                    _flagAdIsLoading = false
                    debug(Tag,"native ad failed to load ${p1.message}")
                    FirebaseEventPost.event("value_native_load_failed")
                }

                override fun onNativeAdClicked(p0: MaxAd) {
                    FirebaseEventPost.event("value_native_click")
                    BaseLifeCycle.splashFlag = true
                }

                override fun onNativeAdExpired(p0: MaxAd) {
                    debug(Tag,"native ad is expired")
                    _maxNativeAdLoader?.loadAd()
                    FirebaseEventPost.event("value_native_expired")
                }
            })

            setRevenueListener {
                FirebaseEventPost.event("value_native_impress")
                BiServiceClass().postValueEvent(it)
                FirebaseEventPost.uploadAdValueEvent(it)
                AppsflyerTool.postRoseValue(it)
                _flagAdShowed = true
            }
        }
        _maxNativeAdLoader?.loadAd()
        FirebaseEventPost.event("value_native_load")
    }

    fun adShow(){
        if (_maxNativeAdLoader != null && _maxAd != null && _flagAdLoaded && !_flagAdShowed){
            _maxNativeAdView = buildNativeAdView(context)
            _nativeContainer?.removeAllViews()
           removeViewParent(_maxNativeAdView)
            _maxNativeAdLoader!!.render(_maxNativeAdView,_maxAd)
            _nativeContainer?.addView(_maxNativeAdView)
            FirebaseEventPost.event("value_native_show")
        }
    }

    fun buildNativeAdView(context: Context): MaxNativeAdView {
        val viewBuilder = MaxNativeAdViewBinder.Builder(R.layout.layout_native_view)
            .setTitleTextViewId(R.id.title_text_view)
            .setBodyTextViewId(R.id.body_text_view)
            .setAdvertiserTextViewId(R.id.advertiser_textView)
            .setIconImageViewId(R.id.icon_image_view)
            .setMediaContentViewGroupId(R.id.media_view_container)
            .setCallToActionButtonId(R.id.btn_button)
            .setOptionsContentViewGroupId(R.id.ad_options_view)
            .build()

        return MaxNativeAdView(viewBuilder,context)
    }

    fun onDestroy(){
        if (_maxAd != null){
            _maxNativeAdLoader?.destroy(_maxAd)
            _maxAd = null
        }
        _maxNativeAdView = null
    }

    fun setOnAdListener(listener: AdStatusListener){
        _adStatusListener = listener
    }

    fun removeViewParent(view: View?) {
        if (view != null) {
            val parent = view.parent
            if (parent is ViewGroup) {
                parent.removeView(view)
            }
        }
    }
}