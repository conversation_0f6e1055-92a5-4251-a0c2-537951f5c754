package com.additional.max

import android.app.Activity
import com.additional.firebase.FirebaseEventPost
import com.example.newdc.BaseApplication
import com.google.android.ump.ConsentInformation
import com.google.android.ump.ConsentRequestParameters
import com.google.android.ump.UserMessagingPlatform
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import java.util.concurrent.atomic.AtomicBoolean

object UMPTool {
    private lateinit var consentInformation: ConsentInformation
    var UMPIsInit = AtomicBoolean(false)
    var MaxSdkInitialized = AtomicBoolean(false)
    private var isCanShowNext = true

    fun userMessagingRequest(activity: Activity, call: () -> Unit){
        isCanShowNext = true
//        val debugSettings = ConsentDebugSettings.Builder(activity)
//            .setDebugGeography(ConsentDebugSettings.DebugGeography.DEBUG_GEOGRAPHY_EEA)
//            .addTestDeviceHashedId("74EA6C9FDB60A333CFD5964965BF6774")
//            .build()

        val params = ConsentRequestParameters
            .Builder()
//            .setConsentDebugSettings(debugSettings)
            .build()

        consentInformation = UserMessagingPlatform.getConsentInformation(activity)
        consentInformation.requestConsentInfoUpdate(
            activity,
            params,
            {
                FirebaseEventPost.event("ucm_consent_suc")
                UserMessagingPlatform.loadAndShowConsentFormIfRequired(
                    activity
                ) { loadAndShowError ->
                    if (loadAndShowError != null) {
                        // Consent gathering failed.
                        FirebaseEventPost.event("ucm_consent_faild")
                        initProgress(call)
                    }else{
                        if (consentInformation.canRequestAds()){
                            FirebaseEventPost.event("ucm_consent_agree")
                        }
                        initProgress(call)
                    }
                    // Consent has been gathered.
                }
            },
            { requestConsentError ->
                // Consent gathering failed.
                FirebaseEventPost.event("ucm_request_faild")
                initProgress(call)
            })
        UMPIsInit.set(true)
    }

    private fun initProgress(onCallBack: () -> Unit){
        if (isCanShowNext){
            isCanShowNext = false
            onCallBack()
        }
        if (MaxSdkInitialized.get()) return
        GlobalScope.launch {
            AdConfig.initialMaxSDK(BaseApplication.mAppContext)
        }
    }
}