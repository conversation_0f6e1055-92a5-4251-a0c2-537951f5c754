package com.additional.max

import com.applovin.mediation.MaxAd
import com.applovin.mediation.MaxError
import com.applovin.mediation.ads.MaxAppOpenAd
import com.applovin.mediation.ads.MaxInterstitialAd

abstract class AdStatusListener {
    open fun onAdLoaded(ad: MaxAd){}
    open fun onAdLoadFailed(error: MaxError){}
    open fun onAdDisplayed(ad: MaxAd){}
    open fun onAdAdDisplayFail(error: MaxError){}
    open fun onImpression(){}
    open fun onAdClose(){}
    open fun onAdClicked(){}
}