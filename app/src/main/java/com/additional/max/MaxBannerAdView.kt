package com.additional.max

import android.app.Activity
import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.RelativeLayout
import com.additional.AppsflyerTool
import com.additional.biservice.BiServiceClass
import com.additional.firebase.FirebaseEventPost
import com.airbnb.lottie.LottieAnimationView
import com.applovin.mediation.MaxAd
import com.applovin.mediation.MaxAdFormat
import com.applovin.mediation.MaxAdViewAdListener
import com.applovin.mediation.MaxError
import com.applovin.mediation.ads.MaxAdView
import com.applovin.sdk.AppLovinSdkUtils
import com.example.newdc.BaseLifeCycle
import com.example.newdc.R
import com.example.newdc.modelutil.tool.ExtraTool.debug

class MaxBannerAdView: RelativeLayout {
    private val TAG = "MaxBannerAdView"
    private var viewContainer : RelativeLayout? = null
    private var bannerContainer: RelativeLayout? = null
    private var lottLoading: LottieAnimationView? = null
    private var adStatusListener: AdStatusListener? = null
    private var maxAdView: MaxAdView? = null
    private var adHasLoad = false


    constructor(context: Context): this(context, null)
    constructor(context: Context, attributeSet: AttributeSet?): this(context, attributeSet, 0)
    constructor(context: Context, attributeSet: AttributeSet?, defStyleAttr: Int) :super(context, attributeSet, defStyleAttr){
        val view = LayoutInflater.from(context).inflate(R.layout.layout_banner_view,null,true).apply {
            viewContainer = findViewById(R.id.banner_view)
            bannerContainer = findViewById(R.id.ad_contain)
            lottLoading = findViewById(R.id.lott_load)
        }
        val params = LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT)
        addView(view,params)
    }

    fun addBannerView(adUnitKey: String = AdConfig.getKeyBanner()){
        if (adUnitKey.isBlank()) {
            debug(TAG,"banner Key is blank")
            FirebaseEventPost.event("key_blank_banner")
            return
        }
        val width = ViewGroup.LayoutParams.MATCH_PARENT
        val heightDp = MaxAdFormat.BANNER.getAdaptiveSize(context as Activity).height
        val heightPx = AppLovinSdkUtils.dpToPx(context, heightDp)
        maxAdView = MaxAdView(adUnitKey,context).apply {
            setListener(object : MaxAdViewAdListener{
                override fun onAdLoaded(p0: MaxAd) {
                    FirebaseEventPost.event("value_banner_loaded")
                    debug(TAG,"value_banner_loaded")
                }

                override fun onAdDisplayed(p0: MaxAd) {
                    FirebaseEventPost.event("value_banner_display")
                    lottLoading?.visibility = View.GONE
                }

                override fun onAdHidden(p0: MaxAd) {
                }

                override fun onAdClicked(p0: MaxAd) {
                    FirebaseEventPost.event("value_banner_click")
                    BaseLifeCycle.splashFlag = true
                }

                override fun onAdLoadFailed(p0: String, p1: MaxError) {
                    FirebaseEventPost.event("value_banner_load_failed")
                    debug(TAG,"value_banner_load_failed ${p1.message}")
                    lottLoading?.visibility = View.VISIBLE
                }

                override fun onAdDisplayFailed(p0: MaxAd, p1: MaxError) {
                    FirebaseEventPost.event("value_banner_display_failed")
                    debug(TAG,"value_banner_display_failed ${p1.message}")
                    lottLoading?.visibility = View.VISIBLE
                }

                override fun onAdExpanded(p0: MaxAd) {
                }

                override fun onAdCollapsed(p0: MaxAd) {
                }
            })

            setAdReviewListener { s, maxAd ->
                FirebaseEventPost.event("value_banner_impress")
                BiServiceClass().postValueEvent(maxAd)
                FirebaseEventPost.uploadAdValueEvent(maxAd)
                AppsflyerTool.postRoseValue(maxAd)
            }
            layoutParams = FrameLayout.LayoutParams(width, heightPx)
            setExtraParameter("adaptive_banner", "true")
            debug(TAG,"add view banner")
        }
        viewContainer?.addView(maxAdView)
        load()
    }

    fun load() {
        if (AdConfig.getSwitchAdBanner() && maxAdView != null && !adHasLoad) {
            adHasLoad = true
            maxAdView?.loadAd()
            FirebaseEventPost.event("value_banner_load")
            debug(TAG,"ad banner load")
        }
    }

    fun resume() {
        maxAdView?.startAutoRefresh()
    }

    fun stop() {
        maxAdView?.setExtraParameter("allow_pause_auto_refresh_immediately", "true")
        maxAdView?.stopAutoRefresh()
    }

    fun destroy() {
        maxAdView?.destroy()
        bannerContainer?.removeView(maxAdView)
    }

    fun setAdListener(listener: AdStatusListener) {
        adStatusListener = listener
    }
}