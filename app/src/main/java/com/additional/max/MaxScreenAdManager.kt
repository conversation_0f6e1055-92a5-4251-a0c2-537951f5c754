package com.additional.max

import android.app.Activity
import android.content.Context
import android.os.Handler
import android.os.Looper
import androidx.lifecycle.LifecycleOwner
import com.additional.firebase.FirebaseEventPost
import com.applovin.mediation.MaxAd
import com.applovin.mediation.ads.MaxAppOpenAd
import com.applovin.mediation.ads.MaxInterstitialAd
import com.example.newdc.modelutil.tool.ExtraTool.countDownFlow
import kotlinx.coroutines.Job

class MaxScreenAdManager(private val isInterstitial :Boolean, private val onClose: ()->Unit = {}) {
    private var interstitialManager: MaxIntersAdManager? = null
    private var openAdManager: MaxOpenAdManager? = null
    private var adIsLoaded = false
    private var adIsShowed = false

    private var waitingJob: Job? = null

    companion object{
        fun loadAdCache(isInterstitial :Boolean,context: Context,activity: Activity){
            if(isInterstitial){
                MaxIntersAdManager.getIntersAdCache(context,AdConfig.getKeyInterstitial())
            }else{
                MaxOpenAdManager.getOpenAdCache(activity,AdConfig.getKeyOpen())
            }
        }
    }

    fun initAd() {
        if (isInterstitial) {
            interstitialManager = MaxIntersAdManager.getAdInstance(AdConfig.getKeyInterstitial())
            interstitialManager?.setOnAdListener(object :AdStatusListener(){
                override fun onAdLoaded(ad: MaxAd) {
                    super.onAdLoaded(ad)
                    adIsLoaded = true
                    adIsShowed = false
                }

                override fun onAdClose() {
                    super.onAdClose()
                    adDestroy()
                    onClose.invoke()
                }
                
                override fun onAdDisplayed(ad: MaxAd) {
                    super.onAdDisplayed(ad)
                    adIsShowed = true
                }
            })
        }else{
            openAdManager = MaxOpenAdManager.getAdInstance(AdConfig.getKeyOpen())
            openAdManager?.setOnAdListener(object :AdStatusListener(){
                override fun onAdLoaded(ad: MaxAd) {
                    super.onAdLoaded(ad)
                    adIsLoaded = true
                    adIsShowed = false
                }

                override fun onAdClose() {
                    super.onAdClose()
                    onClose.invoke()
                }

                override fun onAdDisplayed(ad: MaxAd) {
                    super.onAdDisplayed(ad)
                    adIsShowed = true
                }
            })
        }
    }

    fun adLoad(activity: Activity){
        if (isInterstitial) {
            interstitialManager?.adLoad(activity)
        }else{
            openAdManager?.adLoad(activity)
        }
    }

    fun startWaitingJob(lifecycleOwner: LifecycleOwner,start: () -> Unit = {}, next: () -> Unit = {}, end: () -> Unit = {},time: Int = -1){
        val outTime = if (time >= 0){
            time
        }else{
            if (isInterstitial) AdConfig.getIntersOutTime() else AdConfig.getOpenOutTime()
        }
        waitingJob = lifecycleOwner.countDownFlow(
            time = outTime,
            start = { start.invoke() },
            end = {
                Handler(Looper.getMainLooper()).postDelayed(
                    { if (!adIsShowed){ end.invoke() } },
                    300)
                  },
            next = {
                if (it >= 1){
                    if (adIsLoaded && !adIsShowed && if (isInterstitial) AdConfig.getAllowShowInterstitial() else AdConfig.getAllowShowOpen()){
                        next.invoke()
                    }
                }
            }
        )
    }

    fun showAd(activity: Activity): Boolean {
        if (isInterstitial){
            if (interstitialManager != null && adIsLoaded){
                FirebaseEventPost.event("value_inters_req_show")
                return interstitialManager!!.adShow(activity)
            }
        }else{
            if (openAdManager != null && adIsLoaded){
                FirebaseEventPost.event("value_open_req_show")
                return openAdManager!!.adShow()
            }
        }
        return false
    }

    fun adDestroy(){
        interstitialManager?.onDestroy()
    }

    fun openDestroy(){
        openAdManager?.onDestroy()
    }
}