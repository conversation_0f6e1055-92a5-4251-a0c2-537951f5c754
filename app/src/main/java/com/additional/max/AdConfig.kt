package com.additional.max

import android.content.Context
import com.additional.firebase.FirebaseEventPost
import com.applovin.sdk.AppLovinMediationProvider
import com.applovin.sdk.AppLovinSdk
import com.applovin.sdk.AppLovinSdkInitializationConfiguration
import com.example.newdc.BuildConfig
import com.example.newdc.modelutil.data.EventData
import com.example.newdc.modelutil.data.RemoteConfigClass
import com.example.newdc.modelutil.data.StoreKeyData
import com.example.newdc.modelutil.tool.ExtraTool.debug
import com.jeremyliao.liveeventbus.LiveEventBus
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import kotlinx.coroutines.withContext
import java.util.concurrent.CountDownLatch
import java.util.concurrent.TimeUnit
import java.util.concurrent.atomic.AtomicBoolean
import kotlin.coroutines.resume
import kotlin.math.abs

object AdConfig {
    const val AD_CACHE_INTERSTITIAL = 55 * 60 * 1000L
    const val AD_CACHE_APP_OPEN = 55 * 60 * 1000L

    //second
    const val AD_INTERS_RP_TIME = -1
    const val AD_INTERVAL_INTERSTITIAL = 60
    const val AD_INTERVAL_OPEN = 120
    const val AD_OUT_INTERSTITIAL = 8
    const val AD_OUT_OPEN = 8

    const val SWITCH_AD_OPEN = true
    const val SWITCH_AD_INTERS = true
    const val SWITCH_AD_NATIVE = true
    const val SWITCH_AD_BANNER = true
    const val SWITCH_UNIQUE_NATIVE = false
    const val SWITCH_UNIQUE_BANNER = false

    const val AD_KEY_INTERSTITIAL = BuildConfig.max_ad_inters_ley
    const val AD_KEY_OPEN = BuildConfig.max_ad_open_key
    const val AD_KEY_NATIVE = BuildConfig.max_ad_native_key
    const val AD_KEY_BANNER = BuildConfig.max_ad_banenr_key

    private var sdkIsInitialized = false
    private val mutex = Mutex()

    suspend fun initialMaxSDK(context: Context) = mutex.withLock{
        if (sdkIsInitialized || AppLovinSdk.getInstance(context).isInitialized ) return
        val initConfig = AppLovinSdkInitializationConfiguration.builder(getSDKKey(), context)
            .setMediationProvider(AppLovinMediationProvider.MAX)
            .build()
        FirebaseEventPost.event("max_start")
        debug("liyq","max sdk start")
        suspendCancellableCoroutine { cancellableContinuation ->
            AppLovinSdk.getInstance(context).initialize(initConfig) { sdkConfig ->
                debug("liyq","max sdk initialized")
                FirebaseEventPost.event("max_initialed")
                LiveEventBus.get<String>(EventData.EVENT_AD_INIT).post("")
                sdkIsInitialized = true
                cancellableContinuation.resume(Unit)
            }
        }
    }

    fun getSwitchAdOpen():Boolean = RemoteConfigClass.getAdParam("open")?.ad_switch ?: false

    fun getSwitchAdNative():Boolean = RemoteConfigClass.getAdParam("native")?.ad_switch ?: false

    fun getSwitchAdInters():Boolean = RemoteConfigClass.getAdParam("interstitial")?.ad_switch ?: false

    fun getSwitchAdBanner():Boolean = RemoteConfigClass.getAdParam("banner")?.ad_switch ?: false

    fun getSwitchUniqueNative():Boolean = RemoteConfigClass.getRemoteAdConfigData().unique_switch_native

    fun getSwitchUniqueBanner():Boolean = RemoteConfigClass.getRemoteAdConfigData().unique_switch_banner

    private fun getSDKKey() = RemoteConfigClass.getRemoteAdConfigData().sdk_key

    fun getKeyInterstitial(): String{
        val rpTime = RemoteConfigClass.getRemoteAdConfigData().inters_rp_time
        if (rpTime >= 0) {
            val rpKey = RemoteConfigClass.getAdParam("interstitial")?.ad_rp_key ?: ""
            if (abs(System.currentTimeMillis() - StoreKeyData.getLong(StoreKeyData.BASE_FIRST_ENTER_TIME)) > rpTime * 1000 * 60 && rpKey.isNotEmpty()){
                return rpKey
            }
        }
        return RemoteConfigClass.getAdParam("interstitial")?.ad_key ?: ""
    }

    fun getKeyOpen(): String{
        return RemoteConfigClass.getAdParam("open")?.ad_key ?: ""
    }

    fun getKeyNative(): String{
        return RemoteConfigClass.getAdParam("native")?.ad_key ?: ""
    }

    fun getKeyBanner(): String{
        return RemoteConfigClass.getAdParam("banner")?.ad_key ?: ""
    }

    fun getAllowShowInterstitial():Boolean{
        val interval = RemoteConfigClass.getAdParam("interstitial")?.ad_interval ?: AD_INTERVAL_INTERSTITIAL
        return abs(System.currentTimeMillis() - StoreKeyData.getLong(StoreKeyData.AD_INTERSTITIAL_LAST)) > interval * 1000
    }

    fun getAllowShowOpen() :Boolean{
        val interval = RemoteConfigClass.getAdParam("open")?.ad_interval ?: AD_INTERVAL_OPEN
        return abs(System.currentTimeMillis() - StoreKeyData.getLong(StoreKeyData.AD_OPEN_LAST) ) > interval * 1000
    }

    fun getIntersOutTime(): Int{
        return RemoteConfigClass.getAdParam("interstitial")?.out_time_interval ?: AD_OUT_INTERSTITIAL
    }

    fun getOpenOutTime(): Int{
        return RemoteConfigClass.getAdParam("open")?.out_time_interval ?: AD_OUT_OPEN
    }
}