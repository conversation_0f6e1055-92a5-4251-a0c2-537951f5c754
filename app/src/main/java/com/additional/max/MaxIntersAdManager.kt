package com.additional.max

import android.app.Activity
import android.content.Context
import com.additional.AppsflyerTool
import com.additional.biservice.BiServiceClass
import com.additional.firebase.FirebaseEventPost
import com.applovin.mediation.MaxAd
import com.applovin.mediation.MaxAdListener
import com.applovin.mediation.MaxError
import com.applovin.mediation.ads.MaxInterstitialAd
import com.example.newdc.BaseApplication
import com.example.newdc.BaseLifeCycle
import com.example.newdc.modelutil.data.StoreKeyData
import com.example.newdc.modelutil.tool.ExtraTool.debug
import kotlin.math.abs

class MaxIntersAdManager(private val adUnitKey: String) {
    private val TAG = "MaxIntersAdManager"
    private var _maxInterstitialAd: MaxInterstitialAd? = null
    private var _adStatusListener: AdStatusListener? = null
    private var _maxAd: MaxAd? = null

    private var _flagAdLoaded = false
    private var _flagAdShowed = false
    private var _flagAdClicked = false
    private var _flagAdIsLoading = false
    private var _timeAdLoaded = 0L
    private var _timeAdStartLoad = 0L


    companion object{
        var adMaxInstance: MaxIntersAdManager? = null
        fun getAdInstance(adUnitKey: String): MaxIntersAdManager{
            if (adMaxInstance == null){
                adMaxInstance = MaxIntersAdManager(adUnitKey)
            }
            return adMaxInstance!!
        }

        fun getIntersAdCache(context: Context,adUnitKey: String){
            getAdInstance(adUnitKey).adLoad(context)
        }
    }


    fun adLoad(context: Context){
        if (!AdConfig.getSwitchAdInters() || adUnitKey.isBlank()) {
            if (adUnitKey.isBlank()){
                debug(TAG,"inters Key is blank")
                FirebaseEventPost.event("key_blank_inters")
            }
            return
        }
        if (_flagAdIsLoading && abs(System.currentTimeMillis() - _timeAdStartLoad) < 15 * 1000 * 60){
            debug(TAG,"ad is loading now...")
            return
        }

        if (_maxAd != null && _maxInterstitialAd != null && _flagAdLoaded && !_flagAdShowed &&
            abs(System.currentTimeMillis() - _timeAdLoaded) < AdConfig.AD_CACHE_INTERSTITIAL){
            _adStatusListener?.onAdLoaded(_maxAd!!)
            debug(TAG,"ad inters has cache return")
            return
        }

        _flagAdIsLoading = true
        _timeAdStartLoad = System.currentTimeMillis()


        _maxInterstitialAd = MaxInterstitialAd(adUnitKey,context).apply {
            setListener(object : MaxAdListener{
                override fun onAdLoaded(p0: MaxAd) {
                    _adStatusListener?.onAdLoaded(p0)
                    _flagAdShowed = false
                    _flagAdIsLoading = false
                    _flagAdLoaded = true
                    _maxAd = p0
                    _timeAdLoaded = System.currentTimeMillis()
                    FirebaseEventPost.event("value_inters_loaded")
                }

                override fun onAdDisplayed(p0: MaxAd) {
                    _adStatusListener?.onAdDisplayed(p0)
                    _flagAdShowed = true
                    _flagAdLoaded = false
                    FirebaseEventPost.event("value_inters_display")
                    StoreKeyData.setLong(StoreKeyData.AD_INTERSTITIAL_LAST,System.currentTimeMillis())
                }

                override fun onAdHidden(p0: MaxAd) {
                    _adStatusListener?.onAdClose()
                }

                override fun onAdClicked(p0: MaxAd) {
                    _adStatusListener?.onAdClicked()
                    BaseLifeCycle.splashFlag = true
                    FirebaseEventPost.event("value_inters_click")
                }

                override fun onAdLoadFailed(p0: String, p1: MaxError) {
                    _adStatusListener?.onAdLoadFailed(p1)
                    FirebaseEventPost.event("value_inters_load_failed")
                }

                override fun onAdDisplayFailed(p0: MaxAd, p1: MaxError) {
                    _adStatusListener?.onAdAdDisplayFail(p1)
                    _flagAdIsLoading = false
                    FirebaseEventPost.event("value_inters_display_failed")
                }

            })

            setAdReviewListener { s, maxAd ->
                BiServiceClass().postValueEvent(maxAd)
                FirebaseEventPost.uploadAdValueEvent(maxAd)
                FirebaseEventPost.event("value_inters_impress")
                AppsflyerTool.postRoseValue(maxAd)
            }
        }

        _maxInterstitialAd?.loadAd()
        FirebaseEventPost.event("value_inters_load_all")
    }

    fun adShow(activity: Activity): Boolean{
        if (abs(System.currentTimeMillis() - _timeAdLoaded) > AdConfig.AD_CACHE_INTERSTITIAL){
            FirebaseEventPost.event("value_inters_show_fail_outtime")
        }else if (_maxInterstitialAd == null || _flagAdShowed){
            if (_flagAdShowed){
                FirebaseEventPost.event("value_inters_show_fail_had_show")
            }else{
                FirebaseEventPost.event("value_inters_show_fail_null")
            }
        }else if(_maxInterstitialAd?.isReady!!){
            FirebaseEventPost.event("value_inters_can_show")
            _maxInterstitialAd?.showAd(activity)
            return true
        }
        FirebaseEventPost.event("value_inters_show_fail")
        return false
    }

    fun onDestroy(){
        if (abs(System.currentTimeMillis() - _timeAdLoaded) > AdConfig.AD_CACHE_INTERSTITIAL || _flagAdShowed
            || (_flagAdIsLoading && abs(System.currentTimeMillis() - _timeAdStartLoad) > 15 * 1000)) {
            _maxInterstitialAd?.destroy()
            adMaxInstance = null
            getIntersAdCache(BaseApplication.mAppContext,AdConfig.getKeyInterstitial())
        }
    }

    fun setOnAdListener(listener: AdStatusListener){
        _adStatusListener = listener
    }

    fun getAdIsShow() = _flagAdShowed

}