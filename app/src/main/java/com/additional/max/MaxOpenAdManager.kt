package com.additional.max

import android.app.Activity
import android.content.Context
import com.additional.AppsflyerTool
import com.additional.biservice.BiServiceClass
import com.additional.firebase.FirebaseEventPost
import com.applovin.mediation.MaxAd
import com.applovin.mediation.MaxAdListener
import com.applovin.mediation.MaxError
import com.applovin.mediation.ads.MaxAppOpenAd
import com.example.newdc.BaseLifeCycle
import com.example.newdc.modelutil.data.StoreKeyData
import com.example.newdc.modelutil.tool.ExtraTool.debug
import kotlin.math.abs

class MaxOpenAdManager(private val adUnitKey: String) {
    private val TAG = "MaxOpenAdManager"
    private var _maxOpenAd: MaxAppOpenAd? = null
    private var _adStatusListener: AdStatusListener? = null
    private var _maxAd: MaxAd? = null

    private var _flagAdLoaded = false
    private var _flagAdShowed = false
    private var _flagAdClicked = false
    private var _flagAdIsLoading = false
    private var _timeAdLoaded = 0L
    private var _timeAdStartLoad = 0L


    companion object{
        var adMaxInstance: MaxOpenAdManager? = null
        fun getAdInstance(adUnitKey: String): MaxOpenAdManager{
            if (adMaxInstance == null){
                adMaxInstance = MaxOpenAdManager(adUnitKey)
            }
            return adMaxInstance!!
        }

        fun getOpenAdCache(activity: Activity, adUnitKey: String){
            getAdInstance(adUnitKey).adLoad(activity)
        }
    }


    fun adLoad(activity: Activity){
        if (!AdConfig.getSwitchAdOpen() || adUnitKey.isBlank()) {
            if (adUnitKey.isBlank()){
                debug(TAG,"open Key is blank")
                FirebaseEventPost.event("key_blank_open")
            }
            return
        }
        if (_flagAdIsLoading && abs(System.currentTimeMillis() - _timeAdStartLoad) < 15 * 1000 * 60){
            debug(TAG,"ad is loading now...")
            return
        }

        if (_maxAd != null && _maxOpenAd != null && _flagAdLoaded && !_flagAdShowed &&
            abs(System.currentTimeMillis() - _timeAdLoaded) < AdConfig.AD_CACHE_APP_OPEN){
            _adStatusListener?.onAdLoaded(_maxAd!!)
            debug(TAG,"ad open has cache return")
            return
        }

        _flagAdIsLoading = true
        _timeAdStartLoad = System.currentTimeMillis()


        _maxOpenAd = MaxAppOpenAd(adUnitKey,activity).apply {
            setListener(object : MaxAdListener{
                override fun onAdLoaded(p0: MaxAd) {
                    _adStatusListener?.onAdLoaded(p0)
                    _flagAdShowed = false
                    _flagAdIsLoading = false
                    _flagAdLoaded = true
                    _maxAd = p0
                    _timeAdLoaded = System.currentTimeMillis()
                    FirebaseEventPost.event("value_open_loaded")
                }

                override fun onAdDisplayed(p0: MaxAd) {
                    _adStatusListener?.onAdDisplayed(p0)
                    _flagAdShowed = true
                    _flagAdLoaded = false
                    StoreKeyData.setLong(StoreKeyData.AD_OPEN_LAST,System.currentTimeMillis())
                    FirebaseEventPost.event("value_open_display")
                }

                override fun onAdHidden(p0: MaxAd) {
                    _flagAdLoaded = false
                    _adStatusListener?.onAdClose()
                }

                override fun onAdClicked(p0: MaxAd) {
                    _adStatusListener?.onAdClicked()
                    FirebaseEventPost.event("value_open_click")
                    BaseLifeCycle.splashFlag = true
                }

                override fun onAdLoadFailed(p0: String, p1: MaxError) {
                    _adStatusListener?.onAdLoadFailed(p1)
                    _flagAdIsLoading = false
                    FirebaseEventPost.event("value_open_load_failed")
                }

                override fun onAdDisplayFailed(p0: MaxAd, p1: MaxError) {
                    _adStatusListener?.onAdAdDisplayFail(p1)
                    FirebaseEventPost.event("value_open_display_failed")
                }

            })

            setAdReviewListener { s, maxAd ->
                BiServiceClass().postValueEvent(maxAd)
                FirebaseEventPost.uploadAdValueEvent(maxAd)
                FirebaseEventPost.event("value_open_impress")
                AppsflyerTool.postRoseValue(maxAd)
            }
        }

        _maxOpenAd?.loadAd()
        FirebaseEventPost.event("value_open_load")
    }

    fun adShow(): Boolean{
        if (abs(System.currentTimeMillis() - _timeAdLoaded) > AdConfig.AD_CACHE_APP_OPEN){
            FirebaseEventPost.event("value_open_show_fail_outtime")
        }else if(_maxOpenAd == null || _flagAdShowed){
            if (_flagAdShowed){
                FirebaseEventPost.event("value_open_show_fail_had_show")
            }else{
                FirebaseEventPost.event("value_open_show_fail_outtime_null")
            }
        }else if (_maxOpenAd?.isReady!!){
            FirebaseEventPost.event("value_open_can_show")
            _maxOpenAd?.showAd()
            return true
        }
        FirebaseEventPost.event("value_open_show_fail")
        return false
    }

    fun onDestroy(){
        if (abs(System.currentTimeMillis() - _timeAdLoaded) > AdConfig.AD_CACHE_APP_OPEN || _flagAdShowed
            || (_flagAdIsLoading && abs(System.currentTimeMillis() - _timeAdStartLoad) > 15 * 1000)) {
            _maxOpenAd?.destroy()
            adMaxInstance = null
        }
    }

    fun setOnAdListener(listener: AdStatusListener){
        _adStatusListener = listener
    }

}