package com.additional.biservice

import android.app.Activity
import com.additional.biservice.basedata.NetEventDataBase
import com.additional.biservice.basedata.StartEventData
import com.additional.biservice.basedata.ValueEventData
import com.additional.firebase.FirebaseEventPost
import com.applovin.mediation.MaxAd
import com.example.newdc.BaseApplication
import com.example.newdc.modelutil.data.StoreKeyData
import com.example.newdc.modelutil.tool.EncodeTool
import com.example.newdc.modelutil.tool.ExtraTool.debug
import com.example.newdc.modelutil.tool.ExtraTool.isScreenOn
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import okhttp3.*
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.logging.HttpLoggingInterceptor
import org.json.JSONObject
import retrofit2.Call
import retrofit2.Response
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.io.IOException

class BiServiceClass {

    private val TAG = "BiServiceClass"

    fun getBiInstance(): NetInterface{
        val retrofit = Retrofit.Builder()
            .baseUrl(ParamConstants.BI_BASE_URL)
            .addConverterFactory(GsonConverterFactory.create(Gson().newBuilder().create()))
            .client(OkHttpClient.Builder().addInterceptor(PostInterceptor()).addInterceptor(HttpLoggingInterceptor().apply {
                level = HttpLoggingInterceptor.Level.BODY
            }).build())
            .build()

        return retrofit.create(NetInterface::class.java)
    }

    fun postStartEvent(eventName: String,activity: Activity?){
        val bodyMap = LinkedHashMap<String, Any>()
        val type = if (activity.toString().startsWith(BaseApplication.mAppContext.packageName)) 1 else 2
        bodyMap["event_name"] = eventName
        bodyMap["activity"] = if (eventName == ParamConstants.POST_EVENT_INSTALL) "" else activity.toString()
        bodyMap["type"] = type
        val requestBody = makePostBean(bodyMap)

        if (requestBody != null){
            getBiInstance().postStartEvent(requestBody).enqueue(object : retrofit2.Callback<ResponseBody>{
                override fun onResponse(call: Call<ResponseBody>, response: Response<ResponseBody>) {
                    if (response.isSuccessful){
                        if (eventName == ParamConstants.POST_EVENT_INSTALL){
                            StoreKeyData.setBoolean(StoreKeyData.BI_INSTALL_HAS,true)
                        }
                    }else{
                        if (eventName == ParamConstants.POST_EVENT_START){
                            NetEventDataBase.getInstance(BaseApplication.mAppContext).startEventDao().insertEvents(StartEventData(data = bodyMap))
                        }
                    }
                    debug(TAG, "postStartEvent data: ${bodyMap}")
                    debug(TAG, "postStartEvent success: ${response}")
                }

                override fun onFailure(call: Call<ResponseBody>, t: Throwable) {
                    if (eventName == ParamConstants.POST_EVENT_START){
                        NetEventDataBase.getInstance(BaseApplication.mAppContext).startEventDao().insertEvents(StartEventData(data = bodyMap))
                    }
                    debug(TAG, "postStartEvent fail: ${t.message}")
                }
            })
        }
    }

    fun postStartEventList(list: MutableList<HashMap<String, Any>>){
        if (list.isEmpty()) return
        val requestBody = makeListPostBean(list)
        if (requestBody != null){
            getBiInstance().postStartEvent(requestBody).enqueue(object : retrofit2.Callback<ResponseBody>{
                override fun onResponse(call: Call<ResponseBody>, response: Response<ResponseBody>) {
                    if (response.isSuccessful){
                        GlobalScope.launch { NetEventDataBase.getInstance(BaseApplication.mAppContext).startEventDao().deleteTab() }
                    }
                    debug(TAG, "postStartEventList data: ${list.toString()}")
                    debug(TAG, "postStartEventList success: ${response}")
                }

                override fun onFailure(call: Call<ResponseBody>, t: Throwable) {
                    debug(TAG, "postStartEventList fail: ${t.message}")
                }
            })
        }
    }

    fun postCreateEvent(){
        val bodyMap = LinkedHashMap<String, Any>()
        bodyMap["event_name"] = "s_load"
        val requestBody = makePostBean(bodyMap)
        if (requestBody != null){
            getBiInstance().postCreteEvent(requestBody).enqueue(object : retrofit2.Callback<ResponseBody>{
                override fun onResponse(call: Call<ResponseBody>, response: Response<ResponseBody>) {
                    debug(TAG, "postCreateEvent success: ${response}")
                    StoreKeyData.setBoolean(StoreKeyData.BI_RECORD_EVENT,true)
                }

                override fun onFailure(call: Call<ResponseBody>, t: Throwable) {
                    debug(TAG, "postCreateEvent fail: ${t.message}")
                }
            })
        }
    }

    fun postValueEvent(maxAd: MaxAd,adPlace: String = ""){
        val value = maxAd.revenue ?: 0f
        if (value == 0.0) return
        val bodyMap = LinkedHashMap<String,Any>()
        bodyMap["value"] = value
        bodyMap["currency"] = "USD"
        bodyMap["precision_type"] = maxAd.revenuePrecision ?: 0
        bodyMap["ad_network"] = maxAd.networkName ?: ""
        bodyMap["ad_type"] = maxAd.format.displayName ?: ""
        bodyMap["ad_placement"] = adPlace

        val requestBody = makePostBean(bodyMap)
        if (requestBody != null){
            getBiInstance().postValueEvent(requestBody).enqueue(object : retrofit2.Callback<ResponseBody>{
                override fun onResponse(call: Call<ResponseBody>, response: Response<ResponseBody>) {
                    if (response.isSuccessful){
                        debug(TAG,"postAdValue success: $response")
                    }else{
                        debug(TAG,"postAdValue no success: $response")
                        NetEventDataBase.getInstance(BaseApplication.mAppContext).valueEventDao().insertEvents(ValueEventData(data = bodyMap))
                    }
                }

                override fun onFailure(call: Call<ResponseBody>, t: Throwable) {
                    debug(TAG,"postAdValue error: ${t.message}")
                    NetEventDataBase.getInstance(BaseApplication.mAppContext).valueEventDao().insertEvents(ValueEventData(data = bodyMap))
                }
            })
        }
    }

    fun postValueEventList(list: MutableList<HashMap<String, Any>>){
        if (list.isEmpty()) return
        val requestBody = makeListPostBean(list)
        if (requestBody != null){
            getBiInstance().postValueEvent(requestBody).enqueue(object : retrofit2.Callback<ResponseBody>{
                override fun onResponse(call: Call<ResponseBody>, response: Response<ResponseBody>) {
                    if (response.isSuccessful){
                        GlobalScope.launch { NetEventDataBase.getInstance(BaseApplication.mAppContext).valueEventDao().deleteTab() }
                    }
                    debug(TAG, "postValueEventList data: $list")
                    debug(TAG, "postValueEventList success: ${response}")
                }

                override fun onFailure(call: Call<ResponseBody>, t: Throwable) {
                    debug(TAG, "postValueEventList fail: ${t.message}")
                }
            })
        }
    }

    fun postRemoteConfig(success: (json: String?) -> Unit, fail: () -> Unit){
        val bodyMap = LinkedHashMap<String, Any>()
        bodyMap["language"] = ParamConstants.getLocalLanguage()
        bodyMap["version_code"] = ParamConstants.getVersionCode(BaseApplication.mAppContext)
        val requestBody = makePostBean(bodyMap)

        FirebaseEventPost.event("request_remote_config")
        if (requestBody != null){
            getBiInstance().postRemoteConfig(requestBody).enqueue(object : retrofit2.Callback<ResponseBody>{
                override fun onResponse(call: Call<ResponseBody>, response: Response<ResponseBody>) {
                    if (response.isSuccessful){
                        debug(TAG, "postRemoteConfig success}")
                        success.invoke(response.body()?.string())
                    }
                }

                override fun onFailure(call: Call<ResponseBody>, t: Throwable) {
                    debug(TAG, "postRemoteConfig fail: ${t.message}")
                    if (t.message?.contains("Failed to connect") == true){
                        if (BaseApplication.mAppContext.isScreenOn()){
                            FirebaseEventPost.event("get_remote_fail_off")
                        }else{
                            FirebaseEventPost.event("get_remote_fail_no_net")
                        }
                    }else{
                        FirebaseEventPost.event("get_remote_fail")
                    }
                    fail.invoke()
                }
            })
        }
    }



    private fun makePostBean(bodyMap: HashMap<String, Any>): RequestBody? {
        val gson = GsonBuilder().disableHtmlEscaping().create()
        val json = gson.toJson(bodyMap)
        val finalJson = EncodeTool.encrypt(EncodeTool.BASE_KEY, json)
        val jsonObject = JSONObject().apply {
            put("dd", finalJson)
        }

        return try {
            RequestBody.create("application/json".toMediaTypeOrNull(), jsonObject.toString())
        } catch (e: Exception) {
            null
        }
    }

    private fun makeListPostBean(list: MutableList<HashMap<String, Any>>): RequestBody? {
        val gson = GsonBuilder().disableHtmlEscaping().create()
        val json = gson.toJson(list)
        val finalJson = EncodeTool.encrypt(EncodeTool.BASE_KEY, json)
        val jsonObject = JSONObject().apply {
            put("dd", finalJson)
        }
        return try {
            RequestBody.create("application/json".toMediaTypeOrNull(), jsonObject.toString())
        } catch (e: Exception) {
            null
        }
    }



    inner class PostInterceptor : Interceptor {
        @Throws(IOException::class)
        override fun intercept(chain: Interceptor.Chain): okhttp3.Response {
            val request = chain.request().newBuilder()
                .addHeader("api-version", "1")
                .addHeader("dtree-name", "com.itechnosolut.devicetree")
                .addHeader("dtree", ParamConstants.getBiHeader(BaseApplication.mAppContext))
                .build()
            return chain.proceed(request)
        }
    }
}