package com.additional.biservice

import okhttp3.RequestBody
import okhttp3.ResponseBody
import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.POST

interface NetInterface {
    //post event
    @POST("dtree/log")
    fun postStartEvent(@Body requestBody: RequestBody): Call<ResponseBody>

    //post adValue
    @POST("dtree/income")
    fun postValueEvent(@Body requestBody: RequestBody): Call<ResponseBody>

    //get remote params
    @POST("dtree/user_config")
    fun postRemoteConfig(@Body requestBody: RequestBody): Call<ResponseBody>

    //post createEvent
    @POST("dtree/report")
    fun postCreteEvent(@Body requestBody: RequestBody): Call<ResponseBody>
}