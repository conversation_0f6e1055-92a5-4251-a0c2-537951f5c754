package com.additional.biservice.basedata

import androidx.room.*

@Dao
interface StartEventDao {

    @Query("SELECT * FROM startEvent")
    fun getEvents(): MutableList<StartEventData>

    @Query("DELETE FROM startEvent")
    fun deleteTab()

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertEvents(data: StartEventData):Long

    @Update
    fun updateEvents(data: StartEventData):Int

    @Delete
    fun deleteEvent(data: StartEventData):Int
}