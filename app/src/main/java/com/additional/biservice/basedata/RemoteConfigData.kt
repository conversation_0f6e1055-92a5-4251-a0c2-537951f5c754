package com.additional.biservice.basedata

import android.os.Parcelable
import com.additional.max.AdConfig
import com.example.newdc.BuildConfig
import com.example.newdc.service.notify.NotifyConfig
import kotlinx.android.parcel.Parcelize

@Parcelize
data class RemoteConfigData(
    var config_notify_content: String? = null,
    var config_notify_param: String? = null,
    var config_ad: String? = null
):Parcelable

@Parcelize
data class RemoteNotifyData(
    val first_show_interval: Int = NotifyConfig.NOTIFY_POST_FIRST_INTERVAL,
    val normal_show_interval: Int = NotifyConfig.NOTIFY_POST_LAST_INTERVAL,
    val post_show_max: Int = NotifyConfig.NOTIFY_MAX_SHOW,
    val shot_times: Int = NotifyConfig.NOTIFY_SHOT_TIMES,
    val slide_times: Int = NotifyConfig.NOTIFY_RESIDENT_INTERVAL,
    val close_times: Int = NotifyConfig.NOTIFY_CANCEL_INTERVAL
):Parcelable

@Parcelize
data class RemoteAdData(
    val sdk_key: String = BuildConfig.max_sdk_key,
    val unique_switch_native: Boolean = AdConfig.SWITCH_UNIQUE_NATIVE,
    val unique_switch_banner: Boolean = AdConfig.SWITCH_UNIQUE_BANNER,
    val inters_rp_time: Int = AdConfig.AD_INTERS_RP_TIME,
    val type_param: List<AdParamData> = arrayListOf()
):Parcelable

@Parcelize
data class AdParamData(
    val ad_type: String = "",
    val ad_switch: Boolean = true,
    val ad_key: String = "",
    val ad_rp_key: String = "",
    val ad_interval: Int = 0,
    val out_time_interval: Int = 0
):Parcelable{
    companion object{
        fun getDefault(): List<AdParamData>{
            return arrayListOf<AdParamData>().apply {
                add(AdParamData("interstitial",AdConfig.SWITCH_AD_INTERS,AdConfig.AD_KEY_INTERSTITIAL,"",AdConfig.AD_INTERVAL_INTERSTITIAL,AdConfig.AD_OUT_INTERSTITIAL))
                add(AdParamData("open",AdConfig.SWITCH_AD_OPEN,AdConfig.AD_KEY_OPEN,"",AdConfig.AD_INTERVAL_OPEN,AdConfig.AD_OUT_OPEN))
                add(AdParamData("native",AdConfig.SWITCH_AD_NATIVE,AdConfig.AD_KEY_NATIVE,"",0,0))
                add(AdParamData("banner",AdConfig.SWITCH_AD_BANNER,AdConfig.AD_KEY_BANNER,"",0,0))
            }
        }
    }
}

