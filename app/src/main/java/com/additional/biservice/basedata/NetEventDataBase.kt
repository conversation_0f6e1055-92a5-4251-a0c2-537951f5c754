package com.additional.biservice.basedata

import android.content.Context
import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase

private const val DATABASE_VERSION = 1
private const val TABLE_NAME = "bi_table.db"
@Database(entities = [StartEventData::class, ValueEventData::class],version = DATABASE_VERSION,exportSchema = false)
abstract class NetEventDataBase : RoomDatabase() {
    abstract fun startEventDao(): StartEventDao
    abstract fun valueEventDao(): ValueEventDao

    companion object{
        private var instance: NetEventDataBase? = null
        fun getInstance(context: Context): NetEventDataBase {
            if(instance == null){
                synchronized(NetEventDataBase::class){
                    instance = Room.databaseBuilder(context, NetEventDataBase::class.java, TABLE_NAME)
                        .allowMainThreadQueries()
                        .build()
                }
            }
            return instance !!
        }
    }
}