package com.additional.biservice.basedata

import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.TypeConverter
import androidx.room.TypeConverters
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import java.io.Serializable

@TypeConverters(HashMapConverter::class)
@Entity(tableName = "startEvent")
data class StartEventData(
    @PrimaryKey(autoGenerate = true)
    var id: Long = 0,
    val data: HashMap<String,Any>
): Serializable

@TypeConverters(HashMapConverter::class)
@Entity(tableName = "valueEvent")
data class ValueEventData(
    @PrimaryKey(autoGenerate = true)
    var id: Long = 0,
    val data: HashMap<String,Any>
): Serializable

class HashMapConverter {
    @TypeConverter
    fun fromHashMap(value: HashMap<String, Any>): String {
        return Gson().toJson(value)
    }

    @TypeConverter
    fun toHashMap(value: String): HashMap<String, Any> {
        val type = object : TypeToken<HashMap<String, Any>>() {}.type
        return Gson().fromJson(value, type)
    }
}