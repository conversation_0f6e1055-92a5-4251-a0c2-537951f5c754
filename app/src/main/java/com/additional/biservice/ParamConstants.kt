package com.additional.biservice

import android.content.Context
import android.os.Build
import android.provider.Settings
import com.example.newdc.BaseApplication
import com.example.newdc.modelutil.data.StoreKeyData
import com.example.newdc.modelutil.tool.EncodeTool
import com.google.android.gms.ads.identifier.AdvertisingIdClient
import com.google.gson.Gson
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import java.util.*

object ParamConstants {
    const val BI_BASE_URL = "http://47.253.80.156/api/"


    const val HEAD_LANGUAGE = "language"
    const val HEAD_VERSION_NAME = "version_name"
    const val HEAD_VERSION_CODE = "version_code"
    const val HEAD_APPSFLYER_ID = "appsflyer_id"
    const val HEAD_APP_INSTANCE_ID = "app_instance_id"
    const val HEAD_PACKAGE_ID = "package_id"
    const val HEAD_USER_ID = "user_id"

    const val POST_EVENT_START = "on_start"
    const val POST_EVENT_INSTALL = "install"


    fun getBiHeader(context: Context): String{
        val userId = getUserId()
        if (userId.isBlank()) return ""

        val headMap = hashMapOf<String,Any>().apply {
            put(HEAD_PACKAGE_ID,"com.itechnosolut.devicetree")
            put(HEAD_APPSFLYER_ID,StoreKeyData.getString(StoreKeyData.BI_MMP_ID) ?: "")
            put(HEAD_APP_INSTANCE_ID, StoreKeyData.getString(StoreKeyData.BI_INSTANCE_ID) ?: "")
            put(HEAD_USER_ID, getUserId())
            put(HEAD_LANGUAGE, getLocalLanguage())
            put(HEAD_VERSION_NAME,getVersionName(context))
            put(HEAD_VERSION_CODE, getVersionCode(context))
        }
        return EncodeTool.encrypt(EncodeTool.BASE_KEY,Gson().toJson(headMap).toString())
    }


    private fun getVersionName(context: Context):String{
        try {
            val packageInfo = context.packageManager.getPackageInfo(context.packageName,0)
            return packageInfo.versionName
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return ""
    }

    fun getVersionCode(context: Context):Int{
        try {
            val packageInfo = context.packageManager.getPackageInfo(context.packageName,0)
            return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) packageInfo.longVersionCode.toInt()
            else packageInfo.versionCode
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return 0
    }

    fun getLocalLanguage(): String{
        return Locale.getDefault().toLanguageTag()
    }

    suspend fun getGaId(): String{
        try {
            val adInfo = AdvertisingIdClient.getAdvertisingIdInfo(BaseApplication.mAppContext)
            val advertisingId = adInfo.id
            if (advertisingId != null) {
                return advertisingId
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return ""
    }

    private fun getAndroidId():String{
        return try {
            Settings.System.getString(BaseApplication.mAppContext.contentResolver, Settings.Secure.ANDROID_ID)
        }catch (_: Exception){
            ""
        }
    }

    fun findUserId(){
        if (getUserId().isNotBlank()) return
        GlobalScope.launch {
            val gaId = getGaId()
            val androidId = getAndroidId()
            val mmpId = StoreKeyData.getString(StoreKeyData.BI_MMP_ID)
            var tempGaId = ""
            gaId.forEach { if (it.isLetterOrDigit()) tempGaId += it }
            if (gaId.isNotBlank() && !tempGaId.matches(Regex("^0+$"))){
                StoreKeyData.setString(StoreKeyData.BI_USER_ID,gaId)
            }else if (androidId.isNotBlank() && !androidId.matches(Regex("^0+$"))){
                StoreKeyData.setString(StoreKeyData.BI_USER_ID,androidId)
            }else if (!mmpId.isNullOrBlank()){
                StoreKeyData.setString(StoreKeyData.BI_USER_ID,mmpId)
            }else{
                val id = getRandomUserId(16)
                StoreKeyData.setString(StoreKeyData.BI_USER_ID,id)
            }
            StoreKeyData.setLong(StoreKeyData.BI_GET_USER_TIME,System.currentTimeMillis())
        }
    }

    private fun getUserId(): String{
        return StoreKeyData.getString(StoreKeyData.BI_USER_ID) ?: ""
    }


    private fun getRandomUserId(size: Int): String {
        val characters = "abcdefghijklmnopqrstuvwxyz0123456789"
        val random = Random(System.currentTimeMillis())

        val sb = StringBuilder(size)
        repeat(size) {
            val randomIndex = random.nextInt(characters.length)
            val randomChar = characters[randomIndex]
            sb.append(randomChar)
        }

        return sb.toString()
    }
}