package com.additional.firebase

import android.os.Bundle
import com.applovin.mediation.MaxAd
import com.example.newdc.BaseApplication
import com.example.newdc.BuildConfig
import com.google.firebase.analytics.FirebaseAnalytics
import com.example.newdc.modelutil.data.StoreKeyData

object FirebaseEventPost {
    private var firebaseAnalyticsInstance: FirebaseAnalytics? = null

    private fun getInstance(): FirebaseAnalytics {
        if (firebaseAnalyticsInstance == null) {
            firebaseAnalyticsInstance = FirebaseAnalytics.getInstance(BaseApplication.mAppContext)
        }
        return firebaseAnalyticsInstance!!
    }

    fun event(key: String?, value: Bundle? = null) {
        if (BuildConfig.DEBUG) return
        if (value != null) {
            getInstance().logEvent(key!!, value)
        } else {
            getInstance().logEvent(key!!, Bundle())
        }
    }

    private fun postMaxRevenue(ad: MaxAd?) {
        if (BuildConfig.DEBUG || ad == null) return
        val bundle = Bundle()
        bundle.putDouble(FirebaseAnalytics.Param.VALUE, ad.revenue)
        bundle.putString(FirebaseAnalytics.Param.CURRENCY, "USD") //The revenue
        bundle.putString("bdNetwork", ad.networkName)
        bundle.putString("bdFormat", ad.format.label)
        event("Ad_Impression_revenue", bundle)
    }

    private fun postTotalRevenue(ad: MaxAd?){
        val adValue = ad?.revenue
        if (BuildConfig.DEBUG || adValue == null) return
        val currentImpressionRevenue = adValue
        val previousTaichiTroasCache = StoreKeyData.getFloat(StoreKeyData.AD_TOTAL_VALUE)
        val currentTaichiTroasCache = currentImpressionRevenue + previousTaichiTroasCache
        if (currentTaichiTroasCache >= 0.01){
            val bundle = Bundle()
            bundle.putDouble(FirebaseAnalytics.Param.VALUE, currentTaichiTroasCache)
            bundle.putString(FirebaseAnalytics.Param.CURRENCY, "USD")
            event("Total_Ads_Revenue_001",bundle)
            StoreKeyData.setFloat(StoreKeyData.AD_TOTAL_VALUE,0f)
        }else{
            StoreKeyData.setFloat(StoreKeyData.AD_TOTAL_VALUE,currentTaichiTroasCache.toFloat())
        }
    }

    private fun postAdImpression(maxAd: MaxAd?) {
        if (BuildConfig.DEBUG) return
        val bundle = Bundle()
        maxAd?.let {bundle.putString(FirebaseAnalytics.Param.AD_PLATFORM, "appLovin")
            bundle.putString(FirebaseAnalytics.Param.AD_UNIT_NAME, maxAd.adUnitId)
            bundle.putString(FirebaseAnalytics.Param.AD_FORMAT, maxAd.format.displayName)
            bundle.putString(FirebaseAnalytics.Param.AD_SOURCE, maxAd.networkName)
            bundle.putDouble(FirebaseAnalytics.Param.VALUE, maxAd.revenue)
            bundle.putString(FirebaseAnalytics.Param.CURRENCY, "USD") // All Applovin revenue is sent in USD
        }
        getInstance()?.logEvent(FirebaseAnalytics.Event.AD_IMPRESSION,bundle)
    }

    fun uploadAdValueEvent(maxAd: MaxAd?){
        postMaxRevenue(maxAd)
        postTotalRevenue(maxAd)
        postAdImpression(maxAd)
    }
}