package com.additional.firebase

import android.annotation.SuppressLint
import android.content.Context
import com.additional.biservice.basedata.RemoteAdData
import com.additional.biservice.basedata.RemoteNotifyData
import com.example.newdc.modelutil.data.NotificationData
import com.example.newdc.modelutil.data.StoreKeyData
import com.example.newdc.modelutil.tool.ExtraTool.debug
import com.google.android.gms.tasks.OnCompleteListener
import com.google.firebase.FirebaseApp
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.messaging.FirebaseMessaging
import com.google.firebase.remoteconfig.FirebaseRemoteConfig
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import kotlin.math.abs

object InstanceTool {
    private const val SUBSCRIBE_TOPIC = "deviceTreeType"
    const val FETCH_REMOTE_TIME = 60 * 60 * 1000
    private const val TAG = "InstanceTool"
    private const val REMOTE_CONFIG_NOTIFY_PARAM = "config_notify_param"
    private const val REMOTE_CONFIG_NOTIFY_CONTENT = "config_notify_content"
    private const val REMOTE_CONFIG_AD_CONFIG = "config_notify_ad_config"

    @SuppressLint("StaticFieldLeak")
    private var firebaseRemoteConfig: FirebaseRemoteConfig? = null

    fun getInstance(): FirebaseRemoteConfig?{
        if (firebaseRemoteConfig == null){
            firebaseRemoteConfig = FirebaseRemoteConfig.getInstance()
        }
        return firebaseRemoteConfig
    }

    fun initAnalyticsInstance(context: Context){
        FirebaseApp.initializeApp(context)
        fetchRemoteConfig()
        if (StoreKeyData.getString(StoreKeyData.BI_INSTANCE_ID).isNullOrEmpty()){
            FirebaseAnalytics.getInstance(context).appInstanceId.addOnCompleteListener { task ->
                if (task.isSuccessful) {
                    val appInstanceId = task.result
                    StoreKeyData.setString(StoreKeyData.BI_INSTANCE_ID, appInstanceId)
                    debug(TAG, "AppInstance ID: $appInstanceId")
                } else {
                    debug(TAG, "Unable to get AppInstance ID ${task.exception}")
                }
            }
        }

    }

    fun setSubscribe(context: Context) {
        FirebaseEventPost.event("fcm_start_sub")
        FirebaseMessaging.getInstance().subscribeToTopic(SUBSCRIBE_TOPIC)
            .addOnCompleteListener { task ->
                val msg = if (task.isSuccessful) {
                        FirebaseEventPost.event("fcm_sub_success")
                        "Subscribed"
                    }else{
                    FirebaseEventPost.event("fcm_sub_failed")
                    "Subscribe failed"
                    }
                debug(TAG, "Firebase subscribeToTopic $msg")
            }
        FirebaseMessaging.getInstance().token.addOnCompleteListener(OnCompleteListener { task ->
            if (!task.isSuccessful) {
                debug("FCMServiceManager", "Fetching FCM registration token failed : ${task.exception}" )
                return@OnCompleteListener
            }
            debug("FCMServiceManager", "get token: ${task.result}")
        })
    }

    @Synchronized
    fun fetchRemoteConfig() {
        if (abs(System.currentTimeMillis() - StoreKeyData.getLong(StoreKeyData.FB_CONFIG_FETCH_LAST)) < FETCH_REMOTE_TIME) return
        debug(TAG, "Firebase remote start receive")
        getInstance()?.fetchAndActivate()?.addOnCompleteListener {
            if (it.isSuccessful){
                if (it.result){
                    debug(TAG, "Firebase remote data receive success")
                    StoreKeyData.setLong(StoreKeyData.FB_CONFIG_FETCH_LAST,System.currentTimeMillis())
                }
            }
        }
    }

    fun getRemoteNotifyParam(): RemoteNotifyData?{
        var data: RemoteNotifyData? = null
        val notifyData = getInstance()?.getString(REMOTE_CONFIG_NOTIFY_PARAM)
        if (notifyData != null){
            try {
                data = Gson().fromJson(notifyData, RemoteNotifyData::class.java)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
        return data
    }

    fun getRemoteContent(): MutableList<NotificationData>?{
        var data: MutableList<NotificationData>? = null
        val notifyData = getInstance()?.getString(REMOTE_CONFIG_NOTIFY_CONTENT)
        if (notifyData != null){
            try {
                val notificationType = object : TypeToken<MutableList<NotificationData>>() {}.type
                data = Gson().fromJson(notifyData, notificationType)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
        return data
    }

    fun getRemoteAdData(): RemoteAdData?{
        var data: RemoteAdData? = null
        val adData = getInstance()?.getString(REMOTE_CONFIG_AD_CONFIG)
        if (adData != null){
            try {
                data = Gson().fromJson(adData, RemoteAdData::class.java)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
        return data
    }

}