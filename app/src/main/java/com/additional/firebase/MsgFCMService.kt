package com.additional.firebase

import android.os.Bundle
import com.example.newdc.modelutil.tool.ExtraTool.debug
import com.example.newdc.service.notify.PostShowNotification
import com.google.firebase.messaging.FirebaseMessagingService
import com.google.firebase.messaging.RemoteMessage
import android.os.Handler
import android.os.Looper
import com.example.newdc.modelutil.data.RemoteConfigClass
import com.example.newdc.modelutil.data.StoreKeyData
import com.example.newdc.modelutil.tool.ExtraTool.isLocked
import com.example.newdc.modelutil.tool.ExtraTool.isScreenOn
import com.example.newdc.service.notify.TopConstantService
import kotlin.math.abs

class MsgFCMService: FirebaseMessagingService() {
    var recordTime = 0L

    override fun onMessageReceived(message: RemoteMessage) {
        super.onMessageReceived(message)
        val days = (abs(System.currentTimeMillis() - StoreKeyData.getLong(StoreKeyData.BASE_FIRST_ENTER_TIME)) % 24 * 60 * 60 * 1000).toInt()
        val bundle = Bundle().apply { putInt("fcm_days",days + 1) }
        FirebaseEventPost.event("receive_fcm_msg",bundle)
        debug("MsgFCMService","receive fcm")
        Handler(Looper.getMainLooper()).postDelayed(
            {RemoteConfigClass.getRemoteConfig()}, (0..120).random() * 1000L
        )
        if (abs(System.currentTimeMillis() - recordTime) > 60 * 1000) {
            if (StoreKeyData.recordTime()){
                StoreKeyData.restTodayConfig()
            }
            recordTime = System.currentTimeMillis()
            PostShowNotification.requestNotification(this)
            if (this.isScreenOn() && !this.isLocked()){
                TopConstantService.startConstantNotify(this)
            }
        }
    }
}