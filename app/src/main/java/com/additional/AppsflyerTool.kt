package com.additional

import android.content.Context
import com.additional.firebase.FirebaseEventPost
import com.applovin.mediation.MaxAd
import com.appsflyer.AFAdRevenueData
import com.appsflyer.AdRevenueScheme
import com.appsflyer.AppsFlyerLib
import com.appsflyer.MediationNetwork
import com.example.newdc.BuildConfig
import com.example.newdc.modelutil.data.StoreKeyData


object AppsflyerTool {
    private const val DEV_KEY = BuildConfig.appsflyer_id

    fun initAppsflyer(context: Context) {
        FirebaseEventPost.event("af_init")
        AppsFlyerLib.getInstance().init(DEV_KEY, null, context)
        AppsFlyerLib.getInstance().start(context)
        val appId = AppsFlyerLib.getInstance().getAppsFlyerUID(context) ?: ""
        if (appId.isNotEmpty()) {
            StoreKeyData.setString(StoreKeyData.BI_MMP_ID,appId)
        }
    }

    fun postRoseValue(max: MaxAd){
        val appsflyer = AppsFlyerLib.getInstance()
        val adRevenueData = AFAdRevenueData(
            max.networkName,  // monetizationNetwork
            MediationNetwork.APPLOVIN_MAX,  // mediationNetwork
            "USD",  // currencyIso4217Code
            max.revenue // revenue
        )

        val additionalParameters: MutableMap<String, Any> = HashMap()
        additionalParameters[AdRevenueScheme.COUNTRY] = "US"
        additionalParameters[AdRevenueScheme.AD_UNIT] = max.adUnitId ?: ""
        additionalParameters[AdRevenueScheme.AD_TYPE] = max.format.displayName ?: ""
        additionalParameters[AdRevenueScheme.PLACEMENT] = max.placement ?: ""

        appsflyer.logAdRevenue(adRevenueData, additionalParameters)
    }
}