package com.example.newdc

import android.app.Application
import android.content.Context
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.util.Log
import com.additional.AppsflyerTool
import com.additional.biservice.BiServiceClass
import com.additional.biservice.ParamConstants
import com.additional.firebase.InstanceTool
import com.additional.max.AdConfig
import com.example.newdc.modelutil.data.RemoteConfigClass
import com.example.newdc.modelutil.data.StoreKeyData
import com.example.newdc.modelutil.tool.PermissionTool
import com.example.newdc.service.notify.NotifyReceive
import com.example.newdc.service.notify.TopConstantService
import com.example.newdc.service.receiver.TickTimeReceiver
import com.tencent.mmkv.MMKV
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch

class BaseApplication: Application() {
    companion object{
        lateinit var mAppContext: Context
    }
    override fun onCreate() {
        super.onCreate()
        mAppContext = this
        registerActivityLifecycleCallbacks(BaseLifeCycle())
        MMKV.initialize(this)
//        InstanceTool.initAnalyticsInstance(this)
//        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.TIRAMISU || PermissionTool.checkPostNotifyPermission(this)) {
//            InstanceTool.setSubscribe(this)
//        }
//        AppsflyerTool.initAppsflyer(this)
        ParamConstants.findUserId()
        Handler(Looper.getMainLooper()).postDelayed({ TopConstantService.startConstantNotify(this) },2000)
        if (StoreKeyData.getLong(StoreKeyData.BASE_FIRST_ENTER_TIME) == 0L){
            StoreKeyData.setLong(StoreKeyData.BASE_FIRST_ENTER_TIME, System.currentTimeMillis())
            RemoteConfigClass.findRemoteConfigByStart()
        }else{
            RemoteConfigClass.getRemoteAdConfigData()
        }
        initOtherConfig()
    }

    private fun initOtherConfig(){
        NotifyReceive().registerSelf(this)
        TickTimeReceiver().registerSelf(this)
        GlobalScope.launch {
            AdConfig.initialMaxSDK(this@BaseApplication)
        }
    }
}