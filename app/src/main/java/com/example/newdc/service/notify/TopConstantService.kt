package com.example.newdc.service.notify

import android.app.*
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.IBinder
import android.widget.RemoteViews
import androidx.core.app.NotificationCompat
import com.example.newdc.BaseLifeCycle
import com.example.newdc.R
import com.example.newdc.content.activity.StarterActivity
import com.example.newdc.modelutil.data.NotificationData
import com.example.newdc.service.FlashService
import kotlin.math.abs

class TopConstantService: Service() {
    override fun onBind(intent: Intent?): IBinder? {
        return null
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        try {
            startForeground(NotifyConfig.CONSTANT_NOTIFY, createNotification(this))
            freshTime = System.currentTimeMillis()
        }catch (e:Exception){
        }
        return START_STICKY
    }

    companion object{
        var freshTime = 0L
        fun serviceIsRunning(context: Context): Boolean {
            val manager = context.getSystemService(ACTIVITY_SERVICE) as ActivityManager
            for (service in manager.getRunningServices(Int.MAX_VALUE)) {
                if (Companion::class.java.name.contains(service.service.className)) {
                    return true
                }
            }
            return false
        }

        @Synchronized
        fun startConstantNotify(context: Context){
            try {
                val intent = Intent(context, TopConstantService::class.java)
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O){
                    context.startForegroundService(intent)
                }else{
                    try {
                        context.startService(intent)
                    } catch (e: Exception) {
                        val notifyManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
                        notifyManager.notify(NotifyConfig.CONSTANT_NOTIFY, createNotification(context))
                    }
                }
            }catch (e: Exception){
                e.printStackTrace()
            }
        }

        private fun createNotification(context: Context): Notification{
            val manager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

            val builder = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                val channel = NotificationChannel(NotifyConfig.BUNDLE_CONSTANT_ID, "CONSTANT", NotificationManager.IMPORTANCE_LOW)
                manager.createNotificationChannel(channel)
                NotificationCompat.Builder(context, NotifyConfig.BUNDLE_CONSTANT_ID)
            } else { NotificationCompat.Builder(context) }

            val remoteView = RemoteViews(context.packageName,R.layout.layout_constant_s)
            val remoteExpand = RemoteViews(context.packageName,R.layout.layout_constant_l)

            val flashIntent = Intent(context, FlashService::class.java).apply {
                action = FlashService.ACTION_FLASH_TORCH
            }

            val flashPending = PendingIntent.getService(context,
                NotifyConfig.NOTY_PENDING_FLASH,flashIntent,
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S)
                    PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_MUTABLE else PendingIntent.FLAG_UPDATE_CURRENT)

            remoteView.apply {
                setOnClickPendingIntent(R.id.ll_sto,clickPending(context,NotificationData.TYPE_STORAGE))
                setOnClickPendingIntent(R.id.ll_ram,clickPending(context,NotificationData.TYPE_RAM))
                setOnClickPendingIntent(R.id.ll_batt,clickPending(context,NotificationData.TYPE_BATTERY))
                setOnClickPendingIntent(R.id.ll_flash,flashPending)
            }

            remoteExpand.apply {
                setOnClickPendingIntent(R.id.ll_sto,clickPending(context,NotificationData.TYPE_STORAGE))
                setOnClickPendingIntent(R.id.ll_ram,clickPending(context,NotificationData.TYPE_RAM))
                setOnClickPendingIntent(R.id.ll_batt,clickPending(context,NotificationData.TYPE_BATTERY))
                setOnClickPendingIntent(R.id.ll_flash,flashPending)
            }

            val notify = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S){
                builder
                    .setCustomContentView(remoteView)
                    .setCustomBigContentView(remoteExpand)
                    .setCustomContentView(remoteView)
                    .setSmallIcon(R.mipmap.ic_launcher)
                    .setGroup("constant")
                    .setPriority(NotificationCompat.PRIORITY_LOW)
                    .build()
            } else {
                builder
                    .setContent(remoteExpand)
                    .setCustomBigContentView(remoteExpand)
                    .setCustomContentView(remoteExpand)
                    .setSmallIcon(R.mipmap.ic_launcher)
                    .setGroup("constant")
                    .setPriority(NotificationCompat.PRIORITY_LOW)
                    .build()
            }
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S){
                notify.flags = notify.flags or 0x00000002
            }
            return notify
        }

        private fun clickPending(context: Context,clickId: NotificationData) :PendingIntent{
            val intentClickIntent = Intent(context, StarterActivity::class.java).apply {
                flags = Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_NEW_TASK
                putExtra(BaseLifeCycle.BUNDLE_NOTIFY_ID,-1)
                putExtra(BaseLifeCycle.BUNDLE_NOTIFY_INTENT,clickId.type)
                putExtra(BaseLifeCycle.BUNDLE_NOTIFY_ENTER_CODE,clickId.enterCode)
            }

            return PendingIntent.getActivity(context,NotifyConfig.CONSTANT_NOTIFY + clickId.id,intentClickIntent,
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S)
                    PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_MUTABLE else PendingIntent.FLAG_UPDATE_CURRENT)
        }
    }

}