package com.example.newdc.service.notify

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.os.Build
import android.view.View
import android.widget.RemoteViews
import androidx.core.app.NotificationCompat
import com.additional.firebase.FirebaseEventPost
import com.example.newdc.BaseLifeCycle
import com.example.newdc.R
import com.example.newdc.content.activity.StarterActivity
import com.example.newdc.modelutil.data.NotificationData
import com.example.newdc.modelutil.data.RemoteConfigClass
import com.example.newdc.modelutil.data.StoreKeyData
import com.example.newdc.modelutil.dev.BatteryTool
import com.example.newdc.modelutil.dev.PathAndRamUtil
import com.example.newdc.modelutil.tool.ExtraTool.isLocked
import com.example.newdc.modelutil.tool.ExtraTool.isScreenOn
import com.example.newdc.modelutil.tool.PermissionTool
import kotlin.math.abs

object PostShowNotification {

    fun requestNotification(context: Context) {
        if (context.isLocked() || !context.isScreenOn() ||
            (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU && !PermissionTool.checkPostNotifyPermission(context))) {return}
        if (NotifyConfig.canShowNotifyByCount()) {
            FirebaseEventPost.event("post_out_times")
            return
        }

        val index = StoreKeyData.getInt(StoreKeyData.NOTIFY_SHOW_INDEX)
        val list = RemoteConfigClass.notifyContentList(context)
        val data = if (index < list.size) {
            list[index]
        }else{
            StoreKeyData.setInt(StoreKeyData.NOTIFY_SHOW_INDEX,0)
            list[0]
        }

        if (!StoreKeyData.getBoolean(StoreKeyData.NOTIFY_SHOW_FIRST)){
            if (NotifyConfig.allowFirstNotifyShow()){
                showNotificationByData(context, data)
                StoreKeyData.setBoolean(StoreKeyData.NOTIFY_SHOW_FIRST, true)
            }
        }else{
            if (NotifyConfig.allowNormalNotifyShow()){
                showNotificationByData(context, data)
            }
        }
    }


    fun showNotificationByData(context: Context,data: NotificationData) {
        val notification = createNotifyInstance(context,data)
        try {
            val postHeadHandle = PostHeadHandle.getInstance(context)?.apply { setNotification(notification,RemoteConfigClass.notifyShotTimes(),data.id) }
            postHeadHandle?.sendEmptyMessage(PostHeadHandle.POST_SHOW_MSG)
            StoreKeyData.apply {
                var showCount = getInt(NOTIFY_SHOW_COUNT)
                var index = getInt(NOTIFY_SHOW_INDEX) + 1
                index %= RemoteConfigClass.notifyContentList(context).size
                setInt(NOTIFY_SHOW_INDEX,index)
                setLong(NOTIFY_SHOW_LAST,System.currentTimeMillis())
                setInt(NOTIFY_SHOW_COUNT,++ showCount)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }


    private fun createNotifyInstance(context: Context,data: NotificationData): Notification {
        val manager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

        val intentClickIntent = Intent(context, StarterActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_NEW_TASK
            putExtra(BaseLifeCycle.BUNDLE_NOTIFY_ID,data.id)
            putExtra(BaseLifeCycle.BUNDLE_NOTIFY_INTENT,data.type)
            putExtra(BaseLifeCycle.BUNDLE_NOTIFY_ENTER_CODE,data.enterCode)
        }

        val intentClickPendingIntent = PendingIntent.getActivity(context,NotifyConfig.NOTY_PENDING_CLICK + data.id,intentClickIntent,
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S)
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_MUTABLE else PendingIntent.FLAG_UPDATE_CURRENT)

        val cancelIntent = Intent(NotifyConfig.NOTY_ACT_CANCEL).putExtra(NotifyConfig.BUNDLE_NOTIFY_ID,data.id)
        val cancelPendingIntent = PendingIntent.getBroadcast(context,NotifyConfig.NOTY_PENDING_CANCEL + data.id,
            cancelIntent,if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S)
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE else PendingIntent.FLAG_UPDATE_CURRENT)

        var iconRes = R.mipmap.noty_storage
        var titile = data.title.ifBlank { context.getString(R.string.noty_title_storage) }
        val sub = data.info.ifBlank { context.getString(R.string.noty_sub_storage) }

        when(data.type){
            NotifyConfig.TYPE_NOTY_BATTERY ->{
                iconRes = R.mipmap.noty_battery
                titile = if (data.title.contains("%b%")){
                    try {
                        val percent = BatteryTool.getBatterPercent(context)
                        data.title.replace("%b%", "$percent%")
                    } catch (e: Exception) {
                        data.title
                    }
                }else{
                    data.title
                }
                FirebaseEventPost.event("type_post_battery")
            }
            NotifyConfig.TYPE_NOTY_TEMP ->{
                iconRes = R.mipmap.noty_temp
                titile = if (data.title.contains("%t%")){
                    try {
                        val temp = "${BatteryTool.findBatteryTemp(context,null)}°C"
                        data.title.replace("%t%", temp)
                    } catch (e: Exception) {
                        data.title
                    }
                }else{
                    data.title
                }
                FirebaseEventPost.event("type_post_temp")
            }
            NotifyConfig.TYPE_NOTY_RAM ->{
                iconRes = R.mipmap.noty_ram
                titile = if (data.title.contains("%r%")){
                    try {
                        val percent = PathAndRamUtil.getRamPercent(context)
                        data.title.replace("%r%", "$percent%")
                    } catch (e: Exception) {
                        data.title
                    }
                }else{
                    data.title
                }
                FirebaseEventPost.event("type_post_ram")
            }
            NotifyConfig.TYPE_NOTY_STORAGE ->{
                iconRes = R.mipmap.noty_storage
                titile = if (data.title.contains("%s%")){
                    try {
                        val percent = PathAndRamUtil.getStoragePercent()
                        data.title.replace("%s%", "$percent%")
                    } catch (e: Exception) {
                        data.title
                    }
                }else{
                    data.title
                }
                FirebaseEventPost.event("type_post_storage")
            }
            NotifyConfig.TYPE_NOTY_LARGE ->{
                iconRes = R.mipmap.noty_large
                FirebaseEventPost.event("type_post_large")
            }
            NotifyConfig.TYPE_NOTY_REDUNDANT ->{
                iconRes = R.mipmap.noty_redundant
                FirebaseEventPost.event("type_post_redundant")
            }
            NotifyConfig.TYPE_NOTY_DUPLICATION ->{
                iconRes = R.mipmap.noty_dup
                FirebaseEventPost.event("type_post_duplication")
            }
            NotifyConfig.TYPE_NOTY_INFO ->{
                iconRes = R.mipmap.noty_info
                FirebaseEventPost.event("type_post_info")
            }
            NotifyConfig.TYPE_NOTY_RECENT ->{
                iconRes = R.mipmap.noty_recent
                FirebaseEventPost.event("type_post_recent")
            }
        }
        FirebaseEventPost.event("type_post_all")

        val remoteViewSmall = RemoteViews(context.packageName,R.layout.layout_notify_small).apply {
            setOnClickPendingIntent(R.id.btn_clean,intentClickPendingIntent)
            setTextViewText(R.id.tv_title,titile)
            setImageViewResource(R.id.icon_func,iconRes)
        }

        val remoteViewLarge = RemoteViews(context.packageName,R.layout.layout_notify_large).apply {
            setOnClickPendingIntent(R.id.btn_clean,intentClickPendingIntent)
            setOnClickPendingIntent(R.id.img_close,cancelPendingIntent)
            setTextViewText(R.id.tv_title,titile)
            setTextViewText(R.id.tv_sub,sub)
            setImageViewResource(R.id.icon_func,iconRes)
            if (RemoteConfigClass.notifyCloseTime() > 0 && abs(System.currentTimeMillis() - StoreKeyData.getLong(StoreKeyData.BASE_FIRST_ENTER_TIME)) >
                RemoteConfigClass.notifyCloseTime() * 60 * 1000) {
                setViewVisibility(R.id.img_close, View.VISIBLE)
            }else{
                setViewVisibility(R.id.img_close, View.INVISIBLE)
            }
        }

        val notifyBuilder = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(NotifyConfig.NOTIFICATION_CODE_POST.toString(), "POST_SEND", NotificationManager.IMPORTANCE_HIGH).apply {
                setSound(null,null)
                vibrationPattern = longArrayOf(0L)
            }
            manager.createNotificationChannel(channel)
            NotificationCompat.Builder(context, NotifyConfig.NOTIFICATION_CODE_POST.toString())
        } else {
            NotificationCompat.Builder(context)
                .setVibrate(longArrayOf(0L))
                .setSound(null)
        }

        val notification = notifyBuilder.apply {
            setCustomContentView(if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {remoteViewSmall} else remoteViewLarge)
            setCustomHeadsUpContentView(remoteViewLarge)
            setCustomBigContentView(remoteViewLarge)
            setContentIntent(intentClickPendingIntent)
            setDeleteIntent(cancelPendingIntent)
            setSmallIcon(R.mipmap.nav_icon_real)
            setAutoCancel(true)
            setPriority(NotificationCompat.PRIORITY_MAX)
            setGroup("notify_send")
            setVibrate(longArrayOf(0L))
        }.build()
        if (RemoteConfigClass.notifySlideTime() > 0 && abs(System.currentTimeMillis() - StoreKeyData.getLong(StoreKeyData.BASE_FIRST_ENTER_TIME)) >= RemoteConfigClass.notifySlideTime() * 60 * 1000){
            notification.flags = notification.flags or 0x00000002
        }

        return notification
    }

}