package com.example.newdc.service.receiver

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Build
import android.os.Handler
import android.os.Looper
import com.additional.biservice.BiServiceClass
import com.additional.biservice.basedata.NetEventDataBase
import com.additional.firebase.InstanceTool
import com.example.newdc.BaseApplication
import com.example.newdc.modelutil.data.RemoteConfigClass
import com.example.newdc.modelutil.data.StoreKeyData
import com.example.newdc.modelutil.tool.ExtraTool.isLocked
import com.example.newdc.modelutil.tool.ExtraTool.isScreenOn
import com.example.newdc.service.notify.PostShowNotification
import com.example.newdc.service.notify.TopConstantService
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import kotlin.math.abs

class TickTimeReceiver: BroadcastReceiver() {
    override fun onReceive(context: Context?, intent: Intent?) {
//        InstanceTool.fetchRemoteConfig()
        RemoteConfigClass.getRemoteConfig()
        if (StoreKeyData.recordTime()){
            StoreKeyData.restTodayConfig()
        }
        context?.let {
            PostShowNotification.requestNotification(it)
            if (abs(System.currentTimeMillis() - TopConstantService.freshTime) > 60 * 5 * 1000
                && !context.isLocked() && context.isScreenOn()){
                Handler(Looper.getMainLooper()).postDelayed({
                    TopConstantService.startConstantNotify(it)
                },10000)
            }
        }
        postLocalEvent()
    }

    fun registerSelf(context:Context){
        val filter = IntentFilter().also { it.addAction(Intent.ACTION_TIME_TICK) }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU){
            context.registerReceiver(this,filter, Context.RECEIVER_EXPORTED)
        }else{
            context.registerReceiver(this,filter)
        }
    }

    fun postLocalEvent(){
        GlobalScope.launch {
            val startEvents = NetEventDataBase.getInstance(BaseApplication.mAppContext).startEventDao().getEvents()
            if (startEvents.isNotEmpty()){
                val postList = mutableListOf<HashMap<String,Any>>()
                startEvents.forEach {
                    postList.add(it.data)
                }
                BiServiceClass().postStartEventList(postList)
            }

            val valueEvents = NetEventDataBase.getInstance(BaseApplication.mAppContext).valueEventDao().getEvents()
            if (valueEvents.isNotEmpty()){
                val postList = mutableListOf<HashMap<String,Any>>()
                valueEvents.forEach {
                    postList.add(it.data)
                }
                BiServiceClass().postValueEventList(postList)
            }
        }
    }
}