package com.example.newdc.service.notify


import android.annotation.SuppressLint
import android.app.Notification
import android.app.NotificationManager
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Handler
import android.os.Looper
import android.os.Message
import android.util.Log
import java.util.concurrent.TimeUnit

class PostHeadHandle(private val mContext: Context) : Handler(Looper.getMainLooper()) {
    private var postFlag = false
    private var postTimes = 0
    private var postMax = 0
    private var mNotification: Notification? = null
    private var mPostId = -1

    init {
        val screenChangeReceiver = ScreenChangeReceiver()
        mContext.registerReceiver(screenChangeReceiver, IntentFilter("android.intent.action.SCREEN_OFF"))
        mContext.registerReceiver(screenChangeReceiver, IntentFilter("android.intent.action.SCREEN_ON"))
    }

    fun setNotification(notification: Notification?, posMax: Int = 0, postId: Int) {
        postMax = posMax
        postFlag = true
        postTimes = 0
        mPostId = postId
        mNotification = notification
    }

    override fun handleMessage(msg: Message) {
        super.handleMessage(msg)
        if (msg.what == POST_SHOW_MSG) {
            try {
                if (mNotification != null) {
                    val manager = mContext.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
                    manager.notify(mPostId, mNotification)
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
            removeMessages(POST_SHOW_MSG)

            if (postMax > 0) {
                postTimes++
                if (postTimes > postMax) {
                    postFlag = false
                }
            }else{
                postFlag = false
            }

            if (postFlag) {
                sendEmptyMessageDelayed(POST_SHOW_MSG, POST_TIME)
            }
        }
        if (msg.what == POST_CLOSE_MSG) {
            postFlag = false
            postTimes = 0
            removeMessages(POST_SHOW_MSG)
        }
    }


    class ScreenChangeReceiver : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            if (intent.action == "android.intent.action.SCREEN_OFF") {
                if (mInstance != null) {
                    mInstance!!.removeMessages(POST_SHOW_MSG)
                }
            }
        }
    }

    companion object {
        const val POST_SHOW_MSG: Int = 200
        const val POST_CLOSE_MSG: Int = 201

        private val POST_TIME = TimeUnit.SECONDS.toMillis(4)

        @SuppressLint("StaticFieldLeak")
        var mInstance: PostHeadHandle? = null

        @Synchronized
        fun getInstance(context: Context): PostHeadHandle? {
            if (mInstance == null) {
                mInstance = PostHeadHandle(context)
            }
            return mInstance!!
        }
    }
}