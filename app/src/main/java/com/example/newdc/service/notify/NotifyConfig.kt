package com.example.newdc.service.notify

import com.example.newdc.modelutil.data.RemoteConfigClass
import com.example.newdc.modelutil.data.StoreKeyData
import kotlin.math.abs

object NotifyConfig {
    //MIN
    const val NOTIFY_POST_FIRST_INTERVAL = 30
    const val NOTIFY_POST_LAST_INTERVAL = 30

    const val NOTIFY_RESIDENT_INTERVAL = -1
    const val NOTIFY_CANCEL_INTERVAL = -1

    const val NOTIFY_MAX_SHOW = 20
    const val NOTIFY_SHOT_TIMES = 0

    const val TYPE_NOTY_HOME = "home"
    const val TYPE_NOTY_BATTERY = "battery"
    const val TYPE_NOTY_STORAGE = "storage"
    const val TYPE_NOTY_RAM = "ram"
    const val TYPE_NOTY_TEMP = "temp"
    const val TYPE_NOTY_INFO = "info"
    const val TYPE_NOTY_REDUNDANT = "redundant"
    const val TYPE_NOTY_DUPLICATION = "duplicate"
    const val TYPE_NOTY_RECENT = "recent"
    const val TYPE_NOTY_LARGE = "large"

    const val BUNDLE_NOTIFY_ID = "notify_id"
    const val NOTY_ACT_CANCEL = "noty_act_cancel"
    const val NOTY_PENDING_CLICK = 210
    const val NOTY_PENDING_CANCEL = 310
    const val NOTIFICATION_CODE_POST = 230
    const val BUNDLE_CONSTANT_ID = "constant_id"
    const val CONSTANT_NOTIFY = 400
    const val NOTY_PENDING_FLASH = 401

    fun canShowNotifyByCount(): Boolean{
        return StoreKeyData.getInt(StoreKeyData.NOTIFY_SHOW_COUNT) >= RemoteConfigClass.notifyShowMax()
    }

    fun allowFirstNotifyShow(): Boolean{
        return abs(System.currentTimeMillis() - StoreKeyData.getLong(StoreKeyData.BASE_FIRST_ENTER_TIME)) >= RemoteConfigClass.notifyFirstShowInterval() * 60 * 1000
    }

    fun allowNormalNotifyShow(): Boolean{
        return abs(System.currentTimeMillis() - StoreKeyData.getLong(StoreKeyData.NOTIFY_SHOW_LAST)) >= RemoteConfigClass.notifyNormalShowInterval() * 60 * 1000
    }
}