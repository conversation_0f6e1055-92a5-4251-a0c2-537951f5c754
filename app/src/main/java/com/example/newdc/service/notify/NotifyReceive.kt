package com.example.newdc.service.notify

import android.app.NotificationManager
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Build
import com.example.newdc.BaseApplication

class NotifyReceive: BroadcastReceiver() {
    override fun onReceive(context: Context?, intent: Intent?) {
        when(intent?.action){
            NotifyConfig.NOTY_ACT_CANCEL ->{
                val manager = context?.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
                val id = intent.getIntExtra(NotifyConfig.BUNDLE_NOTIFY_ID, -1)
                manager?.cancel(id)
                PostHeadHandle.getInstance(BaseApplication.mAppContext)?.sendEmptyMessage(PostHeadHandle.POST_CLOSE_MSG)
            }
        }
    }

    fun registerSelf(context: Context){
        val filter = IntentFilter()
        filter.addAction(NotifyConfig.NOTY_ACT_CANCEL)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU){
            context.registerReceiver(this,filter, Context.RECEIVER_EXPORTED)
        }else{
            context.registerReceiver(this,filter)
        }
    }
}