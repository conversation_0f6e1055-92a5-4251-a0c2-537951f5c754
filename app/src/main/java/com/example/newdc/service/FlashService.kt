package com.example.newdc.service

import android.app.Service
import android.content.Context
import android.content.Intent
import android.hardware.camera2.CameraCharacteristics
import android.hardware.camera2.CameraManager
import android.os.Build
import android.os.IBinder
import androidx.annotation.RequiresApi

class FlashService: Service() {
    private lateinit var cameraManager: CameraManager
    private var isTorchOn: Boolean = false
    private lateinit var cameraId: String


    override fun onBind(intent: Intent?): IBinder? {
        return null
    }

    companion object {
        const val ACTION_FLASH_TORCH = "action_flash_flash"
    }

    override fun onCreate() {
        super.onCreate()
        cameraManager = getSystemService(Context.CAMERA_SERVICE) as CameraManager
    }

    @RequiresApi(Build.VERSION_CODES.M)
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        if (intent?.action == ACTION_FLASH_TORCH) {
            toggleTorch()
        }
        return super.onStartCommand(intent, flags, startId)
    }

    @RequiresApi(Build.VERSION_CODES.M)
    private fun toggleTorch() {
        try {
            val cameraIds = cameraManager.cameraIdList

            for (id in cameraIds) {
                val characteristics = cameraManager.getCameraCharacteristics(id)
                val flashAvailable = characteristics.get(CameraCharacteristics.FLASH_INFO_AVAILABLE)
                if (flashAvailable == true) {
                    cameraId = id
                    break
                }
            }

            isTorchOn = !isTorchOn
            cameraManager.setTorchMode(cameraId, isTorchOn)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
}