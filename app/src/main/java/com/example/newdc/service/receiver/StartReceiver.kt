package com.example.newdc.service.receiver

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import com.additional.firebase.FirebaseEventPost
import com.example.newdc.BaseApplication
import com.example.newdc.modelutil.tool.ExtraTool.debug
import com.example.newdc.service.notify.PostShowNotification
import com.example.newdc.service.notify.TopConstantService

class StartReceiver: BroadcastReceiver() {
    override fun onReceive(context: Context?, intent: Intent?) {
        debug("StartReceiver", "onReceive")
        FirebaseEventPost.event("start_receive")
        if (intent?.action == Intent.ACTION_USER_PRESENT){
            context?.let {
                PostShowNotification.requestNotification(it)
                TopConstantService.startConstantNotify(it)
            }
        }
    }
}