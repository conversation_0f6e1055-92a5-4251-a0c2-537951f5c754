package com.example.newdc.content.activity

import android.content.Intent
import android.os.Handler
import android.os.Looper
import android.text.format.Formatter
import android.view.View
import androidx.lifecycle.ViewModelProvider
import com.additional.max.AdConfig
import com.additional.max.MaxScreenAdManager
import com.example.newdc.content.BaseLayoutActivity
import com.example.newdc.databinding.ActivityRealtimePageBinding
import com.example.newdc.R
import com.example.newdc.content.fragment.info.ActivityBaseInfo
import com.example.newdc.dataadapter.MoreInfoAdapter
import com.example.newdc.dataadapter.RateItemAdapter
import com.example.newdc.modelutil.DevBatteryViewModel
import com.example.newdc.modelutil.DevValueViewModel
import com.example.newdc.modelutil.data.EventData
import com.example.newdc.modelutil.tool.ExtraTool.countDownFlow
import com.example.newdc.modelutil.tool.FormatTool
import com.jeremyliao.liveeventbus.LiveEventBus
import kotlinx.coroutines.Job
import java.util.*


class RealtimeActivity: BaseLayoutActivity<ActivityRealtimePageBinding>() {
    private lateinit var batteryViewModel: DevBatteryViewModel
    private lateinit var ramViewModel: DevValueViewModel

    private lateinit var cpuAdapter: RateItemAdapter
    private var timer: Timer? = null
    private var animDisposableJob: Job? = null
    private var maxScreenAdManager: MaxScreenAdManager? = null

    override fun layoutResource(): Int {
        return R.layout.activity_realtime_page
    }

    override fun onPause() {
        super.onPause()
        timer?.cancel()
    }

    override fun onStart() {
        super.onStart()
        startRepeatingTask(2000)
    }

    override fun viewCreated() {
        batteryViewModel = ViewModelProvider(this)[DevBatteryViewModel::class.java]
        ramViewModel = ViewModelProvider(this)[DevValueViewModel::class.java]
        mBindingView.lifecycleOwner = this
        mBindingView.battery = batteryViewModel
        mBindingView.ram = ramViewModel

        setView()
        setListener()
        initAd()
        startAnim()
    }

    private fun initAd(){
        if (AdConfig.getSwitchAdInters()){
            maxScreenAdManager = MaxScreenAdManager(true){
                closeAnim()
            }.apply {
                initAd()
                adLoad(this@RealtimeActivity)
            }
        }
    }

    private fun setListener() {
        ramViewModel.getStatusCpuRates.observe(this) {
            val list = it.take(8).toMutableList()
            cpuAdapter.setData(list)
        }
        ramViewModel.getValueRamRawUsed.observe(this) {
            val used = Formatter.formatFileSize(this,it)
            val simplePoint = it / 1000 / 1000
            mBindingView.valueUsed.text = FormatTool.spannableStringBySize(used,30,13).trim()
            mBindingView.tvCurUsed.text = getString(R.string.base_used,"${simplePoint}MB")
            mBindingView.curRam.addPoint(simplePoint.toFloat())
        }
        ramViewModel.getValueRamTotal.observe(this) {
            mBindingView.valueTotal.text = FormatTool.spannableStringBySize(it,30,13).trim()
        }
        ramViewModel.getValueRamRawAvail.observe(this) {
            val simplePoint = it / 1000 / 1000
            mBindingView.tvCurAvailable.text = getString(R.string.base_available,"${simplePoint}MB")
        }

        ramViewModel.getValueRamPercent.observe(this) {
            mBindingView.tvRamPer.text = "$it%"
            mBindingView.barRam.setProgress(it.toFloat())
        }

        mBindingView.llFunMore.setOnClickListener {
            startActivity(Intent(this,MainActivity::class.java))
            finish()
            Handler(Looper.getMainLooper()).postDelayed({
                LiveEventBus.get<Int>(EventData.EVENT_HOME_TAB_TRANSLATE).post(1)
            },200)
        }

        mBindingView.moreRam.setOnClickListener {
            ActivityBaseInfo.startDevInfoActivity(this,5)
        }

        mBindingView.moreBatt.setOnClickListener {
            ActivityBaseInfo.startDevInfoActivity(this,6)
        }
    }

    private fun startAnim(){
        animDisposableJob = countDownFlow(
            start = {
                mBindingView.lottPage.visibility = View.VISIBLE
                mBindingView.lottScan.playAnimation()
            },
            next = {},
            end = {
                if (maxScreenAdManager != null && AdConfig.getAllowShowInterstitial()){
                    maxScreenAdManager?.startWaitingJob(this,
                        next = {
                            if (maxScreenAdManager?.showAd(this) == true){
                                Handler(Looper.getMainLooper()).postDelayed({closeAnim() },500)
                            }
                        },
                        end = {
                            closeAnim()
                        })
                }else{
                    closeAnim()
                }
            }
        )
    }


    private fun closeAnim(){
        mBindingView.lottScan.apply {
            cancelAnimation()
            clearAnimation()
            visibility = View.GONE
        }
        mBindingView.lottPage.visibility = View.GONE
    }

    private fun setView() {
        mBindingView.moreRam.text = "${mBindingView.moreRam.text} >"
        mBindingView.moreBatt.text = "${mBindingView.moreBatt.text} >"
        mBindingView.llTitleBar.tvTitle.text = getString(R.string.title_realtime)
        cpuAdapter = RateItemAdapter()
        mBindingView.rvCpu.apply {
            adapter = cpuAdapter
        }
    }

    private fun startRepeatingTask(interval: Long) {
        timer = Timer()
        val task = object : TimerTask() {
            override fun run() {
                batteryViewModel.findBatteryInfo(this@RealtimeActivity)
                ramViewModel.getRealtimeStatus(this@RealtimeActivity)
            }
        }
        timer?.scheduleAtFixedRate(task, 0, interval)
    }

    override fun finish() {
        LiveEventBus.get<String>(EventData.EVENT_RETURN_BACK).post("")
        super.finish()
    }

}