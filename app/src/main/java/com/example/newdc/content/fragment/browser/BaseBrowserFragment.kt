package com.example.newdc.content.fragment.browser

import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.CheckBox
import androidx.databinding.ViewDataBinding
import androidx.lifecycle.ViewModelProvider
import com.example.newdc.R
import com.example.newdc.content.fragment.BaseLayoutFragment
import com.example.newdc.dataadapter.BaseFileBrowserAdapter
import com.example.newdc.dataadapter.FileItemAdapter
import com.example.newdc.modelutil.FilesStorageViewModel
import com.example.newdc.modelutil.data.EventData
import com.example.newdc.modelutil.data.ItemFileData
import com.example.newdc.modelutil.tool.MediaStorageTool
import com.jeremyliao.liveeventbus.LiveEventBus

abstract class BaseBrowserFragment<V: ViewDataBinding>: BaseLayoutFragment<V>() {
    protected lateinit var filesStorageViewModel: FilesStorageViewModel
    var itemAdapter: BaseFileBrowserAdapter? = null

    var itemClickListener: ItemSelectListener? = null
    var checkAll: CheckBox? = null
    private var checkSetStata = false

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        filesStorageViewModel = ViewModelProvider(requireActivity())[FilesStorageViewModel::class.java]
        checkAll = view.findViewById<CheckBox>(R.id.ck_all)?.apply {
            setOnCheckedChangeListener { buttonView, isChecked ->
                if (checkSetStata){
                    checkSetStata = false
                }else{
                    itemAdapter?.checkAllFilterItem(isChecked)
                }
            }
        }
        itemAdapter = setFileAdapter()
        if (this !is FragmentBrowserDir){
            itemAdapter?.setItemListener(object :BaseFileBrowserAdapter.BaseItemClickListener{
                override fun onItemClick(item: ItemFileData) {
                    if (!itemAdapter!!.barOpen){
                        MediaStorageTool.fileOpen(requireContext(), item.filePath, item.mime, item.type)
                    }else{
                        if (checkAll != null){
                            if (checkAll!!.isChecked){
                                if (itemAdapter!!.getFilterItems().find { !it.isSelect } != null){
                                    checkSetStata = true
                                    checkAll!!.isChecked = false
                                }
                            }else{
                                if (itemAdapter!!.getFilterItems().find { !it.isSelect } == null){ checkAll!!.isChecked = true }
                            }
                        }
                    }
                    itemClickListener?.onClick(itemAdapter!!.getAllSelectItems())
                }

                override fun onItemLongClick(barOpen: Boolean) {
                    checkAll?.visibility = if (barOpen) View.VISIBLE else View.GONE
                    itemClickListener?.onLongClick(barOpen,itemAdapter!!.getAllSelectItems())
                }
            })
        }

        LiveEventBus.get<String>(EventData.EVENT_FILE_CHANGED).observe(this){
            itemAdapter?.notifyDataSetChanged()
        }

        super.onViewCreated(view, savedInstanceState)
    }

    fun closeControlBar(){
        checkAll?.visibility = View.GONE
        getAdapter()?.closeControlBar()
    }

    fun getAdapter(): BaseFileBrowserAdapter? {
        return itemAdapter
    }

    interface ItemSelectListener{
        fun onClick(selectList: MutableList<ItemFileData>)
        fun onLongClick(barOpen: Boolean,selectList: MutableList<ItemFileData>)
    }

    fun setItemSelectListener(itemSelectListener: ItemSelectListener) {
        itemClickListener = itemSelectListener
    }

    abstract fun setFileAdapter(): BaseFileBrowserAdapter

}