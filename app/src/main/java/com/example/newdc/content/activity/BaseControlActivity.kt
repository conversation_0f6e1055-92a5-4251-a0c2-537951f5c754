package com.example.newdc.content.activity

import android.os.Bundle
import android.view.View
import android.widget.Button
import android.widget.LinearLayout
import androidx.databinding.ViewDataBinding
import com.example.newdc.R
import com.example.newdc.content.BaseLayoutActivity
import com.example.newdc.content.view.dialog.FileChangeDialog
import com.example.newdc.content.view.dialog.FileDetailDialog
import com.example.newdc.modelutil.data.EventData
import com.example.newdc.modelutil.data.ItemFileData
import com.example.newdc.modelutil.tool.FilesTool
import com.example.newdc.modelutil.tool.MediaStorageTool
import com.jeremyliao.liveeventbus.LiveEventBus

abstract class BaseControlActivity<V: ViewDataBinding>: BaseLayoutActivity<V>() {
    protected var controlBar: LinearLayout? = null
    protected var btnClean: Button? = null
    protected var selectItem = mutableListOf<ItemFileData>()
    protected var browserType = 0

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        controlBar = mBindingView.root.findViewById(R.id.ll_control)
        btnClean = mBindingView.root.findViewById(R.id.btn_clean)
        setControlBarListener()
    }

    fun changeBarStatus(itemSize: Int){
        if (itemSize == 1){
            controlBar?.visibility = View.VISIBLE
            btnClean?.visibility = View.GONE
        }else if (itemSize > 1){
            controlBar?.visibility = View.GONE
            btnClean?.visibility = View.VISIBLE
        }else{
            controlBar?.visibility = View.GONE
            btnClean?.visibility = View.GONE
        }
    }

    private fun setControlBarListener(){
        controlBar?.findViewById<LinearLayout>(R.id.ll_detail)?.setOnClickListener{
            showDetail(selectItem)
        }
        controlBar?.findViewById<LinearLayout>(R.id.ll_rename)?.setOnClickListener{
            renameFile(selectItem)
        }
        controlBar?.findViewById<LinearLayout>(R.id.ll_delete)?.setOnClickListener {
            deleteToOtherPage(browserType)
        }
        controlBar?.findViewById<LinearLayout>(R.id.ll_open)?.setOnClickListener {
            openFile(selectItem)
        }
        controlBar?.findViewById<LinearLayout>(R.id.ll_share)?.setOnClickListener {
            shareFile(selectItem)
        }
    }

    private fun showDetail(items: MutableList<ItemFileData>){
        if (items.isNotEmpty()){
            FileDetailDialog(items[0]).show(supportFragmentManager,"dialog_detail")
        }
    }

    private fun renameFile(items: MutableList<ItemFileData>){
        if (items.isNotEmpty()){
            FileChangeDialog.showFileChangeDialog(supportFragmentManager,FileChangeDialog.TYPE_FILE_RENAME,items){
                LiveEventBus.get<String>(EventData.EVENT_FILE_CHANGED).post("")
            }
        }
    }

    fun deleteFile(items: MutableList<ItemFileData>,call: () ->Unit = {}){
        if (items.isNotEmpty()){
            FileChangeDialog.showFileChangeDialog(supportFragmentManager,FileChangeDialog.TYPE_FILE_DELETE,items){
                call.invoke()
            }
        }
    }

    private fun openFile(items: MutableList<ItemFileData>){
        if (items.isNotEmpty()){
            val file = items[0]
            if (file.isDir){
                LiveEventBus.get<String>(EventData.EVENT_DIR_ENTER).post(file.filePath)
            }else{
                MediaStorageTool.fileOpen(this,file.filePath,file.mime,file.type)
            }
        }
    }

    private fun shareFile(items: MutableList<ItemFileData>){
        if (items.isNotEmpty()){
            val file = items[0]
            FilesTool.shareFile(this,file.filePath)
        }
    }

    fun deleteToOtherPage(type: Int){
        deleteFile(selectItem){
            AnimTransActivity.enterTransActivity(this,type)
            finish()
        }
    }

}