package com.example.newdc.content.activity


import android.Manifest
import android.content.Intent
import android.os.Build
import android.provider.Settings
import android.view.View
import com.example.newdc.BaseLifeCycle
import com.example.newdc.R
import com.example.newdc.content.BaseLayoutActivity
import com.example.newdc.content.view.dialog.RequestDialog
import com.example.newdc.databinding.ActivityPermissionManagerBinding
import com.example.newdc.modelutil.data.StoreKeyData
import com.example.newdc.modelutil.tool.PermissionTool

class PermissionManagerActivity: BaseLayoutActivity<ActivityPermissionManagerBinding>() {

    override fun layoutResource(): Int {
        return R.layout.activity_permission_manager
    }

    override fun onStart() {
        super.onStart()
        val stoAllow = PermissionTool.allowStoragePermission(this)
        var notyAllow = PermissionTool.checkPostNotifyPermission(this)
        val optAllow = PermissionTool.allowBatteryOptimization(this)

        mBindingView.ckGrantSto.isChecked = stoAllow
        mBindingView.ckGrantSto.isEnabled = !stoAllow
        mBindingView.ckGrantNotify.isChecked = notyAllow
        mBindingView.ckGrantNotify.isEnabled = !notyAllow
        mBindingView.ckGrantOpt.isEnabled = !optAllow
    }

    override fun viewCreated() {
        mBindingView.ckGrantSto.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                if (!PermissionTool.allowStoragePermission(this)){
                    RequestDialog(RequestDialog.TYPE_STORAGE,{
                        PermissionTool.permissionGrantStorage(this)
                    },{
                        mBindingView.ckGrantSto.isChecked = false
                        mBindingView.ckGrantSto.isSelected = false
                    }
                    ).show(supportFragmentManager,"permission_storage")
                }
            }
        }

        mBindingView.ckGrantNotify.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                if (!PermissionTool.checkPermissionGranted(this, Manifest.permission.POST_NOTIFICATIONS)){
                    RequestDialog(RequestDialog.TYPE_NOTIFY,{
                        PermissionTool.postNotifySetting(this)
                    },{
                        mBindingView.ckGrantNotify.isChecked = false
                        mBindingView.ckGrantNotify.isSelected = false
                    }).show(supportFragmentManager,"notify")
                }
            }
        }

        mBindingView.ckGrantOpt.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                if (!PermissionTool.allowBatteryOptimization(this)) {
                    RequestDialog(RequestDialog.TYPE_BATTERY,{
                        PermissionTool.requestBatteryOptimization(this)
                    },{
                        mBindingView.ckGrantOpt.isChecked = false
                        mBindingView.ckGrantOpt.isSelected = false
                    }).show(supportFragmentManager,"optimization")
                }else{
                    try {
                        startActivity(Intent().apply {
                            setAction(Settings.ACTION_IGNORE_BATTERY_OPTIMIZATION_SETTINGS)
                        })
                        BaseLifeCycle.splashFlag = true
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                }
            }else{
                if (PermissionTool.allowBatteryOptimization(this)){
                    try {
                        startActivity(Intent().apply {
                            setAction(Settings.ACTION_IGNORE_BATTERY_OPTIMIZATION_SETTINGS)
                        })
                        BaseLifeCycle.splashFlag = true
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                }
            }
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            mBindingView.llNoty.visibility = View.VISIBLE
        }else{
            mBindingView.llNoty.visibility = View.GONE
        }

        mBindingView.llStorage.setOnClickListener {
            if (!mBindingView.ckGrantSto.isChecked){
                mBindingView.ckGrantSto.isChecked = !mBindingView.ckGrantSto.isChecked
            }
        }

        mBindingView.llNoty.setOnClickListener {
            if (!mBindingView.ckGrantNotify.isChecked) {
                mBindingView.ckGrantNotify.isChecked = !mBindingView.ckGrantNotify.isChecked
            }
        }

        mBindingView.llOpt.setOnClickListener {
            if (!mBindingView.ckGrantOpt.isChecked){
                mBindingView.ckGrantOpt.isChecked = !mBindingView.ckGrantOpt.isChecked
            }
        }

    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == PermissionTool.PERMISSION_STORAGE_MANAGER_CODE){
            val isAllow = PermissionTool.allowStoragePermission(this)
            mBindingView.ckGrantSto.isSelected = isAllow
            mBindingView.ckGrantSto.isChecked = isAllow
        }
        if (requestCode == PermissionTool.PERMISSION_POST_NOTIFY_CODE){
            val isAllow = PermissionTool.checkPostNotifyPermission(this)
            mBindingView.ckGrantNotify.isSelected = isAllow
            mBindingView.ckGrantNotify.isChecked = isAllow
            StoreKeyData.setBoolean(StoreKeyData.PERMISSION_NOTIFY_SWITCH,true)
        }
        if (requestCode == PermissionTool.PERMISSION_OPTIMIZATION_CODE){
            val isAllow = PermissionTool.allowBatteryOptimization(this)
            mBindingView.ckGrantOpt.isSelected = isAllow
            mBindingView.ckGrantOpt.isChecked = isAllow
        }
    }

}