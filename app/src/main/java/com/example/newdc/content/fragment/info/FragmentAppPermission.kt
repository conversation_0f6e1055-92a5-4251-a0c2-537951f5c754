package com.example.newdc.content.fragment.info

import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.example.newdc.R
import com.example.newdc.content.fragment.BaseLayoutFragment
import com.example.newdc.dataadapter.ApkPermissionAdapter
import com.example.newdc.databinding.FragmentAppGeneralBinding
import com.example.newdc.databinding.FragmentAppPermissionBinding
import com.example.newdc.databinding.FragmentInfoSysBinding
import com.example.newdc.modelutil.AppInfoViewModel
import com.example.newdc.modelutil.DevInfoViewModel
import java.util.*

class FragmentAppPermission(private val packageName: String): BaseLayoutFragment<FragmentAppPermissionBinding>() {
    private lateinit var appInfoViewModel: AppInfoViewModel
    private lateinit var permissionAdapter: ApkPermissionAdapter

    override fun layoutResource(): Int {
        return R.layout.fragment_app_permission
    }

    override fun viewCreated() {
        appInfoViewModel = ViewModelProvider(this)[AppInfoViewModel::class.java]

        permissionAdapter = ApkPermissionAdapter()
        appInfoViewModel.getAppPermissionList().observe(viewLifecycleOwner) {
            permissionAdapter.setApkData(it)
        }

        mBindingView.rvPermission.apply {
            layoutManager = LinearLayoutManager(context)
            adapter = permissionAdapter
        }

        appInfoViewModel.getAppAllPermissionUse(requireContext(),packageName)

    }
}