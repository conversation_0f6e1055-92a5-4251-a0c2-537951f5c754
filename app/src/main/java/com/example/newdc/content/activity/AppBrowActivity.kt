package com.example.newdc.content.activity

import android.view.View
import android.widget.AdapterView
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.additional.max.AdConfig
import com.additional.max.MaxScreenAdManager
import com.example.newdc.R
import com.example.newdc.content.BaseLayoutActivity
import com.example.newdc.content.view.dialog.ApkInfoMoreDialog
import com.example.newdc.content.view.dialog.OrderPopDialog
import com.example.newdc.dataadapter.ApkInfoAdapter
import com.example.newdc.databinding.ActivityBrowAppBinding
import com.example.newdc.modelutil.AppInfoViewModel
import com.example.newdc.modelutil.data.EventData
import com.example.newdc.modelutil.tool.OrderTool
import com.jeremyliao.liveeventbus.LiveEventBus

class AppBrowActivity: BaseLayoutActivity<ActivityBrowAppBinding>() {
    private lateinit var appAdapter: ApkInfoAdapter
    private lateinit var appInfoViewModel: AppInfoViewModel
    private var orderType = OrderTool.TYPE_ORDER_NEW
    private var filterType = 0
    private var maxScreenAdManager: MaxScreenAdManager? = null
    private var loadFinish = false

    override fun layoutResource(): Int {
        return R.layout.activity_brow_app
    }

    override fun viewCreated() {
        mBindingView.titleBar.tvTitle.text = getString(R.string.title_apps)
        appInfoViewModel = ViewModelProvider(this)[AppInfoViewModel::class.java]
        appAdapter = ApkInfoAdapter{
            ApkInfoMoreDialog(this@AppBrowActivity,it).show(supportFragmentManager,it)
        }

        mBindingView.rvDir.apply {
            layoutManager = LinearLayoutManager(this@AppBrowActivity,LinearLayoutManager.VERTICAL,false)
            adapter = appAdapter
        }

        appInfoViewModel.getAppPackageList().observe(this) {
            OrderTool.orderApkType(orderType,it)
            appAdapter.setApkData(it)
            if (loadFinish){
                mBindingView.llLoad.llLoad.visibility = View.GONE
            }
        }

        mBindingView.spinnerType.onItemSelectedListener = object : AdapterView.OnItemSelectedListener{
            override fun onItemSelected(parent: AdapterView<*>?, view: View?, position: Int, id: Long) {
                filterType = position
                appAdapter.filterFileList(position)
            }

            override fun onNothingSelected(parent: AdapterView<*>?) {
            }
        }

        mBindingView.imgOrder.setOnClickListener {
            OrderPopDialog(orderType){
                orderType = it
                 OrderTool.orderApkType(it, appAdapter.getList())
                appAdapter.filterFileList(filterType)
            }.show(supportFragmentManager,"order")
        }

        initAd()
        mBindingView.llLoad.llLoad.visibility = View.VISIBLE
        appInfoViewModel.getAppPackage(this)
    }

    private fun initAd(){
        if (AdConfig.getSwitchAdInters() && AdConfig.getAllowShowInterstitial()){
            maxScreenAdManager = MaxScreenAdManager(true).apply {
                initAd()
                adLoad(this@AppBrowActivity)
            }
            maxScreenAdManager?.startWaitingJob(this@AppBrowActivity,
                start = {mBindingView.llLoad.llLoad.visibility = View.VISIBLE},
                next = {
                    if(maxScreenAdManager?.showAd(this@AppBrowActivity) == true){
                        mBindingView.llLoad.llLoad.visibility = View.GONE
                    }
                },
                end = {
                    mBindingView.llLoad.llLoad.visibility = View.GONE
                    loadFinish = true
                })
        }else{
            loadFinish = true
        }
    }

    override fun finish() {
        LiveEventBus.get<String>(EventData.EVENT_RETURN_BACK).post("")
        super.finish()
    }

    override fun onDestroy() {
        maxScreenAdManager?.adDestroy()
        super.onDestroy()
    }

}