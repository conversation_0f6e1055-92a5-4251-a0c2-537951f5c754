package com.example.newdc.content.view

import android.content.Context
import android.graphics.*
import android.util.AttributeSet
import android.view.View
import com.example.newdc.modelutil.tool.ExtraTool.dp2px
import kotlin.math.min


class RamCureView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
    degStyleRes: Int = 0
) :
    View(context, attrs, defStyleAttr, degStyleRes) {

    companion object {
        private const val MAX_POINTS = 6
    }

    private val textSize: Float = 18f
    private val yPoints: ArrayList<Float> = arrayListOf()

    private var paint: Paint
    private val path: Path = Path()

    init {
        // 初始化画笔和路径
        paint = Paint().apply {
            color = Color.parseColor("#FF80D5FF")
            style = Paint.Style.STROKE
            isAntiAlias = true
            strokeJoin = Paint.Join.MITER
            strokeCap = Paint.Cap.ROUND
            strokeWidth = 5f.dp2px
            setShadowLayer(8f, 0f, 7f, Color.parseColor("#8D5381FF"))
        }
    }


    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        val shader = LinearGradient(0f, 0f, width.toFloat(), height.toFloat(),
            Color.parseColor("#FF80D5FF"), Color.parseColor("#FF386BFD"), Shader.TileMode.CLAMP) // 边缘处理模式
        paint.setShader(shader)
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        path.reset()
        if (yPoints.size > 0) {
            val xStep = (width - paddingRight - paddingLeft - getTextDrawSize()) / MAX_POINTS
            val swing = getAvg2() / getHeightPercent() / 2
            path.moveTo(paddingRight.toFloat(), swing)
            yPoints.forEachIndexed { index, value ->
                val x = xStep * (index + 1)
                val y = getYPoint(value, swing)
                path.lineTo(x, y)
            }
        }
        canvas.drawPath(path, paint)
    }

    private fun getYPoint(point: Float, swing: Float): Float {
        val percent = getHeightPercent()
        return height - (swing + (point / percent / 2 - swing) * percent * 2.4f)
    }


    private fun getTextDrawSize(): Float {
        return textSize.dp2px
    }

    private fun getHeightPercent(): Float {
        val max = yPoints.max()
        val min = yPoints.min()
        return (max + min) / 2f / height
    }

    private fun getAvg2(): Float {
        val max = yPoints.max()
        val min = yPoints.min()
        return (max + min) / 2f
    }

    fun addPoint(y: Float) {
        if (yPoints.size >= MAX_POINTS) {
            yPoints.removeAt(0)
        }
        yPoints.add(y)
        invalidate()
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        val widthMode = MeasureSpec.getMode(widthMeasureSpec)
        val widthSize = MeasureSpec.getSize(widthMeasureSpec)

        var width = 0
        if (widthMode == MeasureSpec.EXACTLY) {
            width = widthSize
        } else {
            val needWidth = measuredWidth + paddingLeft + paddingRight
            width = if (widthMode == MeasureSpec.AT_MOST) {
                min(needWidth.toDouble(), widthSize.toDouble()).toInt()
            } else {
                needWidth
            }
        }

        val heightMode = MeasureSpec.getMode(heightMeasureSpec)
        val heightSize = MeasureSpec.getSize(heightMeasureSpec)
        var height = 0

        if (heightMode == MeasureSpec.EXACTLY) {
            height = heightSize
        } else {
            val needHeight = measuredHeight + paddingTop + paddingBottom
            height = if (heightMode == MeasureSpec.AT_MOST) {
                min(needHeight.toDouble(), heightSize.toDouble()).toInt()
            } else  //MeasureSpec.UNSPECIFIED
            {
                needHeight
            }
        }
        setMeasuredDimension(width, height)
    }
}