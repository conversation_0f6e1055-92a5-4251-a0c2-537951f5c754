package com.example.newdc.content.fragment.browser

import android.os.Environment
import android.util.Log
import android.view.View
import android.widget.LinearLayout
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.example.newdc.R
import com.example.newdc.content.view.CurrentPathView
import com.example.newdc.content.view.dialog.OrderPopDialog
import com.example.newdc.dataadapter.BaseFileBrowserAdapter
import com.example.newdc.dataadapter.FileItemAdapter
import com.example.newdc.databinding.FragmentBrowDirBinding
import com.example.newdc.modelutil.data.EventData
import com.example.newdc.modelutil.data.ItemFileData
import com.example.newdc.modelutil.tool.MediaStorageTool
import com.example.newdc.modelutil.tool.OrderTool
import com.jeremyliao.liveeventbus.LiveEventBus

class FragmentBrowserDir: BaseBrowserFragment<FragmentBrowDirBinding>() {
    private var currentPath: String? = null
    private var orderType = OrderTool.TYPE_ORDER_NEW

    override fun layoutResource(): Int {
        return R.layout.fragment_brow_dir
    }

    override fun setFileAdapter(): BaseFileBrowserAdapter {
        return FileItemAdapter()
    }

    override fun viewCreated() {
        setCurrentTitle()
        setListener()
        changeTitlePath(Environment.getExternalStorageDirectory().absolutePath)
    }

    private fun setListener() {
        itemAdapter!!.setItemListener(object : BaseFileBrowserAdapter.BaseItemClickListener {
            override fun onItemClick(item: ItemFileData) {
                if (!itemAdapter!!.barOpen){
                    if (item.isDir){
                        changeTitlePath(item.filePath)
                    }else{
                        MediaStorageTool.fileOpen(requireContext(), item.filePath, item.mime, item.type)
                    }
                }
                itemClickListener?.onClick(itemAdapter!!.getAllSelectItems())
            }

            override fun onItemLongClick(barOpen: Boolean) {
                itemClickListener?.onLongClick(barOpen,itemAdapter!!.getAllSelectItems())
            }

        })
        mBindingView.rvDir.apply {
            layoutManager = LinearLayoutManager(context, RecyclerView.VERTICAL, false)
            adapter = itemAdapter
        }

        mBindingView.llLoad.apply {
            tvLoad.visibility = View.GONE
            proLoad.visibility = View.GONE
            proLoadR.visibility = View.VISIBLE
        }

        filesStorageViewModel.getBrowserDirList.observe(this){
            mBindingView.llLoad.llLoad.visibility = View.GONE
            OrderTool.orderFileType(orderType,it)
            itemAdapter?.setData(it)
            if (it.isNotEmpty()){
                mBindingView.llEmpty.visibility = View.GONE
            }else{
                mBindingView.llEmpty.visibility = View.VISIBLE
            }
        }

        LiveEventBus.get<String>(EventData.EVENT_DIR_ENTER).observe(this) {
            itemAdapter?.checkAllFilterItem(false)
            changeTitlePath(it)
        }

        LiveEventBus.get<String>(EventData.EVENT_FILE_CHANGED).observe(this) {
            if (currentPath != null){
                changeTitlePath(currentPath!!)
            }
        }

        mBindingView.imgOrder.setOnClickListener {
            OrderPopDialog(orderType){
                orderType = it
                itemAdapter?.getFilterItems()?.let { it1 -> OrderTool.orderFileType(it, it1) }
                itemAdapter?.notifyDataSetChanged()
            }.show(childFragmentManager,"order")
        }
    }

    private fun setCurrentTitle() {
        mBindingView.currentTitle.apply {
            orientation = LinearLayout.HORIZONTAL
            setItemClickListener(object : CurrentPathView.ItemClickListener {
                override fun click(path: String?) {
                    if (path != null) {
                        changeTitlePath(path)
                    }
                }
            })
        }
    }

    fun changeTitlePath(currentPath: String) {
        this.currentPath = currentPath
        mBindingView.llLoad.llLoad.visibility = View.VISIBLE
        filesStorageViewModel.getSelectPathFiles(currentPath)
        mBindingView.currentTitle.getTitleCurrent(currentPath)
    }

    fun getTitlePath(): MutableList<String> {
        return mBindingView.currentTitle.getPathList()
    }

}