package com.example.newdc.content.fragment.info

import android.content.Context
import android.content.Intent
import android.view.View
import com.additional.max.AdConfig
import com.additional.max.MaxScreenAdManager
import com.example.newdc.R
import com.example.newdc.content.BaseLayoutActivity
import com.example.newdc.databinding.ActivityBaseInfoBinding
import com.example.newdc.modelutil.data.EventData
import com.jeremyliao.liveeventbus.LiveEventBus


class ActivityBaseInfo: BaseLayoutActivity<ActivityBaseInfoBinding>() {
    private var fragmentType = 0
    private var maxScreenAdManager: MaxScreenAdManager? = null

    companion object{
        const val INFO_TYPE = "info_type"
        fun startDevInfoActivity(context: Context,type:Int){
            val intent = Intent(context, ActivityBaseInfo::class.java)
            intent.putExtra(INFO_TYPE,type)
            try {
                context.startActivity(intent)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    override fun layoutResource(): Int {
        return R.layout.activity_base_info
    }

    private fun initAd(){
        if (AdConfig.getSwitchAdInters() && AdConfig.getAllowShowInterstitial()){
            maxScreenAdManager = MaxScreenAdManager(true).apply {
                initAd()
                adLoad(this@ActivityBaseInfo)
            }
            maxScreenAdManager?.startWaitingJob(this,
                start = {mBindingView.llLoad.llLoad.visibility = View.VISIBLE},
                next = {if (maxScreenAdManager?.showAd(this@ActivityBaseInfo) == true){
                    mBindingView.llLoad.llLoad.visibility = View.GONE
                } },
                end = {mBindingView.llLoad.llLoad.visibility = View.GONE}
            )
        }
    }

    override fun viewCreated() {
        fragmentType = intent.getIntExtra(INFO_TYPE,0)
        val typeFragment = when (fragmentType) {
            1 -> {
                mBindingView.llTitleBar.tvTitle.text = getString(R.string.title_device)
                FragmentInfoDevice()
            }
            2 -> {
                mBindingView.llTitleBar.tvTitle.text = getString(R.string.title_system)
                FragmentInfoSys()
            }
            3 -> {
                mBindingView.llTitleBar.tvTitle.text = getString(R.string.title_display)
                FragmentInfoDisplay()
            }
            4 -> {
                mBindingView.llTitleBar.tvTitle.text = getString(R.string.title_network)
                FragmentInfoNetwork()
            }
            5 -> {
                mBindingView.llTitleBar.tvTitle.text = getString(R.string.title_cpu)
                FragmentInfoCpu()
            }
            6 -> {
                mBindingView.llTitleBar.tvTitle.text = getString(R.string.title_battery)
                FragmentInfoBattery()
            }
            else -> FragmentInfoDevice()
        }
        supportFragmentManager.beginTransaction()
            .add(R.id.container_fragment, typeFragment)
            .commit()
        initAd()
    }

    override fun onDestroy() {
        maxScreenAdManager?.adDestroy()
        super.onDestroy()
    }

    override fun finish() {
        LiveEventBus.get<String>(EventData.EVENT_RETURN_BACK).post("")
        super.finish()
    }
}