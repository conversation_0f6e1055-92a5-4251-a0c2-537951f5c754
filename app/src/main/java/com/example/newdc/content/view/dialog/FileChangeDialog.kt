package com.example.newdc.content.view.dialog

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.FragmentManager
import com.example.newdc.R
import com.example.newdc.databinding.DialogFileChangeBinding
import com.example.newdc.modelutil.data.ItemFileData
import com.example.newdc.modelutil.tool.FilesTool
import com.example.newdc.modelutil.tool.MediaStorageTool
import java.io.File
import java.util.ArrayList

class FileChangeDialog(private val clickCall: () -> Unit = {}): BaseDialogFragment<DialogFileChangeBinding>() {
    private var dialogType: Int = 0
    private var fileItem: ArrayList<ItemFileData> = arrayListOf()

    override fun getBaseLayoutRes(): Int {
        return  R.layout.dialog_file_change
    }

    companion object{
        const val STRING_FILE_TYPE = "string_file_type"
        const val STRING_FILE_ITEM = "string_file_item"

        const val TYPE_FILE_RENAME = 0
        const val TYPE_FILE_DELETE = 1

        fun showFileChangeDialog(fragmentManager: FragmentManager, type: Int, items: MutableList<ItemFileData>,clickCall: () -> Unit = {}) {
            val dialog = FileChangeDialog(clickCall).apply {
                arguments = Bundle().apply {
                    putInt(STRING_FILE_TYPE, type)
                    putParcelableArrayList(STRING_FILE_ITEM, ArrayList(items))
                }
            }
            dialog.show(fragmentManager,type.toString())
        }
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        dialogType = arguments?.getInt(STRING_FILE_TYPE) ?: 0
        fileItem = arguments?.getParcelableArrayList(STRING_FILE_ITEM) ?: arrayListOf()
        return super.onCreateView(inflater, container, savedInstanceState)
    }


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        if (dialogType == TYPE_FILE_RENAME){
            mDataBinding.tvTitle.text = getString(R.string.dia_rename)
            mDataBinding.btnYes.text = getString(R.string.dia_save)
            mDataBinding.btnCancel.text = getString(R.string.dia_cancel)

            if (fileItem.size > 0){
                val pathTitle = fileItem[0].filePath
                val nameTitle = pathTitle.substring(pathTitle.lastIndexOf("/") + 1,pathTitle.length)
                mDataBinding.editor.setText(nameTitle)
            }
        }else if (dialogType == TYPE_FILE_DELETE){
            mDataBinding.editor.visibility = View.GONE
            mDataBinding.tvDetail.visibility = View.VISIBLE
            mDataBinding.tvTitle.text = getString(R.string.base_delete)
            mDataBinding.btnYes.text = getString(R.string.base_yes)
            mDataBinding.btnCancel.text = getString(R.string.base_no)
        }

        mDataBinding.btnCancel.setOnClickListener {
            dismissAllowingStateLoss()
        }

        mDataBinding.btnYes.setOnClickListener {
            if (dialogType == TYPE_FILE_RENAME){
                val newPath = mDataBinding.editor.text.toString()
                if (newPath.isNotBlank() && fileItem.size > 0){
                    FilesTool.filePathRename(File(fileItem[0].filePath),newPath)
                    MediaStorageTool.updateFilePath(fileItem[0].filePath)
                }
            }else if (dialogType == TYPE_FILE_DELETE){
                fileItem.forEach {
                    FilesTool.deletePath(it.filePath)
                    MediaStorageTool.updateFilePath(it.filePath)
                }
            }
            clickCall()
            dismissAllowingStateLoss()
        }

    }
}