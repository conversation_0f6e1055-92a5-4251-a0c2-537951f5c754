package com.example.newdc.content

import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.View
import android.widget.FrameLayout
import android.widget.ImageView
import androidx.appcompat.app.AppCompatActivity
import androidx.databinding.DataBindingUtil
import androidx.databinding.ViewDataBinding
import com.additional.firebase.FirebaseEventPost
import com.additional.firebase.InstanceTool
import com.additional.max.AdConfig
import com.additional.max.AdStatusListener
import com.additional.max.MaxBannerAdView
import com.additional.max.MaxNativeAdManager
import com.applovin.mediation.MaxAd
import com.applovin.sdk.AppLovinSdk
import com.example.newdc.BaseApplication
import com.example.newdc.R
import com.example.newdc.content.activity.*
import com.example.newdc.content.fragment.info.ActivityBaseInfo
import com.example.newdc.content.view.dialog.RequestDialog
import com.example.newdc.modelutil.data.EventData
import com.example.newdc.modelutil.tool.ExtraTool
import com.example.newdc.modelutil.tool.PermissionTool
import com.example.newdc.service.notify.TopConstantService
import com.jeremyliao.liveeventbus.LiveEventBus

abstract class BaseLayoutActivity<V: ViewDataBinding>: AppCompatActivity() {
    lateinit var mBindingView : V
    protected var navBack: ImageView? = null
    private var enterType = -1
    private var llAdNative: FrameLayout? = null
    private var llBanner: MaxBannerAdView? = null
    private var maxAdNativeManager: MaxNativeAdManager? = null
    private var adIsLoad = false

    abstract fun layoutResource(): Int
    abstract fun viewCreated()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        mBindingView = DataBindingUtil.setContentView(this, layoutResource())
        changeStatusBarHind()

        llAdNative = mBindingView.root.findViewById(R.id.ll_native)
        if (llAdNative != null){
            llAdNative!!.visibility = if (changeHideNative()) View.GONE else View.VISIBLE
        }

        llBanner = mBindingView.root.findViewById(R.id.ad_banner)
        if (llBanner != null){
            llBanner!!.visibility = if (changeBanner()) View.GONE else View.VISIBLE
        }

        navBack = mBindingView.root.findViewById<ImageView?>(R.id.nav_back)?.apply {
            setOnClickListener { super.onBackPressed() }
        }

        LiveEventBus.get<String>(EventData.EVENT_AD_INIT).observe(this){
            initAd()
        }
        initAd()
        viewCreated()
    }

    private fun changeStatusBarHind() {
        ExtraTool.setStatusHind(this)
    }

    fun startBrowserActivity(type: Int){
        enterType = type
        if (PermissionTool.allowStoragePermission(this)){
            enterActivityByType(type)
        }else{
            RequestDialog(RequestDialog.TYPE_STORAGE,{
                PermissionTool.permissionGrantStorage(this)
            }).show(supportFragmentManager,type.toString())
        }
    }

    private fun enterActivityByType(type: Int){
        if (type == -1) return
        when(type){
            BaseBrowserActivity.TYPE_BROWSER_REDUNDANT ->{
                startActivity(Intent(this, FileRedundantActivity::class.java))
            }
            else ->{
                BaseBrowserActivity.enterBrowserTypeActivity(this,type)
            }
        }
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode == PermissionTool.PERMISSION_STORAGE_MANAGER_CODE && PermissionTool.allowStoragePermission(this)){
            enterActivityByType(enterType)
            enterType = -1
            finish()
        }
        if (requestCode == PermissionTool.PERMISSION_POST_NOTIFY_CODE && Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU &&
            PermissionTool.checkPostNotifyPermission(this)){
            FirebaseEventPost.event("per_notify_get")
            TopConstantService.startConstantNotify(BaseApplication.mAppContext)
//            InstanceTool.setSubscribe(this)
        }
        if (requestCode == PermissionTool.PERMISSION_OPTIMIZATION_CODE && PermissionTool.allowBatteryOptimization(this)){
            FirebaseEventPost.event("per_opt_get")
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == PermissionTool.PERMISSION_STORAGE_MANAGER_CODE && PermissionTool.allowStoragePermission(this)
            && enterType != -1){
            enterActivityByType(enterType)
            enterType = -1
            finish()
        }
        if (requestCode == PermissionTool.PERMISSION_POST_NOTIFY_CODE && Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU &&
            PermissionTool.checkPostNotifyPermission(this)){
            FirebaseEventPost.event("per_notify_get")
            TopConstantService.startConstantNotify(BaseApplication.mAppContext)
//            InstanceTool.setSubscribe(this)
        }
        if (requestCode == PermissionTool.PERMISSION_OPTIMIZATION_CODE && PermissionTool.allowBatteryOptimization(this)){
            FirebaseEventPost.event("per_opt_get")
        }
    }

    private fun initNative(){
        if (llAdNative?.visibility == View.VISIBLE){
            maxAdNativeManager = MaxNativeAdManager(this).apply {
                setOnAdListener(object : AdStatusListener() {
                    override fun onAdLoaded(ad: MaxAd) {
                    }
                })
                adLoad(llAdNative!!)
            }
        }
    }

    private fun initAd() {
        Handler(Looper.getMainLooper()).postDelayed({
            if (AppLovinSdk.getInstance(BaseApplication.mAppContext).isInitialized && !adIsLoad){
                adIsLoad = true
                initBanner()
                initNative()
            }
        },100)
    }

    private fun initBanner(){
        if (llBanner?.visibility == View.VISIBLE){
            llBanner?.addBannerView()
        }
    }

    private fun changeHideNative(): Boolean{
        return if (!AdConfig.getSwitchUniqueNative()){
            (this is StorageActivity || this is RealtimeActivity || this is FileRedundantActivity
                    || this is BaseBrowserActivity || this is TestActivity || this is AppBrowActivity)
        }else{
            false
        }
    }

    private fun changeBanner(): Boolean{
        return if (!AdConfig.getSwitchUniqueBanner()){
            (this is ActivityBaseInfo || this is CompleteActivity)
        }else{
            false
        }
    }

    override fun onResume() {
        llBanner?.resume()
        super.onResume()
    }

    override fun onPause() {
        llBanner?.stop()
        super.onPause()
    }

    override fun onDestroy() {
        maxAdNativeManager?.onDestroy()
        llBanner?.destroy()
        super.onDestroy()
    }
}