package com.example.newdc.content.fragment

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.databinding.DataBindingUtil
import androidx.databinding.ViewDataBinding
import androidx.fragment.app.Fragment
import com.additional.max.AdConfig
import com.additional.max.AdStatusListener
import com.additional.max.MaxNativeAdManager
import com.applovin.mediation.MaxAd
import com.applovin.sdk.AppLovinSdk
import com.example.newdc.BaseApplication
import com.example.newdc.R
import com.example.newdc.modelutil.data.EventData
import com.jeremyliao.liveeventbus.LiveEventBus

abstract class BaseLayoutFragment<V: ViewDataBinding>: Fragment() {
    protected lateinit var mBindingView: V
    private var llAdNative: FrameLayout? = null
    private var maxAdNativeManager: MaxNativeAdManager? = null
    private var adIsLoad = false


    abstract fun layoutResource(): Int
    abstract fun viewCreated()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        mBindingView = DataBindingUtil.inflate(inflater, layoutResource(), container, false)
        llAdNative = mBindingView.root.findViewById(R.id.ll_native)
        if (llAdNative != null){
            llAdNative!!.visibility = if (changeHideNative()) View.GONE else View.VISIBLE
            initNative()
        }
        return mBindingView.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        LiveEventBus.get<String>(EventData.EVENT_AD_INIT).observe(this){
            if (llAdNative != null){
                llAdNative!!.visibility = if (changeHideNative()) View.GONE else View.VISIBLE
                initNative()
            }
        }
        viewCreated()
    }

    private fun initNative(){
        if (AppLovinSdk.getInstance(BaseApplication.mAppContext).isInitialized && !adIsLoad){
            adIsLoad = true
            if (llAdNative?.visibility == View.VISIBLE){
                maxAdNativeManager = MaxNativeAdManager(requireContext()).apply {
                    setOnAdListener(object : AdStatusListener() {
                        override fun onAdLoaded(ad: MaxAd) {
                        }
                    })
                    adLoad(llAdNative!!)
                }
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        maxAdNativeManager?.onDestroy()
    }

    private fun changeHideNative(): Boolean{
        return if (!AdConfig.getSwitchUniqueNative()){
            (this is FragmentSetting || this is FragmentMore || this is FragmentHome)
        }else{
            false
        }
    }
}