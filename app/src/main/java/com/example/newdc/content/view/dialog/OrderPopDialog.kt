package com.example.newdc.content.view.dialog

import android.graphics.Color
import android.os.Bundle
import android.view.Gravity
import android.view.View
import android.view.WindowManager
import android.widget.TextView
import com.example.newdc.R
import com.example.newdc.databinding.DialogOrderPopBinding
import com.example.newdc.modelutil.tool.OrderTool


class OrderPopDialog(private val oldType: Int,val call: (type: Int) -> Unit): BaseDialogFragment<DialogOrderPopBinding>() {

    override fun onStart() {
        super.onStart()
        val dialog = dialog
        if (dialog != null) {
            val window = dialog.window
            val windowParams = window!!.attributes
            windowParams.width = WindowManager.LayoutParams.MATCH_PARENT
            windowParams.height = WindowManager.LayoutParams.WRAP_CONTENT
            windowParams.flags = WindowManager.LayoutParams.FLAG_DIM_BEHIND or WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS
            windowParams.dimAmount = 0.8f
            windowParams.gravity = Gravity.BOTTOM
            window.attributes = windowParams
        }
    }

    override fun getBaseLayoutRes(): Int {
        return R.layout.dialog_order_pop
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        changeView()
        mDataBinding.imgClose.setOnClickListener {
            dismissAllowingStateLoss()
        }

        mDataBinding.tvNew.setOnClickListener {
            call.invoke(OrderTool.TYPE_ORDER_NEW)
            dismissAllowingStateLoss()
        }

        mDataBinding.tvOld.setOnClickListener {
            call.invoke(OrderTool.TYPE_ORDER_OLD)
            dismissAllowingStateLoss()
        }

        mDataBinding.tvLarge.setOnClickListener {
            call.invoke(OrderTool.TYPE_ORDER_LARGE)
            dismissAllowingStateLoss()
        }

        mDataBinding.tvSmall.setOnClickListener {
            call.invoke(OrderTool.TYPE_ORDER_SMALL)
            dismissAllowingStateLoss()
        }

        mDataBinding.tvAtoZ.setOnClickListener {
            call.invoke(OrderTool.TYPE_ORDER_A_Z)
            dismissAllowingStateLoss()
        }

        mDataBinding.tvZtoA.setOnClickListener {
            call.invoke(OrderTool.TYPE_ORDER_Z_A)
            dismissAllowingStateLoss()
        }
    }

    private fun changeView(){
        val count = mDataBinding.llOrderList.childCount
        for (i in 0 until count) {
            val child = mDataBinding.llOrderList.getChildAt(i)
            if (child is TextView) {
                if (oldType == i){
                    child.setBackgroundColor(Color.parseColor("#FFF3F0FF"))
                    child.setTextColor(Color.parseColor("#FF8267F7"))
                }else{
                    child.setBackgroundColor(Color.WHITE)
                    child.setTextColor(Color.parseColor("#FF333333"))
                }
            }
        }
    }
}