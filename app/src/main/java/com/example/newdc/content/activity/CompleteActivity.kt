package com.example.newdc.content.activity

import android.content.Context
import android.content.Intent
import androidx.recyclerview.widget.GridLayoutManager
import com.example.newdc.R
import com.example.newdc.content.BaseLayoutActivity
import com.example.newdc.dataadapter.MoreFuncAdapter
import com.example.newdc.databinding.ActivityCompleteBinding
import com.example.newdc.modelutil.data.EventData
import com.example.newdc.modelutil.data.MoreFuncData
import com.jeremyliao.liveeventbus.LiveEventBus

class CompleteActivity: BaseLayoutActivity<ActivityCompleteBinding>() {
    private var enterType = 0

    override fun layoutResource(): Int {
        return R.layout.activity_complete
    }

    companion object{
        const val ENTER_TYPE = "ENTER_TYPE"
        fun enterCompleteActivity(context: Context,type:Int){
            val intent = Intent(context, CompleteActivity::class.java).apply {
                putExtra(ENTER_TYPE,type)
            }
            try {
                context.startActivity(intent)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    override fun viewCreated() {
        enterType = intent.getIntExtra(ENTER_TYPE,0)
        mBindingView.titleBar.tvTitle.text = getString(R.string.title_complete)
        val funcAdapter = MoreFuncAdapter(MoreFuncData.getCompleteFuncList()){
            when (it) {
                BaseBrowserActivity.TYPE_FUNC_RAM -> {
                    startActivity(Intent(this,RealtimeActivity::class.java))
                }
                BaseBrowserActivity.TYPE_FUNC_STORAGE -> {
                    startActivity(Intent(this,StorageActivity::class.java))}
                else -> {startBrowserActivity(it)}
            }
            finish()
        }
        mBindingView.rvMore.apply {
            adapter = funcAdapter
            layoutManager = GridLayoutManager(this@CompleteActivity,2)
        }

    }

    override fun onDestroy() {
        super.onDestroy()
        LiveEventBus.get<String>(EventData.EVENT_RETURN_BACK).post("")
    }
}