package com.example.newdc.content.fragment.info

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.net.ConnectivityManager
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.view.View
import android.widget.LinearLayout
import androidx.lifecycle.ViewModelProvider
import com.example.newdc.R
import com.example.newdc.content.fragment.BaseLayoutFragment
import com.example.newdc.databinding.FragmentInfoNetworkBinding
import com.example.newdc.modelutil.DevInfoViewModel
import com.example.newdc.modelutil.data.EventData
import com.example.newdc.modelutil.dev.NetTool
import com.jeremyliao.liveeventbus.LiveEventBus
import java.util.*

class FragmentInfoNetwork: BaseLayoutFragment<FragmentInfoNetworkBinding>() {
    private lateinit var devViewModel: DevInfoViewModel
    private lateinit var timer: Timer


    override fun layoutResource(): Int {
        return R.layout.fragment_info_network
    }

    override fun onStart() {
        super.onStart()
        startTimer()
        devViewModel.getMobileInfoData(requireContext())
        devViewModel.getWifiInfoData(requireContext())
    }

    override fun onStop() {
        super.onStop()
        if(::timer.isInitialized){
            timer.cancel()
        }
        try {
            requireContext().unregisterReceiver(connectChangeReceiver)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    override fun viewCreated() {
        devViewModel = ViewModelProvider(this)[DevInfoViewModel::class.java]
        mBindingView.devData = devViewModel
        mBindingView.lifecycleOwner = this

        devViewModel.getWifiSignal.observe(this){
            mBindingView.tvDbm.text = it
        }
        devViewModel.getWifiSignalPer.observe(this){
            mBindingView.tvDbmPer.text = it
        }

        LiveEventBus.get<Boolean>(EventData.EVENT_CONNECT_CHANGE).observe(this){
            devViewModel.getWifiInfoData(requireContext())
        }
        val filter = IntentFilter(ConnectivityManager.CONNECTIVITY_ACTION)
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE){
                requireContext().registerReceiver(connectChangeReceiver,filter,Context.RECEIVER_NOT_EXPORTED)
            }else {
                requireContext().registerReceiver(connectChangeReceiver,filter)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun startTimer(){
        timer = Timer()
        timer.schedule(object : TimerTask() {
            override fun run() {
                Handler(Looper.getMainLooper()).post(Runnable {
                    changeInfoView()
                    if (NetTool.getWifiConnectStatus(requireContext())){
                        mBindingView.tvModel.text = getString(R.string.base_wifi)
                        mBindingView.imgDev.setImageResource(R.mipmap.icon_wifi)
                        devViewModel.getWifiSignal(requireContext())
                        mBindingView.llSignal.visibility = View.VISIBLE
                    }else if (NetTool.getMobileConnectStatus(requireContext())){
                        mBindingView.imgDev.setImageResource(R.mipmap.icon_signal)
                        mBindingView.tvModel.text = getString(R.string.base_mobile_data)
                        mBindingView.llSignal.visibility = View.GONE
                    }else{
                        mBindingView.tvModel.text = getString(R.string.base_no_connect)
                        mBindingView.llSignal.visibility = View.GONE
                    }
                })

            }
        },0,1000)
    }

    fun changeInfoView(){
        if (NetTool.getWifiConnectStatus(requireContext())){
            hideProFromLinear(mBindingView.llWifi,-1)
        }else{
            hideProFromLinear(mBindingView.llWifi,1)
        }

        if (NetTool.getMobileConnectStatus(requireContext())){
            hideProFromLinear(mBindingView.llMobile,-1)
        }else{
            hideProFromLinear(mBindingView.llMobile,2)
        }
    }

    private fun hideProFromLinear(linear: LinearLayout, hideCount: Int = 2){
        val count = linear.childCount
        for (i in 0 until count){
            val child = linear.getChildAt(i)
            if (child is LinearLayout){
                if (hideCount < 0){
                    child.visibility = View.VISIBLE
                }else if (i > hideCount){
                    child.visibility = View.GONE
                }
            }
        }
    }

    private val connectChangeReceiver = object :BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            if (intent?.action == ConnectivityManager.CONNECTIVITY_ACTION) {
                val connectivityManager = context?.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
                val networkInfo = connectivityManager.activeNetworkInfo
                val isConnected = networkInfo?.isConnected ?: false
                LiveEventBus.get<Boolean>(EventData.EVENT_CONNECT_CHANGE).post(isConnected)
            }
        }
    }
}