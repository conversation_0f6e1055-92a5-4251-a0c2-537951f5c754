package com.example.newdc.content.fragment.info

import androidx.lifecycle.ViewModelProvider
import com.example.newdc.R
import com.example.newdc.content.fragment.BaseLayoutFragment
import com.example.newdc.databinding.FragmentInfoDevBinding
import com.example.newdc.databinding.FragmentInfoSysBinding
import com.example.newdc.modelutil.DevInfoViewModel

class FragmentInfoDevice: BaseLayoutFragment<FragmentInfoDevBinding>() {
    private lateinit var devViewModel: DevInfoViewModel


    override fun layoutResource(): Int {
        return R.layout.fragment_info_dev
    }

    override fun viewCreated() {
        devViewModel = ViewModelProvider(this)[DevInfoViewModel::class.java]
        mBindingView.devData = devViewModel
        mBindingView.lifecycleOwner = this
        devViewModel.getDeviceInfoData(requireContext())
    }
}