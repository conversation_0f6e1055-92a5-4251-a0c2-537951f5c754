package com.example.newdc.content.view

import android.content.Context
import android.graphics.Color
import android.opengl.GLSurfaceView
import android.util.AttributeSet
import android.widget.FrameLayout
import com.example.newdc.modelutil.dev.ScreenTool
import javax.microedition.khronos.egl.EGLConfig
import javax.microedition.khronos.opengles.GL10

class GpuSurfaceView(context: Context, attrs: AttributeSet? = null): FrameLayout(context, attrs),GLSurfaceView.Renderer {
    private var gLSurfaceView: GLSurfaceView? = null
    private var initCall: (() -> Unit)? = null

    //GPU 渲染器
    var surRenderer: String? = null

    //GPU 供应商
    var surVendor: String? = null

    //GPU 版本
    var surVersion: String? = null

    //GPU  扩展名
    var surExtensions: String? = null

    init {
        gLSurfaceView = GLSurfaceView(getContext())
        gLSurfaceView?.setRenderer(this)
        gLSurfaceView?.requestRender()
        gLSurfaceView?.setBackgroundColor(Color.WHITE)
        addView(gLSurfaceView)
    }

    override fun onSurfaceCreated(gl: GL10?, config: EGLConfig?) {
        surRenderer = gl?.glGetString(GL10.GL_RENDERER)
        surVendor = gl?.glGetString(GL10.GL_VENDOR)
        surVersion = "${ScreenTool.findOpenGlVersion(context)} ${gl?.glGetString(GL10.GL_VERSION)}"
        surExtensions = gl?.glGetString(GL10.GL_EXTENSIONS)

        initCall?.let { it() }
    }

    override fun onSurfaceChanged(gl: GL10?, width: Int, height: Int) {
    }

    override fun onDrawFrame(gl: GL10?) {
    }

    fun setInitListener(call : () -> Unit){
        initCall = call
    }

}