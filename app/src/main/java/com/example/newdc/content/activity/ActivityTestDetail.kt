package com.example.newdc.content.activity

import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.hardware.Sensor
import android.view.KeyEvent
import android.view.View
import androidx.lifecycle.ViewModelProvider
import com.example.newdc.R
import com.example.newdc.content.BaseLayoutActivity
import com.example.newdc.databinding.ActivityTestDetailBinding
import com.example.newdc.modelutil.data.StoreKeyData
import com.example.newdc.modelutil.data.TestData
import com.example.newdc.modelutil.TestTypeViewModel
import com.example.newdc.modelutil.data.EventData
import com.jeremyliao.liveeventbus.LiveEventBus

class ActivityTestDetail: BaseLayoutActivity<ActivityTestDetailBinding>() {
    private lateinit var testTypeViewModel: TestTypeViewModel
    private var testType: TestData? = null
    private val colorList = listOf(Color.RED,Color.GREEN,Color.BLUE,Color.WHITE,Color.BLACK)
    private var colorIndex = 0

    override fun layoutResource(): Int {
        return R.layout.activity_test_detail
    }

    companion object{
        fun enterTestDetailActivity(context: Context, testType: TestData){
            val intent = Intent(context, ActivityTestDetail::class.java).apply {
                putExtra("testType", testType)
            }
            try {
                context.startActivity(intent)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    override fun viewCreated() {
        testTypeViewModel = ViewModelProvider(this)[TestTypeViewModel::class.java]
        testType = intent.getParcelableExtra("testType")
        getTestTypePage()
        getListener()
    }

    private fun getListener() {
        testTypeViewModel.getLightLx.observe(this){
            mBindingView.tvLx.text = "$it lx"
        }
        testTypeViewModel.getProximityStatus.observe(this){
            mBindingView.imgTestType.setImageResource(if (it) R.mipmap.test_ear3 else R.mipmap.test_ear1)
        }
    }

    private fun getTestTypePage() {
        if (testType != null){
            mBindingView.titleBar.tvTitle.text = getString(testType!!.testName)
            mBindingView.tvTipTitle.text = getString(testType!!.testDescribe)
            when(testType!!.testIntent){
                StoreKeyData.TEST_VALUE_COLOR ->{
                    mBindingView.imgTestType.visibility = View.GONE
                    mBindingView.llBack.setBackgroundColor(colorList[0])
                    mBindingView.llBack.setOnClickListener {
                        if (colorIndex == colorList.size){colorIndex = 0}
                        if (colorIndex < colorList.size){
                            mBindingView.llBack.setBackgroundColor(colorList[colorIndex])
                            colorIndex ++
                        }
                    }
                }
                StoreKeyData.TEST_VALUE_TOUCH ->{
                    mBindingView.imgTestType.visibility = View.GONE
                    mBindingView.svTouch.visibility = View.VISIBLE
                }
                StoreKeyData.TEST_VALUE_FLASHLIGHT ->{
                    mBindingView.imgTestType.setImageResource(R.mipmap.test_flashlight)
                    testTypeViewModel.flashOpen(this)
                }
                StoreKeyData.TEST_VALUE_SPEAKER ->{
                    mBindingView.imgTestType.setImageResource(R.mipmap.test_ear)
                    testTypeViewModel.earSoundPlay(this,false)
                }
                StoreKeyData.TEST_VALUE_LOUD ->{
                    mBindingView.imgTestType.setImageResource(R.mipmap.test_loudspeaker)
                    testTypeViewModel.earSoundPlay(this,true)
                }
                StoreKeyData.TEST_VALUE_PROXIMITY ->{
                    mBindingView.imgTestType.setImageResource(R.mipmap.test_ear1)
                    testTypeViewModel.getSensorValue(this,Sensor.TYPE_PROXIMITY)
                }
                StoreKeyData.TEST_VALUE_LIGHT ->{
                    mBindingView.imgTestType.setImageResource(R.mipmap.test_light)
                    mBindingView.tvLx.visibility = View.VISIBLE
                    testTypeViewModel.getSensorValue(this,Sensor.TYPE_LIGHT)
                }
                StoreKeyData.TEST_VALUE_VIBRATION ->{
                    mBindingView.imgTestType.setImageResource(R.mipmap.test_vibration)
                    testTypeViewModel.sensorVibStart(this)
                }
                StoreKeyData.TEST_VALUE_VOLUME_UP ->{
                    mBindingView.imgTestType.setImageResource(R.mipmap.test_vol_up)
                }
                StoreKeyData.TEST_VALUE_VOLUME_DOWN ->{
                    mBindingView.imgTestType.setImageResource(R.mipmap.test_vol_down)
                }
            }

            mBindingView.imgNo.setOnClickListener {
                StoreKeyData.setInt(testType!!.testIntent,-1)
                finish()
            }

            mBindingView.imgYes.setOnClickListener {
                StoreKeyData.setInt(testType!!.testIntent,1)
                finish()
            }
        }
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        if (keyCode == KeyEvent.KEYCODE_VOLUME_UP) {
            if (testType?.testIntent == StoreKeyData.TEST_VALUE_VOLUME_UP){
                testTypeViewModel.sensorVibStart(this,true)
                return true
            }
        } else if (keyCode == KeyEvent.KEYCODE_VOLUME_DOWN) {
            if (testType?.testIntent == StoreKeyData.TEST_VALUE_VOLUME_DOWN){
                testTypeViewModel.sensorVibStart(this,true)
                return true
            }
        }
        return super.onKeyDown(keyCode, event)
    }

    override fun finish() {
        LiveEventBus.get<String>(EventData.EVENT_RETURN_BACK).post("")
        super.finish()
    }

    override fun onDestroy() {
        when(testType?.testIntent){
            StoreKeyData.TEST_VALUE_VIBRATION ->{testTypeViewModel.sensorVibStop()}
            StoreKeyData.TEST_VALUE_FLASHLIGHT ->{testTypeViewModel.flashClose(this)}
            StoreKeyData.TEST_VALUE_LIGHT,StoreKeyData.TEST_VALUE_PROXIMITY ->{testTypeViewModel.destroyBodySensor()}
            StoreKeyData.TEST_VALUE_LOUD,StoreKeyData.TEST_VALUE_SPEAKER ->{testTypeViewModel.earSoundStop()}
        }
        super.onDestroy()
    }

}