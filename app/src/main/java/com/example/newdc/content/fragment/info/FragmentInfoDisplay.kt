package com.example.newdc.content.fragment.info

import androidx.lifecycle.ViewModelProvider
import com.example.newdc.R
import com.example.newdc.content.fragment.BaseLayoutFragment
import com.example.newdc.databinding.FragmentInfoDevBinding
import com.example.newdc.databinding.FragmentInfoDisplayBinding
import com.example.newdc.databinding.FragmentInfoSysBinding
import com.example.newdc.modelutil.DevInfoViewModel

class FragmentInfoDisplay: BaseLayoutFragment<FragmentInfoDisplayBinding>() {
    private lateinit var devViewModel: DevInfoViewModel


    override fun layoutResource(): Int {
        return R.layout.fragment_info_display
    }

    override fun viewCreated() {
        devViewModel = ViewModelProvider(this)[DevInfoViewModel::class.java]
        mBindingView.devData = devViewModel
        mBindingView.lifecycleOwner = this
        devViewModel.getDisplayInfo(requireContext())
    }
}