package com.example.newdc.content.activity

import android.content.Context
import android.content.Intent
import android.view.View
import com.additional.max.AdConfig
import com.additional.max.MaxScreenAdManager
import com.example.newdc.R
import com.example.newdc.content.fragment.browser.*
import com.example.newdc.databinding.ActivityBaseBrowserBinding
import com.example.newdc.modelutil.data.EventData
import com.example.newdc.modelutil.data.ItemFileData
import com.example.newdc.modelutil.tool.ExtraTool.countDownFlow
import com.example.newdc.modelutil.tool.MediaStorageTool
import com.jeremyliao.liveeventbus.LiveEventBus
import kotlinx.coroutines.Job

class BaseBrowserActivity: BaseControlActivity<ActivityBaseBrowserBinding>() {
    private var typeFragment: BaseBrowserFragment<*>? = null
    private var controlBarShow = false

    private var animDisposableJob: Job? = null
    private var maxScreenAdManager: MaxScreenAdManager? = null

    override fun layoutResource(): Int {
        return R.layout.activity_base_browser
    }

    companion object{
        const val BROWSER_TYPE = "browser_type"
        const val TYPE_BROWSER_DIR = 1
        const val TYPE_BROWSER_REDUNDANT = 2
        const val TYPE_BROWSER_LARGE = 3
        const val TYPE_BROWSER_DUPLICATE = 4
        const val TYPE_BROWSER_RECENT = 5
        const val TYPE_BROWSER_IMAGE = 6
        const val TYPE_BROWSER_VIDEO = 7
        const val TYPE_BROWSER_AUDIO = 8
        const val TYPE_BROWSER_DOCS = 9
        const val TYPE_BROWSER_ZIPS = 10
        const val TYPE_FUNC_STORAGE = 11
        const val TYPE_FUNC_RAM = 12

        fun enterBrowserTypeActivity(context: Context,browserType: Int){
            val intent = Intent(context, BaseBrowserActivity::class.java)
            intent.putExtra(BROWSER_TYPE, browserType)
            try {
                context.startActivity(intent)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    override fun viewCreated() {
        MediaStorageTool.updateMediaStorage(this)
        setFragmentView()
        setListener()
        initAd()
        initScanAnim()
    }

    private fun initAd(){
        if (AdConfig.getSwitchAdInters()){
            maxScreenAdManager = MaxScreenAdManager(true).apply {
                initAd()
                adLoad(this@BaseBrowserActivity)
            }
        }
    }

    private fun setListener() {
        typeFragment?.setItemSelectListener(object : BaseBrowserFragment.ItemSelectListener {
            override fun onClick(selectList: MutableList<ItemFileData>) {
                if (controlBarShow) {
                    changeBarStatus(selectList.size)
                    selectItem = selectList
                }
            }

            override fun onLongClick(barOpen: Boolean, selectList: MutableList<ItemFileData>) {
                controlBarShow = barOpen
                if (controlBarShow){
                    changeBarStatus(selectList.size)
                    selectItem = selectList
                }
            }
        })

        LiveEventBus.get<String>(EventData.EVENT_FILE_CHANGED).observe(this){
            typeFragment?.closeControlBar()
            controlBar?.visibility = View.GONE
            btnClean?.visibility = View.GONE
            controlBarShow = false
        }

        LiveEventBus.get<String>(EventData.EVENT_FILE_DELETE).observe(this){
            deleteToOtherPage(browserType)
        }

        mBindingView.btnClean.setOnClickListener {
            deleteToOtherPage(browserType)
        }

    }

    private fun setFragmentView() {
        browserType = intent.getIntExtra(BROWSER_TYPE, 0)
        typeFragment = when (browserType) {
            TYPE_BROWSER_DIR -> {
                mBindingView.llTitleBar.tvTitle.text = getString(R.string.title_brow_storage)
                FragmentBrowserDir()
            }
            TYPE_BROWSER_LARGE -> {
                mBindingView.llTitleBar.tvTitle.text = getString(R.string.title_brow_large)
                FragmentBrowserLarge()
            }
            TYPE_BROWSER_DUPLICATE -> {
                mBindingView.llTitleBar.tvTitle.text = getString(R.string.title_brow_duplicate)
                FragmentBrowserDuplication()
            }
            TYPE_BROWSER_RECENT -> {
                mBindingView.llTitleBar.tvTitle.text = getString(R.string.title_brow_recent)
                FragmentBrowserDuplication(true)
            }
            TYPE_BROWSER_IMAGE -> {
                mBindingView.llTitleBar.tvTitle.text = getString(R.string.base_images)
                FragmentBrowserMedia()
            }
            TYPE_BROWSER_VIDEO -> {
                mBindingView.llTitleBar.tvTitle.text = getString(R.string.base_videos)
                FragmentBrowserMedia(true)
            }
            TYPE_BROWSER_AUDIO ->{
                mBindingView.llTitleBar.tvTitle.text = getString(R.string.base_audios)
                FragmentBrowserOtherFile(TYPE_BROWSER_AUDIO)
            }
            TYPE_BROWSER_ZIPS ->{
                mBindingView.llTitleBar.tvTitle.text = getString(R.string.base_zips)
                FragmentBrowserOtherFile(TYPE_BROWSER_ZIPS)
            }
            TYPE_BROWSER_DOCS ->{
                mBindingView.llTitleBar.tvTitle.text = getString(R.string.base_docs)
                FragmentBrowserDocs()
            }
            else -> FragmentBrowserDir()
        }
        supportFragmentManager.beginTransaction()
            .add(R.id.frame_brow, typeFragment!!)
            .commit()
    }

    fun initScanAnim(){
        when(browserType){
            TYPE_BROWSER_DIR -> {
                mBindingView.lottScan.setAnimation("func/browser/browse.json")
                mBindingView.lottScan.imageAssetsFolder = "func/browser/images"
            }
            TYPE_BROWSER_LARGE -> {
                mBindingView.lottScan.setAnimation("func/large/scan/large-scanning.json")
                mBindingView.lottScan.imageAssetsFolder = "func/large/scan/images"
            }
            TYPE_BROWSER_DUPLICATE -> {
                mBindingView.lottScan.setAnimation("func/duplicate/scan/duplicate-scanning.json")
                mBindingView.lottScan.imageAssetsFolder = "func/duplicate/scan/images"
            }
            TYPE_BROWSER_RECENT -> {
                mBindingView.lottScan.setAnimation("func/recent/scan/recent-scanning.json")
                mBindingView.lottScan.imageAssetsFolder = "func/recent/scan/images"
            }
            TYPE_BROWSER_IMAGE -> {
                mBindingView.lottScan.setAnimation("func/image/image.json")
                mBindingView.lottScan.imageAssetsFolder = "func/image/images"
            }
            TYPE_BROWSER_VIDEO -> {
                mBindingView.lottScan.setAnimation("func/video/video.json")
                mBindingView.lottScan.imageAssetsFolder = "func/video/images"
            }
            TYPE_BROWSER_AUDIO ->{
                mBindingView.lottScan.setAnimation("func/audio/audio.json")
                mBindingView.lottScan.imageAssetsFolder = "func/audio/images"
            }
            TYPE_BROWSER_ZIPS ->{
                mBindingView.lottScan.setAnimation("func/zip/zip.json")
                mBindingView.lottScan.imageAssetsFolder = "func/zip/images"
            }
            TYPE_BROWSER_DOCS ->{
                mBindingView.lottScan.setAnimation("func/doc/doc.json")
                mBindingView.lottScan.imageAssetsFolder = "func/doc/images"
            }
        }
        startAnim()
    }

    fun startAnim(){
        animDisposableJob = countDownFlow(
            start = {
                mBindingView.lottPage.visibility = View.VISIBLE
                mBindingView.lottScan.playAnimation()
            },
            next = {},
            end = {
                if (maxScreenAdManager != null && AdConfig.getAllowShowInterstitial()){
                    maxScreenAdManager?.startWaitingJob(this,
                        next = {
                            if (maxScreenAdManager?.showAd(this) == true){
                                closeAnim()
                            }
                        },
                        end = {
                            closeAnim()
                        })
                }else{
                    closeAnim()
                }
            }
        )
    }

    private fun closeAnim(){
        mBindingView.lottScan.apply {
            cancelAnimation()
            clearAnimation()
            visibility = View.GONE
        }
        mBindingView.lottPage.visibility = View.GONE
    }

    override fun onBackPressed() {
        if (controlBarShow){
            typeFragment?.closeControlBar()
            controlBar?.visibility = View.GONE
            btnClean?.visibility = View.GONE
            controlBarShow = false
        }else if (typeFragment is FragmentBrowserDir){
            val list = (typeFragment as FragmentBrowserDir).getTitlePath()
            if(list.size >= 2){
                try {
                    val newPath = list[list.size - 2]
                    (typeFragment as FragmentBrowserDir).changeTitlePath(newPath)
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }else{
                super.onBackPressed()
            }
        }else{
            super.onBackPressed()
        }
    }

    override fun finish() {
        LiveEventBus.get<String>(EventData.EVENT_RETURN_BACK).post("")
        super.finish()
    }

    override fun onDestroy() {
        maxScreenAdManager?.adDestroy()
        super.onDestroy()
    }
}