package com.example.newdc.content.fragment.info

import android.os.Handler
import android.os.Looper
import androidx.lifecycle.ViewModelProvider
import com.example.newdc.R
import com.example.newdc.content.fragment.BaseLayoutFragment
import com.example.newdc.dataadapter.MoreInfoAdapter
import com.example.newdc.dataadapter.RateItemAdapter
import com.example.newdc.databinding.FragmentInfoCpuBinding
import com.example.newdc.databinding.FragmentInfoDevBinding
import com.example.newdc.databinding.FragmentInfoNetworkBinding
import com.example.newdc.databinding.FragmentInfoSysBinding
import com.example.newdc.modelutil.DevInfoViewModel
import com.example.newdc.modelutil.DevValueViewModel
import java.util.*

class FragmentInfoCpu: BaseLayoutFragment<FragmentInfoCpuBinding>() {
    private lateinit var devViewModel: DevInfoViewModel
    private lateinit var devValueModel: DevValueViewModel
    private lateinit var cpuAdapter: RateItemAdapter

    private lateinit var timer: Timer

    override fun layoutResource(): Int {
        return R.layout.fragment_info_cpu
    }

    override fun onStart() {
        super.onStart()
        startTimer()
    }

    override fun onStop() {
        super.onStop()
        if(::timer.isInitialized){
            timer.cancel()
        }
    }

    override fun viewCreated() {
        devViewModel = ViewModelProvider(this)[DevInfoViewModel::class.java]
        devValueModel = ViewModelProvider(this)[DevValueViewModel::class.java]
        mBindingView.devData = devViewModel
        mBindingView.lifecycleOwner = this

        cpuAdapter = RateItemAdapter()
        mBindingView.rvCpu.adapter = cpuAdapter


        devValueModel.getStatusCpuRates.observe(this){
            cpuAdapter.setData(it)
        }

        mBindingView.gpuFrame.let {
            it.setInitListener {
                Handler(Looper.getMainLooper()).post {
                    mBindingView.valueRender.text = it.surRenderer
                    mBindingView.valueVersion.text = it.surVersion
                    mBindingView.valueVendor.text = it.surVendor
                }
            }
        }


        devViewModel.getCpuInfoData(requireContext())
        devViewModel.getGpuInfoData(requireContext())
        devValueModel.getCpuRate(requireContext())
    }

    private fun startTimer(){
        timer = Timer()
        timer.schedule(object : TimerTask() {
            override fun run() {
                devValueModel.getCpuRate(requireContext())
            }
        },0,1000)
    }
}