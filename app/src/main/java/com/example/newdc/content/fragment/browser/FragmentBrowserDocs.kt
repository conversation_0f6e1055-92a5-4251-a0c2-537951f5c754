package com.example.newdc.content.fragment.browser

import android.R.attr.entries
import android.view.View
import android.widget.AdapterView
import android.widget.ArrayAdapter
import androidx.recyclerview.widget.LinearLayoutManager
import com.example.newdc.R
import com.example.newdc.dataadapter.BaseFileBrowserAdapter
import com.example.newdc.dataadapter.FileItemAdapter
import com.example.newdc.databinding.FragmentBrowLargeBinding
import com.example.newdc.modelutil.data.EventData
import com.example.newdc.modelutil.tool.MediaStorageTool
import com.jeremyliao.liveeventbus.LiveEventBus


class FragmentBrowserDocs: BaseBrowserFragment<FragmentBrowLargeBinding>() {
    override fun layoutResource(): Int {
        return R.layout.fragment_brow_large
    }

    override fun setFileAdapter(): BaseFileBrowserAdapter {
        return FileItemAdapter()
    }

    override fun viewCreated() {
        setView()
        setListener()
        mBindingView.llLoad.llLoad.visibility = View.VISIBLE
        filesStorageViewModel.getDocsFileData(requireContext())
    }

    private fun setListener() {
        filesStorageViewModel.getBrowserDocsList.observe(this){
            mBindingView.llLoad.llLoad.visibility = View.GONE
            itemAdapter?.setData(it)
            if (it.isNotEmpty()){
                mBindingView.llEmpty.visibility = View.GONE
            }else{
                mBindingView.llEmpty.visibility = View.VISIBLE
            }
        }
        mBindingView.spinnerType.apply {
            val mAdapter = ArrayAdapter(requireContext(), android.R.layout.simple_spinner_item, resources.getStringArray(R.array.type_docs))
            mAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
            adapter = mAdapter
            onItemSelectedListener = object : AdapterView.OnItemSelectedListener, AdapterView.OnItemClickListener {
                override fun onItemSelected(parent: AdapterView<*>?, view: View?, position: Int, id: Long
                ) {
                    getFilterFiles(position)
                    checkAll?.isChecked = false
                }

                override fun onNothingSelected(parent: AdapterView<*>?) {}

                override fun onItemClick(parent: AdapterView<*>?, view: View?, position: Int, id: Long) {}
            }
        }

        LiveEventBus.get<String>(EventData.EVENT_FILE_CHANGED).observe(this){
            filesStorageViewModel.getDocsFileData(requireContext())
        }
    }

    private fun setView() {
        mBindingView.rvDir.apply {
            layoutManager = LinearLayoutManager(context)
            adapter = itemAdapter
        }
    }

    private fun getFilterFiles(index: Int){
        val fileType = when(index){
            1 -> MediaStorageTool.TYPE_BROWSER_DOC_PDF
            2 -> MediaStorageTool.TYPE_BROWSER_DOC_TXT
            3 -> MediaStorageTool.TYPE_BROWSER_DOC_WORD
            4 -> MediaStorageTool.TYPE_BROWSER_DOC_EXCEL
            else -> ""
        }
        getAdapter()?.filterFileList(fileType)
    }
}