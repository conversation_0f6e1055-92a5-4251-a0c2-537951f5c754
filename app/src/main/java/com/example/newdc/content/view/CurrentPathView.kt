package com.example.newdc.content.view

import android.annotation.SuppressLint
import android.content.Context
import android.os.Build
import android.os.storage.StorageManager
import android.util.AttributeSet
import android.widget.LinearLayout
import android.widget.TextView
import com.example.newdc.R
import com.example.newdc.modelutil.tool.ExtraTool.dp2px
import com.example.newdc.modelutil.tool.FilesTool

class CurrentPathView @JvmOverloads constructor(
    context: Context?,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) :
    LinearLayout(context, attrs, defStyleAttr) {
    private val mTextSize = 6f.dp2px
    private val textColor: Int = R.color.color_text_black
    private val pathList = mutableListOf<String>()
    private var clickListener: ItemClickListener? = null


    @SuppressLint("SetTextI18n")
    fun getTitleCurrent(path: String) {
        removeAllViews()
        pathList.clear()
        val root = getStoragePath(path)
        val paths = FilesTool.findPathSplit(path, root)
        val names = FilesTool.findFolderFromPath(path, root)
        var i = -1
        for (index in paths.indices) {
            val s = paths[index]
            if (isStoragePath(paths[index])) {
                val rootText = TextView(context)
                rootText.setTextColor(resources.getColor(textColor))
                val pathName = FilesTool.findMountName(paths[index])
                rootText.text = pathName ?: paths[index]
                rootText.textSize = mTextSize
                rootText.setOnClickListener {
                    if (index != childCount) {
                        if (clickListener != null) {
                            clickListener?.click(s)
                        }
                    }
                }
                pathList.add(s)
                addView(rootText)
            } else if (s != "/") {
                i++
                if (i >= names.size) return
                if (names[i] != "") {
                    val childText = TextView(context)
                    childText.setTextColor(resources.getColor(textColor))
                    childText.text = "/" + names[i]
                    childText.textSize = mTextSize
                    childText.setOnClickListener {
                        if (index != childCount) {
                            clickListener?.click(s)
                        }
                    }
                    pathList.add(s)
                    addView(childText)
                }
            }
        }
        if (childCount == 1) {
            try {
                val childText1 = getChildAt(0) as TextView
                childText1.text = childText1.text.toString() + "/"
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    fun getPathList() = pathList

    private fun isStoragePath(path: String): Boolean {
        val paths: List<String> = deviceList
        for (p in paths) {
            if (p == path) {
                return true
            }
        }
        return false
    }

    private val deviceList: ArrayList<String>
        get() {
            val pathList = ArrayList<String>()
            val storageManager = context.getSystemService(Context.STORAGE_SERVICE) as StorageManager
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                val volumes = storageManager.storageVolumes
                for (volume in volumes) {
                    var path: String? = null
                    if (Build.VERSION.SDK_INT < 30) {
                        try {
                            val clazz = Class.forName(volume!!.javaClass.name)
                            val method = clazz.getDeclaredMethod("getPath")
                            method.isAccessible = true
                            path = method.invoke(volume) as String
                        } catch (e: Exception) {
                            e.printStackTrace()
                        }
                    } else {
                        try {
                            if (volume != null) {
                                path = volume.directory!!.absolutePath
                            }
                        } catch (e: Exception) {
                            e.printStackTrace()
                        }
                    }
                    if (volume != null) {
                        if (volume.state == "mounted" && path != null) {
                            pathList.add(path)
                        }
                    }
                }
            }
            return pathList
        }

    private fun getStoragePath(path: String): String {
        val paths = deviceList
        for (str in paths) {
            if (path.startsWith(str)) return str
        }
        return ""
    }

    interface ItemClickListener {
        fun click(path: String?)
    }

    fun setItemClickListener(listener: ItemClickListener?) {
        clickListener = listener
    }
}