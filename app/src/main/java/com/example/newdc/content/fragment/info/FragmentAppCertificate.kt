package com.example.newdc.content.fragment.info

import androidx.lifecycle.ViewModelProvider
import com.example.newdc.R
import com.example.newdc.content.fragment.BaseLayoutFragment
import com.example.newdc.databinding.FragmentAppCertificateBinding
import com.example.newdc.databinding.FragmentAppGeneralBinding
import com.example.newdc.databinding.FragmentInfoSysBinding
import com.example.newdc.modelutil.AppInfoViewModel
import com.example.newdc.modelutil.DevInfoViewModel
import java.util.*

class FragmentAppCertificate(private val packageName: String): BaseLayoutFragment<FragmentAppCertificateBinding>() {
    private lateinit var appInfoViewModel: AppInfoViewModel


    override fun layoutResource(): Int {
        return R.layout.fragment_app_certificate
    }

    override fun viewCreated() {
        appInfoViewModel = ViewModelProvider(this)[AppInfoViewModel::class.java]
        mBindingView.info = appInfoViewModel
        mBindingView.lifecycleOwner = this

        appInfoViewModel.getAppCertificateInfo(requireContext(),packageName)

    }
}