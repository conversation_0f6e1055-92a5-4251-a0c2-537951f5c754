package com.example.newdc.content.activity

import android.content.Intent
import android.graphics.Color
import android.net.Uri
import android.os.Build
import android.text.Spannable
import android.text.SpannableString
import android.text.TextPaint
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.text.style.ForegroundColorSpan
import android.view.View
import android.widget.Toast
import com.additional.max.AdConfig
import com.additional.max.MaxScreenAdManager
import com.additional.max.UMPTool
import com.applovin.sdk.AppLovinSdk
import com.example.newdc.BaseApplication
import com.example.newdc.BaseLifeCycle
import com.example.newdc.R
import com.example.newdc.content.BaseLayoutActivity
import com.example.newdc.databinding.ActivityStarterBinding
import com.example.newdc.modelutil.data.EventData
import com.example.newdc.modelutil.data.StoreKeyData
import com.example.newdc.modelutil.tool.ExtraTool.countDownFlow
import com.jeremyliao.liveeventbus.LiveEventBus
import kotlinx.coroutines.Job


class StarterActivity: BaseLayoutActivity<ActivityStarterBinding>() {
    private var startDisposable: Job? = null
    private var progressCount = 0
    private var enterType: String? = null
    private var maxScreenAdManager: MaxScreenAdManager? = null
    private var adType = -1
    private var enterDirect = false
    private var adIsLoad = false

    override fun layoutResource(): Int {
        return R.layout.activity_starter
    }

    override fun viewCreated() {
        adType = intent.getIntExtra(BaseLifeCycle.BUNDLE_NOTIFY_ENTER_CODE, -1)
        startByAgree()
    }

    private fun initAd(){
       if (adType != 1){
           val isInters = adType == 3
           val adCanShow = if (isInters) AdConfig.getSwitchAdInters() else AdConfig.getSwitchAdOpen()
           if (AppLovinSdk.getInstance(BaseApplication.mAppContext).isInitialized && adCanShow && !adIsLoad){
               adIsLoad = true
               maxScreenAdManager = MaxScreenAdManager(isInters){
                   enterToActivity()
               }.apply {
                   initAd()
                   adLoad(this@StarterActivity)
               }
           }
       }else{
           enterDirect = true
       }
    }

    private fun startByAgree(){
        val isAgree = StoreKeyData.getBoolean(StoreKeyData.BASE_KEY_AGREE_PRIVACY)
        if (isAgree) {
            LiveEventBus.get<String>(EventData.EVENT_AD_INIT).observe(this){ initAd() }
            if (UMPTool.UMPIsInit.get()){
                initAd()
                startProgress()
            }else{
                UMPTool.userMessagingRequest(this){
                    UMPTool.UMPIsInit.set(true)
                    initAd()
                    startProgress()
                }
            }
        }else{
            setPrivacyText()
            mBindingView.proStart.visibility = View.GONE
            mBindingView.btnAgree.visibility = View.VISIBLE
            mBindingView.llPrivacy.visibility = View.VISIBLE
            mBindingView.btnAgree.setOnClickListener {
                if (mBindingView.ckAgree.isChecked){
                    StoreKeyData.setBoolean(StoreKeyData.BASE_KEY_AGREE_PRIVACY,true)
                    mBindingView.llPrivacy.visibility = View.GONE
                    mBindingView.btnAgree.visibility = View.GONE
                    mBindingView.proStart.visibility = View.VISIBLE
                    UMPTool.userMessagingRequest(this){
                        UMPTool.UMPIsInit.set(true)
                        startProgress()
                    }
                }else{
                    Toast.makeText(this, getString(R.string.base_agree_no), Toast.LENGTH_SHORT).show()
                }
            }
        }
    }

    private fun startProgress(){
        val times = 100
        val delay = if (enterDirect) 1 * 1000 / 100L else 4 * 1000 / 100L
        startDisposable = countDownFlow(
            time = times,
            delay = delay,
            start = {
                progressCount = 0
                mBindingView.proStart.progress = progressCount
            },
            end = {
                val allowShowAd = if (adType == 3) AdConfig.getAllowShowInterstitial() else AdConfig.getAllowShowOpen()
                if (maxScreenAdManager != null && allowShowAd){
                    maxScreenAdManager?.startWaitingJob(this,
                        next = {maxScreenAdManager?.showAd(this)},
                        end = {
                            enterToActivity()
                        }
                    )
                }else{
                    enterToActivity()
                }
            },
            next = {
                progressCount += 1
                mBindingView.proStart.progress = progressCount
            },
        )
    }

    private fun enterToActivity(){
        enterType = intent?.getStringExtra(BaseLifeCycle.BUNDLE_NOTIFY_INTENT)
        val jump = intent?.getBooleanExtra(BaseLifeCycle.BUNDLE_TRANS_HOME,false) ?: false
        if (!jump){
            startActivity(Intent(this, MainActivity::class.java).apply {
                putExtra(BaseLifeCycle.BUNDLE_NOTIFY_INTENT, enterType)
            })
        }
        finish()
    }

    private fun setPrivacyText(){
        val agreeText = getString(R.string.base_tip_privacy)
        val spannableString = SpannableString(agreeText)
        val userClickSpan = object : ClickableSpan() {
            override fun onClick(widget: View) {
                startActivity(Intent(Intent.ACTION_VIEW, Uri.parse(getString(R.string.url_user))))
            }
            override fun updateDrawState(ds: TextPaint) {
                ds.isUnderlineText = false
            }
        }

        val privacyClickSpan = object : ClickableSpan() {
            override fun onClick(widget: View) {
                startActivity(Intent(Intent.ACTION_VIEW, Uri.parse(getString(R.string.url_privacy))))
            }
            override fun updateDrawState(ds: TextPaint) {
                ds.isUnderlineText = false
            }
        }

        val privacyStartIndex = agreeText.indexOf(getString(R.string.base_privacy_policy))
        spannableString.setSpan(
            privacyClickSpan,
            privacyStartIndex,
            privacyStartIndex + getString(R.string.base_privacy_policy).length,
            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
        )

        spannableString.setSpan(
            ForegroundColorSpan(Color.parseColor("#FF7851FD")),
            privacyStartIndex,
            privacyStartIndex + getString(R.string.base_privacy_policy).length,
            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
        )


        val userStartIndex = agreeText.indexOf(getString(R.string.base_user_agreement))
        spannableString.setSpan(
            userClickSpan,
            userStartIndex,
            userStartIndex + getString(R.string.base_user_agreement).length,
            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
        )

        spannableString.setSpan(
            ForegroundColorSpan(Color.parseColor("#FF7851FD")),
            userStartIndex,
            userStartIndex + getString(R.string.base_user_agreement).length,
            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
        )

        mBindingView.tvAgree.apply {
            text = spannableString
            movementMethod = LinkMovementMethod.getInstance()
            highlightColor = Color.TRANSPARENT
        }
    }

    override fun onDestroy() {
        maxScreenAdManager?.adDestroy()
        maxScreenAdManager?.openDestroy()
        MaxScreenAdManager.loadAdCache(false,BaseApplication.mAppContext,this)
        super.onDestroy()
    }

    override fun onBackPressed() {

    }
}