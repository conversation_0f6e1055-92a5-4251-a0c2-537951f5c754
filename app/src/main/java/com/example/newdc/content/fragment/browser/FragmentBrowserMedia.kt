package com.example.newdc.content.fragment.browser

import android.util.Log
import android.view.View
import androidx.recyclerview.widget.GridLayoutManager
import com.example.newdc.R
import com.example.newdc.dataadapter.BaseFileBrowserAdapter
import com.example.newdc.dataadapter.MediaFileAdapter
import com.example.newdc.databinding.FragmentBrowDuplicateBinding
import com.example.newdc.modelutil.data.EventData
import com.jeremyliao.liveeventbus.LiveEventBus

class FragmentBrowserMedia(val isVideo: Boolean = false): BaseBrowserFragment<FragmentBrowDuplicateBinding>() {

    override fun layoutResource(): Int {
        return R.layout.fragment_brow_duplicate
    }

    override fun setFileAdapter(): BaseFileBrowserAdapter {
        return MediaFileAdapter()
    }

    override fun viewCreated() {
        mBindingView.rvDir.apply {
            layoutManager = GridLayoutManager(requireContext(),3)
            adapter = itemAdapter
        }

        filesStorageViewModel.getBrowserMediaList.observe(this){
            mBindingView.llLoad.llLoad.visibility = View.GONE
            itemAdapter?.setData(it)
            if (it.isNotEmpty()){
                mBindingView.llEmpty.visibility = View.GONE
            }else{
                mBindingView.llEmpty.visibility = View.VISIBLE
            }
        }

        LiveEventBus.get<String>(EventData.EVENT_FILE_CHANGED).observe(this){
            mBindingView.llLoad.llLoad.visibility = View.VISIBLE
            filesStorageViewModel.getMediaFileData(requireContext(),isVideo)
        }

        mBindingView.llLoad.llLoad.visibility = View.VISIBLE
        filesStorageViewModel.getMediaFileData(requireContext(),isVideo)
    }
}