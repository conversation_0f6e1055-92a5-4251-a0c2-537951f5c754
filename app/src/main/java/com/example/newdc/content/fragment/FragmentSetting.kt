package com.example.newdc.content.fragment

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.net.Uri
import com.example.newdc.BaseLifeCycle
import com.example.newdc.BuildConfig
import com.example.newdc.R
import com.example.newdc.content.activity.PermissionManagerActivity
import com.example.newdc.databinding.FragmentSettingsBinding

class FragmentSetting: BaseLayoutFragment<FragmentSettingsBinding>() {
    override fun layoutResource(): Int {
        return R.layout.fragment_settings
    }

    @SuppressLint("SetTextI18n")
    override fun viewCreated() {
        mBindingView.tvVersion.text = "${getString(R.string.set_version)} ${BuildConfig.VERSION_NAME}"
        mBindingView.llPermission.setOnClickListener {
            startActivity(Intent(requireActivity(),PermissionManagerActivity::class.java))
        }
        mBindingView.llFeedback.setOnClickListener {
            val uri = Uri.parse("mailto:<EMAIL>")
            val sendIntent = Intent(Intent.ACTION_SENDTO,uri).apply {
                putExtra(Intent.EXTRA_SUBJECT,requireContext().getString(R.string.base_feedback))
                putExtra(Intent.EXTRA_TEXT,requireContext().getString(R.string.base_share_feedback))
            }
            try {
                requireActivity().startActivity(Intent.createChooser(sendIntent,requireContext().getString(R.string.base_feedback)))
                BaseLifeCycle.splashFlag = true
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
        mBindingView.llUser.setOnClickListener {
            openUri(requireContext(),getString(R.string.url_user))
        }
        mBindingView.llPrivacy.setOnClickListener {
            openUri(requireContext(),getString(R.string.url_privacy))
        }
    }

    private fun openUri(context: Context, url: String){
        try {
            val uri = Uri.parse(url)
            context.startActivity(Intent(Intent.ACTION_VIEW, uri))
            BaseLifeCycle.splashFlag = true
        }catch (e: Exception){e.printStackTrace()}
    }
}