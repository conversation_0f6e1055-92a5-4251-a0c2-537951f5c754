package com.example.newdc.content.fragment.browser

import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import com.example.newdc.R
import com.example.newdc.dataadapter.BaseFileBrowserAdapter
import com.example.newdc.dataadapter.DuplicateAdapter
import com.example.newdc.dataadapter.FileItemAdapter
import com.example.newdc.databinding.FragmentBrowDuplicateBinding
import com.example.newdc.databinding.FragmentBrowLargeBinding
import com.example.newdc.modelutil.data.EventData
import com.jeremyliao.liveeventbus.LiveEventBus

class FragmentBrowserDuplication(val isRecent: Boolean = false): BaseBrowserFragment<FragmentBrowDuplicateBinding>() {

    override fun layoutResource(): Int {
        return R.layout.fragment_brow_duplicate
    }

    override fun setFileAdapter(): BaseFileBrowserAdapter {
        return if (isRecent){
            FileItemAdapter()
        }else{
            DuplicateAdapter()
        }
    }

    override fun viewCreated() {
        mBindingView.rvDir.apply {
            layoutManager = LinearLayoutManager(context)
            adapter = itemAdapter
        }

        filesStorageViewModel.getBrowserRecentList.observe(this){
            mBindingView.llLoad.llLoad.visibility = View.GONE
            itemAdapter?.setData(it)
            if (it.isNotEmpty()){
                mBindingView.llEmpty.visibility = View.GONE
            }else{
                mBindingView.llEmpty.visibility = View.VISIBLE
            }
        }

        filesStorageViewModel.getBrowserDuplicateList.observe(this){
            mBindingView.llLoad.llLoad.visibility = View.GONE
            itemAdapter?.setData(it)
            if (it.isNotEmpty()){
                mBindingView.llEmpty.visibility = View.GONE
            }else{
                mBindingView.llEmpty.visibility = View.VISIBLE
            }
        }

        LiveEventBus.get<String>(EventData.EVENT_FILE_CHANGED).observe(this){
            if (isRecent){
                filesStorageViewModel.getRecentFile(requireContext())
            }else{
                filesStorageViewModel.getDuplicateFile(requireContext())
            }
        }

        mBindingView.llLoad.llLoad.visibility = View.VISIBLE
        if (isRecent){
            filesStorageViewModel.getRecentFile(requireContext())
        }else{
            filesStorageViewModel.getDuplicateFile(requireContext())
        }
    }
}