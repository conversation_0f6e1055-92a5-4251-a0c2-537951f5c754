package com.example.newdc.content.fragment

import android.annotation.SuppressLint
import android.content.Intent
import androidx.lifecycle.ViewModelProvider
import com.example.newdc.R
import com.example.newdc.content.activity.RealtimeActivity
import com.example.newdc.content.activity.StorageActivity
import com.example.newdc.databinding.FragmentHomeBinding
import com.example.newdc.modelutil.DevValueViewModel
import com.example.newdc.modelutil.data.EventData
import com.jeremyliao.liveeventbus.LiveEventBus

class FragmentHome: BaseLayoutFragment<FragmentHomeBinding>() {
    private lateinit var infoValueViewModel: DevValueViewModel

    override fun layoutResource(): Int {
        return R.layout.fragment_home
    }

    override fun viewCreated() {
        infoValueViewModel = ViewModelProvider(this)[DevValueViewModel::class.java]
        mBindingView.lifecycleOwner = this
        mBindingView.info = infoValueViewModel

        setView()
        setListener()
        infoValueViewModel.getHomeInfoBoard()
        infoValueViewModel.getHomeRamAndRomValue(requireContext())
    }

    @SuppressLint("SetTextI18n")
    private fun setView() {
        mBindingView.btnRam.text = "${getString(R.string.base_go)} >"
        mBindingView.btnSto.text = "${getString(R.string.base_go)} >"
    }

    private fun setListener() {
        mBindingView.btnSto.setOnClickListener {
            startActivity(Intent(requireActivity(), StorageActivity::class.java))
        }

        mBindingView.btnRam.setOnClickListener {
            startActivity(Intent(requireActivity(), RealtimeActivity::class.java))
        }

        mBindingView.llFunMore.setOnClickListener {
            LiveEventBus.get<Int>(EventData.EVENT_HOME_TAB_TRANSLATE).post(1)
        }

        infoValueViewModel.getValueStoragePercent.observe(viewLifecycleOwner) {
            mBindingView.tvStoPer.text = "$it%"
            mBindingView.barStorage.setProgress(it.toFloat())
        }

        infoValueViewModel.getValueRamPercent.observe(viewLifecycleOwner) {
            mBindingView.tvRamPer.text = "$it%"
            mBindingView.barRam.setProgress(it.toFloat())
        }
    }
}