package com.example.newdc.content.view.dialog

import android.app.Activity
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.util.Log
import android.view.View
import com.additional.firebase.FirebaseEventPost
import com.example.newdc.R
import com.example.newdc.content.view.RateStartView
import com.example.newdc.databinding.DialogRatingViewBinding
import com.example.newdc.modelutil.data.StoreKeyData
import com.google.android.play.core.review.ReviewManagerFactory
import com.google.android.play.core.review.testing.FakeReviewManager
import kotlin.math.abs

class RateDialog(private val call: () -> Unit) : BaseDialogFragment<DialogRatingViewBinding>() {
    override fun getBaseLayoutRes(): Int {
        return  R.layout.dialog_rating_view
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        mDataBinding.imgClose.setOnClickListener {
            dismissAllowingStateLoss()
        }

        mDataBinding.tvCancel.setOnClickListener {
            dismissAllowingStateLoss()
        }

        mDataBinding.btnFeedback.setOnClickListener {
            val uri = Uri.parse("mailto:<EMAIL>")
            val sendIntent = Intent(Intent.ACTION_SENDTO,uri).apply {
                putExtra(Intent.EXTRA_SUBJECT,requireContext().getString(R.string.base_feedback))
                putExtra(Intent.EXTRA_TEXT,requireContext().getString(R.string.base_share_feedback))
            }
            requireActivity().startActivity(Intent.createChooser(sendIntent,requireContext().getString(R.string.base_feedback)))
            dismissAllowingStateLoss()
        }

        mDataBinding.rateView.setItemChangeListener(object :RateStartView.ItemChangeListener{
            override fun onItemChange(position: Int) {
                StoreKeyData.setBoolean(StoreKeyData.REVIEW_HAS_GET,true)
                if (position in 0 .. 4){
                    mDataBinding.imgTop.setBackgroundResource(R.mipmap.bg_rate_f)
                    mDataBinding.llRate.visibility = View.GONE
                    mDataBinding.llFeedback.visibility = View.VISIBLE
                }else if (position == 5){
                    call.invoke()
                    dismissAllowingStateLoss()
                }
            }
        })
        StoreKeyData.apply {
            var count = getInt(REVIEW_SHOW_COUNT)
            setInt(REVIEW_SHOW_COUNT,++count)
        }


        FirebaseEventPost.event("rate_request_show")
    }

    companion object{
        fun startGPReview(activity: Activity){
            if (activity == null) return
            try {
               val manager = ReviewManagerFactory.create(activity)
//                val manager = FakeReviewManager(activity)
                val request = manager.requestReviewFlow()
                request.addOnCompleteListener {
                    if (it.isSuccessful){
                        val reviewInfo = it.result
                        val flow = manager.launchReviewFlow(activity,reviewInfo)
                        flow.addOnCompleteListener { task ->
                            if (task.isSuccessful){
                                Log.d("openGPReview","openGPReview Success")
                                FirebaseEventPost.event("rate_show_success")
                            }else{
                                Log.d("openGPReview","openGPReview Error: ${task.exception?.message}")
                                FirebaseEventPost.event("rate_show_error")
                            }
                        }
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
                FirebaseEventPost.event("rate_show_error")
            }
        }

        fun canShowRateDialog(): Boolean{
            val agree = StoreKeyData.getBoolean(StoreKeyData.REVIEW_HAS_GET,false)
            if (!agree){
                val count = StoreKeyData.getInt(StoreKeyData.REVIEW_SHOW_COUNT)
                if (count < 3){
                    val interval = when(count){
                        0 -> 0L
                        1 -> { 3 * 24 * 60 * 60 * 1000}
                        2 -> { 7 * 24 * 60 * 60 * 1000}
                        else -> { 10 * 24 * 60 * 60 * 1000}
                    }
                    if (abs(System.currentTimeMillis() - StoreKeyData.getLong(StoreKeyData.BASE_FIRST_ENTER_TIME)) >= interval ){
                        return true
                    }
                }
            }
            return false
        }
    }

}