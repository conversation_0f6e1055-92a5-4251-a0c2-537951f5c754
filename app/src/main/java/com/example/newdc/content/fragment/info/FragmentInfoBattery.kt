package com.example.newdc.content.fragment.info

import androidx.lifecycle.ViewModelProvider
import com.example.newdc.R
import com.example.newdc.content.fragment.BaseLayoutFragment
import com.example.newdc.databinding.FragmentInfoBatteryBinding
import com.example.newdc.databinding.FragmentInfoDevBinding
import com.example.newdc.databinding.FragmentInfoNetworkBinding
import com.example.newdc.databinding.FragmentInfoSysBinding
import com.example.newdc.modelutil.DevInfoViewModel
import java.util.*

class FragmentInfoBattery: BaseLayoutFragment<FragmentInfoBatteryBinding>() {
    private lateinit var devViewModel: DevInfoViewModel
    private lateinit var timer: Timer

    override fun layoutResource(): Int {
        return R.layout.fragment_info_battery
    }

    override fun onStart() {
        super.onStart()
        startTimer()
    }

    override fun onStop() {
        super.onStop()
        if(::timer.isInitialized){
            timer.cancel()
        }
    }

    override fun viewCreated() {
        devViewModel = ViewModelProvider(this)[DevInfoViewModel::class.java]
        mBindingView.devData = devViewModel
        mBindingView.lifecycleOwner = this
    }

    private fun startTimer(){
        timer = Timer()
        timer.schedule(object : TimerTask() {
            override fun run() {
                devViewModel.getBatterInfo(requireContext())
            }
        },0,2000)
    }
}