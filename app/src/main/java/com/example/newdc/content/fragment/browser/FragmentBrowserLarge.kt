package com.example.newdc.content.fragment.browser

import android.view.View
import android.widget.AdapterView
import androidx.recyclerview.widget.LinearLayoutManager
import com.example.newdc.R
import com.example.newdc.dataadapter.BaseFileBrowserAdapter
import com.example.newdc.dataadapter.FileItemAdapter
import com.example.newdc.databinding.FragmentBrowLargeBinding
import com.example.newdc.modelutil.data.EventData
import com.example.newdc.modelutil.tool.MediaStorageTool
import com.jeremyliao.liveeventbus.LiveEventBus

class FragmentBrowserLarge: BaseBrowserFragment<FragmentBrowLargeBinding>() {
    override fun layoutResource(): Int {
        return R.layout.fragment_brow_large
    }

    override fun setFileAdapter(): BaseFileBrowserAdapter {
        return FileItemAdapter()
    }

    override fun viewCreated() {
        setView()
        setListener()
        mBindingView.llLoad.llLoad.visibility = View.VISIBLE
        filesStorageViewModel.getLargeFiles(requireContext(),10 * 1024 * 1024)
    }

    private fun setListener() {
        filesStorageViewModel.getBrowserLargeList.observe(this){
            mBindingView.llLoad.llLoad.visibility = View.GONE
            itemAdapter?.setData(it)
            if (it.isNotEmpty()){
                mBindingView.llEmpty.visibility = View.GONE
            }else{
                mBindingView.llEmpty.visibility = View.VISIBLE
            }
        }

        mBindingView.spinnerType.onItemSelectedListener = object : AdapterView.OnItemSelectedListener,
            AdapterView.OnItemClickListener {
            override fun onItemSelected(parent: AdapterView<*>?, view: View?, position: Int, id: Long
            ) {
                getFilterFiles(position)
                checkAll?.isChecked = false
            }

            override fun onNothingSelected(parent: AdapterView<*>?) {}

            override fun onItemClick(parent: AdapterView<*>?, view: View?, position: Int, id: Long) {}
        }

        LiveEventBus.get<String>(EventData.EVENT_FILE_CHANGED).observe(this){
            mBindingView.llLoad.llLoad.visibility = View.VISIBLE
            filesStorageViewModel.getLargeFiles(requireContext(),10 * 1024 * 1024)
        }
    }

    private fun setView() {
        mBindingView.rvDir.apply {
            layoutManager = LinearLayoutManager(context)
            adapter = itemAdapter
        }
    }

    private fun getFilterFiles(index: Int){
        val fileType = when(index){
            1 -> MediaStorageTool.TYPE_BROWSER_IMAGE
            2 -> MediaStorageTool.TYPE_BROWSER_AUDIO
            3 -> MediaStorageTool.TYPE_BROWSER_VIDEO
            4 -> MediaStorageTool.TYPE_BROWSER_OTHER
            else -> ""
        }
        getAdapter()?.filterFileList(fileType)
    }
}