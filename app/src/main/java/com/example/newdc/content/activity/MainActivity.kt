package com.example.newdc.content.activity

import android.content.Intent
import android.graphics.Color
import android.os.Build
import android.view.LayoutInflater
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.fragment.app.Fragment
import androidx.viewpager2.widget.ViewPager2
import com.additional.max.AdConfig
import com.additional.max.MaxScreenAdManager
import com.example.newdc.BaseLifeCycle
import com.example.newdc.R
import com.example.newdc.content.BaseLayoutActivity
import com.example.newdc.content.fragment.FragmentHome
import com.example.newdc.content.fragment.FragmentMore
import com.example.newdc.content.fragment.FragmentSetting
import com.example.newdc.content.fragment.info.ActivityBaseInfo
import com.example.newdc.content.view.dialog.RateDialog
import com.example.newdc.dataadapter.FragmentAdapter
import com.example.newdc.databinding.ActivityMainBinding
import com.example.newdc.modelutil.data.EventData
import com.example.newdc.modelutil.data.StoreKeyData
import com.example.newdc.modelutil.tool.PermissionTool
import com.example.newdc.service.notify.NotifyConfig
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayout.OnTabSelectedListener
import com.google.android.material.tabs.TabLayoutMediator
import com.jeremyliao.liveeventbus.LiveEventBus


class MainActivity : BaseLayoutActivity<ActivityMainBinding>() {
    private var enterType: String? = null
    private var showReview = false
    private var maxScreenAdManager: MaxScreenAdManager? = null

    override fun layoutResource(): Int {
        return R.layout.activity_main
    }

    override fun onStart() {
        super.onStart()
        initAd()
    }

    override fun onDestroy() {
        super.onDestroy()
        maxScreenAdManager?.adDestroy()
    }

    override fun onResume() {
        super.onResume()
        if (!requestPermission() && showReview || enterType == NotifyConfig.TYPE_NOTY_INFO){
            showAdStart()
            showReview = false
        }
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        transToFunc()
    }

    override fun viewCreated() {
        setView()
        setPager()
        setListener()
        transToFunc()
    }

    private fun setListener() {
        LiveEventBus.get<Int>(EventData.EVENT_HOME_TAB_TRANSLATE).observe(this){
            mBindingView.fragmentPager.currentItem = it
        }

        LiveEventBus.get<String>(EventData.EVENT_RETURN_BACK).observe(this){
            showReview = true
        }

        LiveEventBus.get<String>(EventData.EVENT_AD_INIT).observe(this){
            initAd()
        }
    }

    private fun initAd(){
        if (AdConfig.getSwitchAdInters()){
            maxScreenAdManager = MaxScreenAdManager(true){
                mBindingView.llLoad.llLoad.visibility = View.GONE
                MaxScreenAdManager.loadAdCache(true,this,this)
            }.apply {
                initAd()
                adLoad(this@MainActivity)
            }
        }
    }

    private fun showAdStart(){
        if (AdConfig.getAllowShowInterstitial()){
            maxScreenAdManager?.startWaitingJob(this,
                time = 5,
                start = {
                    mBindingView.llLoad.llLoad.visibility = View.VISIBLE
                        },
                next = {
                    if (maxScreenAdManager?.showAd(this@MainActivity) == true){
                        mBindingView.llLoad.llLoad.visibility = View.GONE
                    }
                },
                end = {mBindingView.llLoad.llLoad.visibility = View.GONE}
            )
        }
    }

    private fun setView() {
        mBindingView.llTitleBar.tvTitle.text = getString(R.string.app_name)
        mBindingView.llTitleBar.tvTitle.setTextColor(Color.WHITE)
        navBack?.visibility = View.GONE
    }

    private fun setPager() {
        val pageList = mutableListOf<Fragment>(FragmentHome(),FragmentMore(),FragmentSetting())
        mBindingView.fragmentPager.apply {
            offscreenPageLimit = 3
            adapter = FragmentAdapter(pageList,supportFragmentManager,lifecycle)
            registerOnPageChangeCallback(object :ViewPager2.OnPageChangeCallback(){
                override fun onPageSelected(position: Int) {
                    mBindingView.tabsLayout.selectTab(mBindingView.tabsLayout.getTabAt(position))
                    if (position == 0){
                        mBindingView.llBackTop.setBackgroundResource(R.drawable.shape_bg_home_top)
                    }else{
                        mBindingView.llBackTop.setBackgroundResource(R.drawable.shape_bg_home_top_small)
                    }
                }
            })
        }

        mBindingView.tabsLayout.apply {
            addOnTabSelectedListener(object :OnTabSelectedListener{
                override fun onTabSelected(p0: TabLayout.Tab?) {
                    p0?.customView?.findViewById<TextView>(R.id.tv_tab)?.apply {
                        setTextColor(resources.getColor(R.color.color_tab_select,theme))
                    }
                    when(p0?.position){
                        0 -> mBindingView.llTitleBar.tvTitle.text = getString(R.string.app_name)
                        1 -> mBindingView.llTitleBar.tvTitle.text = getString(R.string.tab_more)
                        2 -> mBindingView.llTitleBar.tvTitle.text = getString(R.string.tab_setting)
                    }
                }

                override fun onTabUnselected(p0: TabLayout.Tab?) {
                    p0?.customView?.findViewById<TextView>(R.id.tv_tab)?.apply {
                        setTextColor(resources.getColor(R.color.color_tab_normal,theme))
                    }
                }

                override fun onTabReselected(p0: TabLayout.Tab?) {

                }
            })
        }

        val tab = TabLayoutMediator(
            mBindingView.tabsLayout, mBindingView.fragmentPager
        ) { tab, position ->
            tab.customView = selectTabView(position)
        }
        tab.attach()


    }

    private fun selectTabView(position: Int): View {
        val view = LayoutInflater.from(this).inflate(R.layout.layout_home_tab_view, null)
        val tabText = view.findViewById<TextView>(R.id.tv_tab)
        val tabImg = view.findViewById<ImageView>(R.id.img_tab)
        when(position){
            0 -> {
                tabImg.setImageResource(R.drawable.sel_tab_home)
                tabText.text = getString(R.string.tab_home)
            }
            1 -> {
                tabImg.setImageResource(R.drawable.sel_tab_more)
                tabText.text = getString(R.string.tab_more)
            }
            2 -> {
                tabImg.setImageResource(R.drawable.sel_tab_setting)
                tabText.text = getString(R.string.tab_setting)
            }
        }
        return view
    }

    override fun onBackPressed() {
        if (mBindingView.fragmentPager.currentItem != 0) {
            mBindingView.fragmentPager.currentItem = 0
        }else{
            moveTaskToBack(true)
        }
    }

    private fun requestPermission() : Boolean{
        if (showReview){
            if (RateDialog.canShowRateDialog()){
                RateDialog { RateDialog.startGPReview(this) }.show(supportFragmentManager,"dialog_review")
                return true
            }
        }
        if (!enterType.isNullOrBlank()) return false
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU
            && PermissionTool.allowPostNotifyRequest(StoreKeyData.getInt(StoreKeyData.PERMISSION_NOTIFY_COUNT))){
            PermissionTool.requestNotifyPost(this)
            return true
        }else{
            return PermissionTool.allowBatteryOptimizationRequest(supportFragmentManager,this)
        }
    }

    private fun transToFunc(){
        enterType = intent?.getStringExtra(BaseLifeCycle.BUNDLE_NOTIFY_INTENT)
        when(enterType){
            NotifyConfig.TYPE_NOTY_RAM,NotifyConfig.TYPE_NOTY_TEMP ->{
                startActivity(Intent(this,RealtimeActivity::class.java))
            }
            NotifyConfig.TYPE_NOTY_BATTERY ->{
                ActivityBaseInfo.startDevInfoActivity(this,6)
            }
            NotifyConfig.TYPE_NOTY_INFO ->{
                mBindingView.fragmentPager.currentItem = 1
            }
            NotifyConfig.TYPE_NOTY_STORAGE ->{
                startActivity(Intent(this,StorageActivity::class.java))
            }
            NotifyConfig.TYPE_NOTY_REDUNDANT ->{
                startBrowserActivity(BaseBrowserActivity.TYPE_BROWSER_REDUNDANT)
            }
            NotifyConfig.TYPE_NOTY_DUPLICATION ->{
                startBrowserActivity(BaseBrowserActivity.TYPE_BROWSER_DUPLICATE)
            }
            NotifyConfig.TYPE_NOTY_LARGE ->{
                startBrowserActivity(BaseBrowserActivity.TYPE_BROWSER_LARGE)
            }
            NotifyConfig.TYPE_NOTY_RECENT ->{
                startBrowserActivity(BaseBrowserActivity.TYPE_BROWSER_RECENT)
            }
        }
        intent?.removeExtra(BaseLifeCycle.BUNDLE_NOTIFY_INTENT)
    }
}