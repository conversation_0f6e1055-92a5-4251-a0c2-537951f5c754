package com.example.newdc.content.activity

import android.content.Intent
import android.os.Handler
import android.os.Looper
import android.view.View
import androidx.lifecycle.ViewModelProvider
import com.additional.max.AdConfig
import com.additional.max.MaxScreenAdManager
import com.example.newdc.R
import com.example.newdc.content.BaseLayoutActivity
import com.example.newdc.dataadapter.StorageFuncAdapter
import com.example.newdc.databinding.ActivityStoragePageBinding
import com.example.newdc.modelutil.DevValueViewModel
import com.example.newdc.modelutil.data.EventData
import com.example.newdc.modelutil.data.MoreInfoData
import com.example.newdc.modelutil.tool.ExtraTool.countDownFlow
import com.example.newdc.modelutil.tool.FormatTool
import com.jeremyliao.liveeventbus.LiveEventBus
import kotlinx.coroutines.Job

class StorageActivity: BaseLayoutActivity<ActivityStoragePageBinding>() {
    lateinit var storageViewModel: DevValueViewModel
    private var animDisposableJob: Job? = null
    private var maxScreenAdManager: MaxScreenAdManager? = null


    override fun layoutResource(): Int {
        return R.layout.activity_storage_page
    }

    override fun viewCreated() {
        storageViewModel = ViewModelProvider(this)[DevValueViewModel::class.java]
        setView()
        setListener()
        storageViewModel.getHomeRamAndRomValue(this)
        initAd()
        startAnim()
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        initAd()
        startAnim()
    }

    private fun initAd(){
        if (AdConfig.getSwitchAdInters()){
            maxScreenAdManager = MaxScreenAdManager(true){
                closeAnim()
            }.apply {
                initAd()
                adLoad(this@StorageActivity)
            }
        }
    }

    private fun setView() {
        mBindingView.llTitleBar.tvTitle.text = getString(R.string.info_storage)
        mBindingView.rvFunc.apply {
            adapter = StorageFuncAdapter(MoreInfoData.getStorageFuncData(this@StorageActivity)){
                startBrowserActivity(it)
            }
        }
        mBindingView.btnBrowse.setOnClickListener {enterBrowserActivity(BaseBrowserActivity.TYPE_BROWSER_DIR)}
        mBindingView.btnLarge.setOnClickListener { enterBrowserActivity(BaseBrowserActivity.TYPE_BROWSER_LARGE) }
        mBindingView.btnRedundant.setOnClickListener { enterBrowserActivity(BaseBrowserActivity.TYPE_BROWSER_REDUNDANT) }
        mBindingView.btnDuplicate.setOnClickListener { enterBrowserActivity(BaseBrowserActivity.TYPE_BROWSER_DUPLICATE) }
        mBindingView.btnRecent.setOnClickListener { enterBrowserActivity(BaseBrowserActivity.TYPE_BROWSER_RECENT) }
    }

    private fun startAnim(){
        animDisposableJob = countDownFlow(
            start = {
                mBindingView.lottPage.visibility = View.VISIBLE
                mBindingView.lottScan.playAnimation()
            },
            next = {},
            end = {
                if (maxScreenAdManager != null && AdConfig.getAllowShowInterstitial()){
                    maxScreenAdManager?.startWaitingJob(this,
                        next = {
                          if (maxScreenAdManager?.showAd(this) == true){
                              Handler(Looper.getMainLooper()).postDelayed({closeAnim() },500)
                          }
                        },
                        end = {
                            closeAnim()
                        })
                }else{
                    closeAnim()
                }
            }
        )
    }

    private fun closeAnim(){
        mBindingView.lottScan.apply {
            cancelAnimation()
            clearAnimation()
            visibility = View.GONE
        }
        mBindingView.lottPage.visibility = View.GONE
    }

    private fun setListener() {
        storageViewModel.getValueStorageTotal.observe(this){
            mBindingView.tvStoTotal.text = "/$it"
        }

        storageViewModel.getValueStoragePercent.observe(this){
            mBindingView.barStorage.setProgress(it.toFloat())
            mBindingView.tvStoPer.text = "$it%"
        }

        storageViewModel.getValueStorageUsed.observe(this){
            mBindingView.tvStoUsed.text = FormatTool.spannableStringBySize(it,24,13)
        }
    }

    private fun enterBrowserActivity(type: Int){
        startBrowserActivity(type)
    }

    override fun finish() {
        LiveEventBus.get<String>(EventData.EVENT_RETURN_BACK).post("")
        super.finish()
    }
}