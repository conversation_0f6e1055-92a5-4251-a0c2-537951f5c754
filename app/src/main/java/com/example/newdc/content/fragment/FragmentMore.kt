package com.example.newdc.content.fragment

import android.content.Intent
import com.example.newdc.R
import com.example.newdc.content.activity.AppBrowActivity
import com.example.newdc.content.activity.TestActivity
import com.example.newdc.content.fragment.info.ActivityBaseInfo
import com.example.newdc.dataadapter.MoreInfoAdapter
import com.example.newdc.databinding.FragmentMoreBinding
import com.example.newdc.modelutil.data.MoreInfoData

class FragmentMore: BaseLayoutFragment<FragmentMoreBinding>() {

    override fun layoutResource(): Int {
        return R.layout.fragment_more
    }

    override fun viewCreated() {
        mBindingView.rvMore.apply {
            adapter = MoreInfoAdapter(MoreInfoData.getHomeMoreInfoData(requireContext())){
                if (it in 1 .. 6){
                    ActivityBaseInfo.startDevInfoActivity(requireContext(),it)
                }
                if (it == 7){
                    startActivity(Intent(requireActivity(),AppBrowActivity::class.java))
                }
                if (it == 8){
                    startActivity(Intent(requireActivity(),TestActivity::class.java))
                }
            }
        }
    }
}