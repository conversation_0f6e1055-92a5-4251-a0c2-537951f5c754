package com.example.newdc.content.activity

import android.content.Context
import android.content.Intent
import android.util.Log
import androidx.fragment.app.Fragment
import com.bumptech.glide.Glide
import com.example.newdc.R
import com.example.newdc.content.BaseLayoutActivity
import com.example.newdc.content.fragment.info.FragmentAppCertificate
import com.example.newdc.content.fragment.info.FragmentAppGeneral
import com.example.newdc.content.fragment.info.FragmentAppPermission
import com.example.newdc.dataadapter.FragmentAdapter
import com.example.newdc.databinding.ActivityApplicationInfoBinding
import com.example.newdc.modelutil.dev.AppsTool
import com.google.android.material.tabs.TabLayoutMediator

class ApplicationInfoActivity: BaseLayoutActivity<ActivityApplicationInfoBinding>() {
    private var appPackageName: String? = null

    override fun layoutResource(): Int {
        return R.layout.activity_application_info
    }

    companion object{
        const val ENTER_PACKAGE = "enter_package"
        fun startApplicationInfoActivity(context: Context,packageName: String){
            val intent = Intent(context, ApplicationInfoActivity::class.java).apply {
                putExtra(ENTER_PACKAGE, packageName)
            }
            try {
                context.startActivity(intent)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    override fun viewCreated() {
        appPackageName = intent?.getStringExtra(ENTER_PACKAGE)
        val icon = AppsTool.getApkIconDrawable(this,appPackageName)
        Glide.with(this).load(icon).placeholder(R.mipmap.icon_type_apk).into(mBindingView.imgIcon)
        if (appPackageName != null) {
            val fragmentList = mutableListOf<Fragment>(FragmentAppGeneral(appPackageName!!),FragmentAppPermission(appPackageName!!),FragmentAppCertificate(appPackageName!!))
            val pagerAdapter = FragmentAdapter(fragmentList, supportFragmentManager,lifecycle)

            mBindingView.vpInfo.adapter = pagerAdapter
            mBindingView.vpInfo.offscreenPageLimit = 3

            TabLayoutMediator(mBindingView.tabInd,mBindingView.vpInfo) {
               tab, position ->
                when(position){
                    0 -> {tab.setText(R.string.tab_general)}
                    1 -> {tab.setText(R.string.tab_permission)}
                    2 -> {tab.setText(R.string.tab_certificate)}
                }
            }.attach()
        }
    }
}