package com.example.newdc.content.fragment.info

import androidx.lifecycle.ViewModelProvider
import com.example.newdc.R
import com.example.newdc.content.fragment.BaseLayoutFragment
import com.example.newdc.databinding.FragmentInfoSysBinding
import com.example.newdc.modelutil.DevInfoViewModel
import java.util.*

class FragmentInfoSys: BaseLayoutFragment<FragmentInfoSysBinding>() {
    private lateinit var devViewModel: DevInfoViewModel
    private lateinit var timer: Timer


    override fun layoutResource(): Int {
        return R.layout.fragment_info_sys
    }

    override fun onStart() {
        super.onStart()
        startTimer()
    }

    override fun onStop() {
        super.onStop()
        if(::timer.isInitialized){
            timer.cancel()
        }
    }

    override fun viewCreated() {
        devViewModel = ViewModelProvider(this)[DevInfoViewModel::class.java]
        mBindingView.devData = devViewModel
        mBindingView.lifecycleOwner = this
        devViewModel.getSysInfoData(requireContext())
        devViewModel.getSysDrmInfoData(requireContext())
        devViewModel.getSysJvmInfoData(requireContext())
    }

    private fun startTimer(){
        timer = Timer()
        timer.schedule(object : TimerTask() {
            override fun run() {
                devViewModel.getRunningTime()
            }
        },1000,1000)
    }
}