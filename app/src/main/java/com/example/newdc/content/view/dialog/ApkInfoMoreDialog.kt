package com.example.newdc.content.view.dialog

import android.content.Context
import android.content.Intent
import android.graphics.PixelFormat
import android.net.Uri
import android.os.Bundle
import android.provider.Settings
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import com.example.newdc.R
import com.example.newdc.content.activity.ApplicationInfoActivity
import com.example.newdc.databinding.DialogApkPopBinding
import com.example.newdc.modelutil.dev.AppsTool


class ApkInfoMoreDialog(private val context: Context, private val pkg: String):
    BaseDialogFragment<DialogApkPopBinding>() {

    override fun onStart() {
        super.onStart()
        val dialog = dialog
        if (dialog != null) {
            val window = dialog.window
            val windowParams = window!!.attributes
            windowParams.width = WindowManager.LayoutParams.MATCH_PARENT
            windowParams.height = WindowManager.LayoutParams.WRAP_CONTENT
            windowParams.flags = WindowManager.LayoutParams.FLAG_DIM_BEHIND or WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS
            windowParams.dimAmount = 0.8f
            windowParams.gravity = Gravity.BOTTOM
            window.attributes = windowParams
        }
    }

    override fun getBaseLayoutRes(): Int {
        return R.layout.dialog_apk_pop
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        mDataBinding.imgClose.setOnClickListener {
            dismissAllowingStateLoss()
        }

        mDataBinding.llDetail.setOnClickListener {
            ApplicationInfoActivity.startApplicationInfoActivity(context,pkg)
            dismissAllowingStateLoss()
        }

        mDataBinding.llSetting.setOnClickListener{
            if (pkg != null) {
                try {
                    val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
                    intent.data = Uri.fromParts("package", pkg, null)
                    context.startActivity(intent)
                    dismissAllowingStateLoss()
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        }

        mDataBinding.llShop.setOnClickListener {
            AppsTool.openGpApp(context,pkg)
            dismissAllowingStateLoss()
        }
    }
}