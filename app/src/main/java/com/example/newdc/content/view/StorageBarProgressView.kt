package com.example.newdc.content.view

import android.annotation.SuppressLint
import android.content.Context
import android.content.res.Resources
import android.graphics.*
import android.text.TextPaint
import android.util.AttributeSet
import android.util.TypedValue
import android.view.View
import com.example.newdc.R
import com.example.newdc.modelutil.tool.ExtraTool.dp2px
import com.example.newdc.modelutil.tool.ExtraTool.px2dp
import java.lang.Integer.min


class StorageBarProgressView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
    degStyleRes: Int = 0
) :
    View(context, attrs, defStyleAttr, degStyleRes) {
    private var mStrokeWidth = 0f
    private var mStartAngle = 0f
    private var mAngleSize = 0f
    private var mArcBgColor = 0
    private var mGradientStart = 0
    private var mGradientEnd = 0
    private var mMaxProgress = 0f
    private var mCurrentAngleSize = 0f
    private var mCurrentProgress = 0f
    private var mDuration: Long = 2000
    private var mProgressColor = 0
    private var mPercentText: String? = null
    private var mPercentColor = 0
    private var mPercentSize = 0f
    private var mInfoText: String? = null
    private var mInfoTextColor = Color.WHITE
    private var mInfoTextSize = 0f
    private var mTitleText: String? = null
    private var mTitleTextColor = 0
    private var mTitleTextSize = 0f
    private var mSetSubTitle = false

    init {
        initAttr(context, attrs)
    }

    //初始化绘制图画的参数
    @SuppressLint("CustomViewStyleable")
    private fun initAttr(context: Context, attrs: AttributeSet?) {
        val array = context.obtainStyledAttributes(attrs, R.styleable.CustomProgressBarView)
        mMaxProgress = array.getFloat(R.styleable.CustomProgressBarView_bar_max_progress, 100f)
        mArcBgColor = array.getColor(R.styleable.CustomProgressBarView_bar_bg_color, Color.GRAY)
        mGradientStart = array.getColor(R.styleable.CustomProgressBarView_bar_bg_start_color, Color.BLUE)
        mGradientEnd = array.getColor(R.styleable.CustomProgressBarView_bar_bg_end_color, Color.BLUE)
        mStrokeWidth = array.getDimension(R.styleable.CustomProgressBarView_bar_stroke_width, 10f.dp2px)
        mCurrentProgress = array.getFloat(R.styleable.CustomProgressBarView_bar_progress, 0f)
        mCurrentAngleSize = 200f
        mProgressColor = array.getColor(R.styleable.CustomProgressBarView_bar_progress_color, Color.WHITE)
        mPercentText = array.getString(R.styleable.CustomProgressBarView_bar_percent_text)
        mPercentSize = array.getDimension(R.styleable.CustomProgressBarView_bar_percent_text_size, 25f)
        mPercentColor = array.getColor(R.styleable.CustomProgressBarView_bar_percent_text_color, context.resources.getColor(R.color.white))
        mInfoText = array.getString(R.styleable.CustomProgressBarView_bar_info_text)
        mInfoTextColor = array.getColor(R.styleable.CustomProgressBarView_bar_info_text_color, context.resources.getColor(R.color.black))
        mInfoTextSize = array.getDimension(R.styleable.CustomProgressBarView_bar_info_text_size, 40f)
        mTitleText = array.getString(R.styleable.CustomProgressBarView_bar_title_text)
        mTitleTextSize = array.getDimension(R.styleable.CustomProgressBarView_bar_title_text_size, 20f)
        mTitleTextColor = array.getColor(R.styleable.CustomProgressBarView_bar_title_text_color, context.resources.getColor(R.color.black))
        mAngleSize = array.getFloat(R.styleable.CustomProgressBarView_bar_angle_size, 270f)
        mStartAngle = array.getFloat(R.styleable.CustomProgressBarView_bar_start_angle, 135f)
        mSetSubTitle = array.getBoolean(R.styleable.CustomProgressBarView_bar_set_sub_title, false)
        array.recycle()
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        val widthMode = MeasureSpec.getMode(widthMeasureSpec)
        val widthSize = MeasureSpec.getSize(widthMeasureSpec)
        val heightMode = MeasureSpec.getMode(heightMeasureSpec)
        val heightSize = MeasureSpec.getSize(heightMeasureSpec)

        // 根据测量要求计算出 View 的宽度和高度
        val desiredWidth = 300// 根据需求计算宽度
        val desiredHeight = 300// 根据需求计算高度

        // 根据测量模式设置最终的测量结果
        val width = when (widthMode) {
            MeasureSpec.EXACTLY -> widthSize
            MeasureSpec.AT_MOST -> min(desiredWidth, widthSize)
            MeasureSpec.UNSPECIFIED -> desiredWidth
            else -> desiredWidth
        }

        val height = when (heightMode) {
            MeasureSpec.EXACTLY -> heightSize
            MeasureSpec.AT_MOST -> min(desiredHeight, heightSize)
            MeasureSpec.UNSPECIFIED -> desiredHeight
            else -> desiredHeight
        }

        // 设置最终测量结果
        setMeasuredDimension(width, height)
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        val centerX = width / 2
        val rectF = RectF()
        rectF.left = mStrokeWidth
        rectF.top = mStrokeWidth
        rectF.right = centerX * 2 - mStrokeWidth
        rectF.bottom = centerX * 2 - mStrokeWidth

        drawArcBg(canvas, rectF)
        drawArcProgress(canvas, rectF)
    }

    private fun drawArcProgress(canvas: Canvas, rectF: RectF) {
        val mPaint = Paint()
        mPaint.style = Paint.Style.STROKE
        mPaint.strokeCap = Paint.Cap.ROUND
        mPaint.strokeWidth = mStrokeWidth
        mPaint.isAntiAlias = true
        mPaint.color = mProgressColor

        val shader = SweepGradient((width / 2).toFloat(), (height / 2).toFloat(), intArrayOf(mGradientStart, mGradientEnd), null)
        val matrix = Matrix()
        matrix.setRotate(90f, (width / 2).toFloat(), (height / 2).toFloat())
        shader.setLocalMatrix(matrix)
        mPaint.setShader(shader)

        canvas.drawArc(rectF, mStartAngle, mCurrentAngleSize, false, mPaint)
    }

    private fun drawArcBg(canvas: Canvas, rectF: RectF) {
        val mPaint = Paint()
        mPaint.isAntiAlias = true
        mPaint.style = Paint.Style.STROKE
        mPaint.strokeWidth = mStrokeWidth
        mPaint.color = mArcBgColor
        mPaint.strokeCap = Paint.Cap.ROUND
        canvas.drawArc(rectF, mStartAngle, mAngleSize, false, mPaint)

        mPaint.strokeWidth = 1f.dp2px
        mPaint.color = mArcBgColor
        val subRectF = RectF(rectF.left + mStrokeWidth,rectF.top + mStrokeWidth,rectF.right - mStrokeWidth, rectF.bottom - mStrokeWidth)
        canvas.drawArc(subRectF, mStartAngle, mAngleSize, false, mPaint)
    }

    fun setProgress(progress: Float) {
        var progress = progress
        var oldProgress = mCurrentProgress
        if (progress == mCurrentProgress) return
        if (progress < 0) {
            progress = 0f
        }
        if (progress > mMaxProgress) {
            progress = mMaxProgress
        }
        mCurrentProgress = progress
        val size = mCurrentProgress / mMaxProgress
        mCurrentAngleSize = (mAngleSize * size).toInt().toFloat()
        invalidate()
//        setAnimator(oldProgress, mCurrentAngleSize)
    }
}