package com.example.newdc.content.view

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Canvas
import android.graphics.Matrix
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import com.example.newdc.R
import com.example.newdc.modelutil.tool.ExtraTool.dp2px

class RateStartView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) :
    View(context, attrs, defStyleAttr) {
    private var mStarNormalBitmap: Bitmap? = null
    private var mStarSelectedBitmap: Bitmap? = null
    private var startCount = 5
    private var starSize = 40f.dp2px
    private var starMargin = 5f.dp2px
    private var currStarCount = 0

    private var itemChangeListener: ItemChangeListener? = null

    init {
        init(context, attrs)
    }

    private fun init(context: Context, attrs: AttributeSet?) {
        val array = context.obtainStyledAttributes(attrs, R.styleable.RateStarsView)
        val emptyImg = array.getResourceId(R.styleable.RateStarsView_emptyView, R.mipmap.icon_start_e)
        mStarNormalBitmap = setImgSize(BitmapFactory.decodeResource(resources, emptyImg), starSize)
        val fullImg = array.getResourceId(R.styleable.RateStarsView_fullView, R.mipmap.icon_start_f)
        mStarSelectedBitmap = setImgSize(BitmapFactory.decodeResource(resources, fullImg), starSize)
        startCount = array.getInt(R.styleable.RateStarsView_starCount, startCount)
        array.recycle()
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        val widthMode = MeasureSpec.getMode(widthMeasureSpec)
        val heightMode = MeasureSpec.getMode(heightMeasureSpec)
        var width = MeasureSpec.getSize(widthMeasureSpec)
        var height = MeasureSpec.getSize(widthMeasureSpec)
        val currWidth = ((starSize + starMargin) * startCount - starMargin).toInt()
        if (widthMode == MeasureSpec.EXACTLY) {
            if (currWidth > width) {
                starMargin = 2f.dp2px
                starSize = width / startCount - starMargin
                mStarNormalBitmap = setImgSize(mStarNormalBitmap, starSize)
                mStarSelectedBitmap = setImgSize(mStarSelectedBitmap, starSize)
            }
        }
        if (widthMode == MeasureSpec.AT_MOST) {
            width = currWidth
        }
        if (heightMode == MeasureSpec.AT_MOST) {
            height = 60f.dp2px.toInt()
        }
        setMeasuredDimension(width, height)
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        for (i in 0 until startCount) {
            val x = (i * (starSize + starMargin)).toInt()
            if (currStarCount > i) {
                canvas.drawBitmap(mStarSelectedBitmap!!, x.toFloat(), 0f, null)
            } else {
                canvas.drawBitmap(mStarNormalBitmap!!, x.toFloat(), 0f, null)
            }
        }
    }

    override fun onTouchEvent(event: MotionEvent): Boolean {
        when (event.action) {
            MotionEvent.ACTION_DOWN, MotionEvent.ACTION_MOVE -> {
                val moveX = event.x
                var count = (moveX / (starSize + starMargin)).toInt() + 1
                if (count < 0) {
                    count = 0
                }
                if (count > startCount) {
                    count = startCount
                }
                if (count == currStarCount) {
                    return true
                }
                currStarCount = count
                itemChangeListener?.onItemChange(currStarCount)
                invalidate()
            }
        }
        return true
    }

    private fun setImgSize(bm: Bitmap?, starSize: Float): Bitmap {
        val width = bm!!.getWidth()
        val height = bm.getHeight()
        val scaleWidth = starSize / width
        val scaleHeight = starSize / height
        val matrix = Matrix()
        matrix.postScale(scaleWidth, scaleHeight)
        return Bitmap.createBitmap(bm, 0, 0, width, height, matrix, true)
    }

    interface ItemChangeListener{
        fun onItemChange(position:Int)
    }

    fun setItemChangeListener(listener: ItemChangeListener){
        itemChangeListener = listener
    }

}


