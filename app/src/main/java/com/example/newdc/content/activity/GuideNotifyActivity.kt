package com.example.newdc.content.activity

import android.content.Intent
import android.os.Build
import androidx.annotation.RequiresApi
import com.example.newdc.R
import com.example.newdc.content.BaseLayoutActivity
import com.example.newdc.databinding.LayoutGuildPerBinding
import com.example.newdc.modelutil.data.StoreKeyData
import com.example.newdc.modelutil.tool.PermissionTool

class GuideNotifyActivity: BaseLayoutActivity<LayoutGuildPerBinding>() {
    override fun layoutResource(): Int {
        return R.layout.layout_guild_per
    }

    @RequiresApi(Build.VERSION_CODES.TIRAMISU)
    override fun viewCreated() {
        StoreKeyData.setBoolean(StoreKeyData.PERMISSION_HAS_GUILD,true)
        mBindingView.tvNot.setOnClickListener {
            startActivity(Intent(this, MainActivity::class.java))
            finish()
        }

        mBindingView.btnContinue.setOnClickListener {
            if (PermissionTool.allowPostNotifyRequest(StoreKeyData.getInt(StoreKeyData.PERMISSION_NOTIFY_COUNT))){
                PermissionTool.requestNotifyPost(this)
            }else{
                startActivity(Intent(this, MainActivity::class.java))
                finish()
            }
        }
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode == PermissionTool.PERMISSION_POST_NOTIFY_CODE){
            startActivity(Intent(this, MainActivity::class.java))
            finish()
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == PermissionTool.PERMISSION_POST_NOTIFY_CODE){
            startActivity(Intent(this, MainActivity::class.java))
            finish()
        }
    }

}