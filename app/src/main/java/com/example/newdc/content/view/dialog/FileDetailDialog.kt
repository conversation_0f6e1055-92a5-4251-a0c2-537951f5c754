package com.example.newdc.content.view.dialog

import android.os.Bundle
import android.text.format.Formatter
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.FragmentManager
import com.example.newdc.R
import com.example.newdc.databinding.DialogFileChangeBinding
import com.example.newdc.databinding.DialogFileDetailBinding
import com.example.newdc.modelutil.data.ItemFileData
import com.example.newdc.modelutil.tool.FilesTool
import com.example.newdc.modelutil.tool.FormatTool
import com.example.newdc.modelutil.tool.MediaStorageTool
import java.io.File
import java.util.*

class FileDetailDialog(val item: ItemFileData): BaseDialogFragment<DialogFileDetailBinding>() {
    override fun getBaseLayoutRes(): Int {
        return  R.layout.dialog_file_detail
    }


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        mDataBinding.btnYes.setOnClickListener {
            dismissAllowingStateLoss()
        }

        mDataBinding.valueName.text = item.title
        mDataBinding.valuePath.text = item.filePath
        mDataBinding.valueSize.text = Formatter.formatFileSize(context, item.fileSize)
        mDataBinding.valueDate.text = FormatTool.certDateFormat(Date(item.modifyDate))
    }
}