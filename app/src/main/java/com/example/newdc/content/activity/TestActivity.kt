package com.example.newdc.content.activity

import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import com.additional.max.AdConfig
import com.additional.max.MaxScreenAdManager
import com.example.newdc.R
import com.example.newdc.content.BaseLayoutActivity
import com.example.newdc.dataadapter.ItemTestAdapter
import com.example.newdc.databinding.ActivityTestBinding
import com.example.newdc.modelutil.data.TestData

class TestActivity: BaseLayoutActivity<ActivityTestBinding>() {
    private var testAdapter: ItemTestAdapter? = null
    private var maxScreenAdManager: MaxScreenAdManager? = null

    override fun layoutResource(): Int {
        return R.layout.activity_test
    }

    override fun onStart() {
        super.onStart()
        if (testAdapter != null) {
            testAdapter?.notifyDataSetChanged()
        }
    }

    override fun onDestroy() {
        maxScreenAdManager?.adDestroy()
        super.onDestroy()
    }

    override fun viewCreated() {
        testAdapter = ItemTestAdapter(TestData.getTestList()){
            ActivityTestDetail.enterTestDetailActivity(this,it)
        }
        mBindingView.titleBar.tvTitle.text = getString(R.string.title_tests)
        mBindingView.rvTest.apply {
            layoutManager = LinearLayoutManager(this@TestActivity)
            adapter = testAdapter
        }
        initAd()
    }

    private fun initAd(){
        if (AdConfig.getSwitchAdInters() && AdConfig.getAllowShowInterstitial()){
            maxScreenAdManager = MaxScreenAdManager(true).apply {
                initAd()
                adLoad(this@TestActivity)
            }

            maxScreenAdManager?.startWaitingJob(this@TestActivity,
                start = {mBindingView.llLoad.llLoad.visibility = View.VISIBLE},
                next = {
                    if(maxScreenAdManager?.showAd(this@TestActivity) == true){
                        mBindingView.llLoad.llLoad.visibility = View.GONE
                    }
                },
                end = {mBindingView.llLoad.llLoad.visibility = View.GONE})
        }
    }
}