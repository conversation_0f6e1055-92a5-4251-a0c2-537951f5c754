package com.example.newdc.content.activity

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.content.Context
import android.content.Intent
import android.view.View
import com.additional.max.AdConfig
import com.additional.max.MaxScreenAdManager
import com.example.newdc.R
import com.example.newdc.content.BaseLayoutActivity
import com.example.newdc.databinding.ActivityAnimTransBinding
import com.example.newdc.modelutil.tool.ExtraTool.countDownFlow
import kotlinx.coroutines.Job

class AnimTransActivity: BaseLayoutActivity<ActivityAnimTransBinding>() {
    private var funcType = 0
    private var animDisposableJob: Job? = null
    private var maxScreenAdManager: MaxScreenAdManager? = null

    override fun layoutResource(): Int {
        return R.layout.activity_anim_trans
    }

    companion object{
        const val ENTER_TYPE = "ENTER_TYPE"
        fun enterTransActivity(context: Context, type:Int){
            val intent = Intent(context, AnimTransActivity::class.java).apply {
                putExtra(ENTER_TYPE,type)
            }
            try {
                context.startActivity(intent)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    override fun viewCreated() {
        funcType = intent.getIntExtra(ENTER_TYPE,0)
        initAnimResource()
        initAd()
        animDisposableJob = countDownFlow(
            start = {
                mBindingView.lollClean.playAnimation()
                    },
            next = {

            },
            end = {
                mBindingView.lollClean.apply {
                    cancelAnimation()
                    clearAnimation()
                    visibility = View.GONE
                }
                mBindingView.lollEnd.apply {
                    visibility = View.VISIBLE
                    playAnimation()
                }
            }
        )
    }

    private fun initAd(){
        if (AdConfig.getSwitchAdInters()){
            maxScreenAdManager = MaxScreenAdManager(true){
                CompleteActivity.enterCompleteActivity(this@AnimTransActivity,funcType)
                finish()
            }.apply {
                initAd()
                adLoad(this@AnimTransActivity)
            }
        }
    }


    private fun initAnimResource() {
        var cleanJsonRes = ""
        var cleanImagesFolds = ""
        when (funcType) {
            BaseBrowserActivity.TYPE_BROWSER_REDUNDANT ->{
                cleanJsonRes = "func/redundant/clean/redundant-deleting.json"
                cleanImagesFolds = "func/redundant/clean/images"
            }
            BaseBrowserActivity.TYPE_BROWSER_LARGE ->{
                cleanJsonRes = "func/large/clean/large-deleting.json"
                cleanImagesFolds = "func/large/clean/images"
            }
            BaseBrowserActivity.TYPE_BROWSER_DUPLICATE ->{
                cleanJsonRes = "func/duplicate/clean/duplicate-deleting.json"
                cleanImagesFolds = "func/duplicate/clean/images"
            }
            BaseBrowserActivity.TYPE_BROWSER_RECENT ->{
                cleanJsonRes = "func/recent/clean/recent-deleting.json"
                cleanImagesFolds = "func/recent/clean/images"
            }
            else ->{
                cleanJsonRes = "func/redundant/clean/redundant-deleting.json"
                cleanImagesFolds = "func/redundant/clean/images"
            }
        }
        mBindingView.lollClean.setAnimation(cleanJsonRes)
        mBindingView.lollClean.imageAssetsFolder = cleanImagesFolds
        mBindingView.lollEnd.addAnimatorListener(object : AnimatorListenerAdapter(){
            override fun onAnimationEnd(animation: Animator) {
                if (maxScreenAdManager != null && AdConfig.getAllowShowInterstitial()){
                    maxScreenAdManager?.startWaitingJob(this@AnimTransActivity,
                        next = {
                            maxScreenAdManager?.showAd(this@AnimTransActivity)
                        },
                        end = {
                            CompleteActivity.enterCompleteActivity(this@AnimTransActivity,funcType)
                            finish()
                        })
                }else{
                    CompleteActivity.enterCompleteActivity(this@AnimTransActivity,funcType)
                    finish()
                }
            }
        })
    }

    override fun onDestroy() {
        maxScreenAdManager?.adDestroy()
        super.onDestroy()
    }
}