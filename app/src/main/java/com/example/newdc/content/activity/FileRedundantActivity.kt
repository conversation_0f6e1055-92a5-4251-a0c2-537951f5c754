package com.example.newdc.content.activity

import android.view.View
import androidx.lifecycle.ViewModelProvider
import com.additional.max.AdConfig
import com.additional.max.MaxScreenAdManager
import com.example.newdc.R
import com.example.newdc.dataadapter.FileRedundantAdapter
import com.example.newdc.databinding.ActivityBrowRedundantBinding
import com.example.newdc.modelutil.FilesStorageViewModel
import com.example.newdc.modelutil.data.EventData
import com.example.newdc.modelutil.tool.ExtraTool.countDownFlow
import com.jeremyliao.liveeventbus.LiveEventBus
import kotlinx.coroutines.Job

class FileRedundantActivity: BaseControlActivity<ActivityBrowRedundantBinding>() {
    private lateinit var filesStorageViewModel: FilesStorageViewModel
    private var expandAdapter: FileRedundantAdapter? = null
    private var animDisposableJob: Job? = null
    private var maxScreenAdManager: MaxScreenAdManager? = null

    override fun layoutResource(): Int {
        return R.layout.activity_brow_redundant
    }

    override fun viewCreated() {
        browserType = BaseBrowserActivity.TYPE_BROWSER_REDUNDANT
        mBindingView.titleBar.tvTitle.text = getString(R.string.title_brow_redundant)
        filesStorageViewModel = ViewModelProvider(this)[FilesStorageViewModel::class.java]
        expandAdapter = FileRedundantAdapter{
            changeBarStatus(it.size)
            selectItem = it
        }
        mBindingView.rvRedundant.apply {
            setAdapter(expandAdapter)
        }

        filesStorageViewModel.getBrowserRedundantList.observe(this){
            mBindingView.llLoad.llLoad.visibility = View.GONE
            expandAdapter?.setData(it)
        }

        LiveEventBus.get<String>(EventData.EVENT_FILE_CHANGED).observe(this) {
            expandAdapter?.notifyDataSetChanged()
        }

        LiveEventBus.get<String>(EventData.EVENT_FILE_DELETE).observe(this){
            deleteToOtherPage(BaseBrowserActivity.TYPE_BROWSER_REDUNDANT)
        }

        mBindingView.btnClean.setOnClickListener {
            deleteToOtherPage(BaseBrowserActivity.TYPE_BROWSER_REDUNDANT)
        }

        mBindingView.llLoad.llLoad.visibility = View.VISIBLE
        filesStorageViewModel.getRedundantFileData(this)
        initAd()
        startAnim()
    }

    fun startAnim(){
        animDisposableJob = countDownFlow(
            start = {
                mBindingView.lottPage.visibility = View.VISIBLE
                mBindingView.lottScan.playAnimation()
            },
            next = {

            },
            end = {
                if (maxScreenAdManager != null && AdConfig.getAllowShowInterstitial()){
                    maxScreenAdManager?.startWaitingJob(this,
                        next = {
                            if (maxScreenAdManager?.showAd(this) == true){
                                closeAnim()
                            }
                        },
                        end = {
                            closeAnim()
                        })
                }else{
                    closeAnim()
                }
            }
        )
    }

    private fun initAd(){
        if (AdConfig.getSwitchAdInters()){
            maxScreenAdManager = MaxScreenAdManager(true).apply {
                initAd()
                adLoad(this@FileRedundantActivity)
            }
        }
    }

    private fun closeAnim(){
        mBindingView.lottScan.apply {
            cancelAnimation()
            clearAnimation()
            visibility = View.GONE
        }
        mBindingView.lottPage.visibility = View.GONE
    }

    override fun finish() {
        LiveEventBus.get<String>(EventData.EVENT_RETURN_BACK).post("")
        super.finish()
    }

    override fun onDestroy() {
        maxScreenAdManager?.adDestroy()
        super.onDestroy()
    }
}