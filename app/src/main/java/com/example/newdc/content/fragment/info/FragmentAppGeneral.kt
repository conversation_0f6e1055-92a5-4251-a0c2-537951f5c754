package com.example.newdc.content.fragment.info

import androidx.lifecycle.ViewModelProvider
import com.example.newdc.R
import com.example.newdc.content.fragment.BaseLayoutFragment
import com.example.newdc.databinding.FragmentAppGeneralBinding
import com.example.newdc.databinding.FragmentInfoSysBinding
import com.example.newdc.modelutil.AppInfoViewModel
import com.example.newdc.modelutil.DevInfoViewModel
import java.util.*

class FragmentAppGeneral(private val packageName: String): BaseLayoutFragment<FragmentAppGeneralBinding>() {
    private lateinit var appInfoViewModel: AppInfoViewModel


    override fun layoutResource(): Int {
        return R.layout.fragment_app_general
    }

    override fun viewCreated() {
        appInfoViewModel = ViewModelProvider(this)[AppInfoViewModel::class.java]
        mBindingView.info = appInfoViewModel
        mBindingView.lifecycleOwner = this

        appInfoViewModel.getAppGeneralInfo(requireContext(),packageName)

    }
}