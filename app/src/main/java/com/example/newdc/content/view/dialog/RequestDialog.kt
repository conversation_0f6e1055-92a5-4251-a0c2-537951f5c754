package com.example.newdc.content.view.dialog

import android.os.Bundle
import android.text.format.Formatter
import android.view.LayoutInflater
import android.view.View
import com.example.newdc.R
import com.example.newdc.databinding.DialogRequestBinding

class RequestDialog: BaseDialogFragment<DialogRequestBinding> {
    private var type: Int = 0
    private var call: (() -> Unit)? = null
    private var close: (() -> Unit)? = null

    override fun getBaseLayoutRes(): Int {
        return  R.layout.dialog_request
    }

    companion object{
        const val TYPE_STORAGE = 1
        const val TYPE_NOTIFY = 2
        const val TYPE_BATTERY = 3
    }

    constructor(type: Int, call: () -> Unit, close: () -> Unit = {}) : this() {
        this.type = type
        this.call = call
        this.close = close
        val args = Bundle()
        args.putInt("type", type)
        this.arguments = args
    }

    constructor()

    init {
        val args = arguments
        if (args != null) {
            type = args.getInt("type")
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        when(type){
            TYPE_STORAGE -> {
                mDataBinding.tvInfo.text = getString(R.string.per_tip_storage)
                mDataBinding.imgPer.setImageResource(R.mipmap.per_storage)
            }
            TYPE_NOTIFY -> {
                mDataBinding.tvInfo.text = getString(R.string.per_tip_notify)
                mDataBinding.imgPer.setImageResource(R.mipmap.per_notify)
            }
            TYPE_BATTERY -> {
                mDataBinding.tvInfo.text = getString(R.string.per_tip_battery)
                mDataBinding.imgPer.setImageResource(R.mipmap.per_battery)
            }
        }


        mDataBinding.imgClose.setOnClickListener {
            close?.invoke()
            dismissAllowingStateLoss()
        }

        mDataBinding.btnYes.setOnClickListener {
            call?.invoke()
            dismissAllowingStateLoss()
        }
    }
}