package com.example.newdc.dataadapter

import android.annotation.SuppressLint
import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.*
import androidx.recyclerview.widget.RecyclerView
import com.example.newdc.R
import com.example.newdc.modelutil.dev.AppPermissionInfoData

class ApkPermissionAdapter: RecyclerView.Adapter<ApkPermissionAdapter.ItemViewHolder>() {
    private val perList = mutableListOf<AppPermissionInfoData>()
    private var mContext: Context? = null

    inner class ItemViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView){
        val tvName = itemView.findViewById<TextView>(R.id.tv_name)
        val tvGrant = itemView.findViewById<TextView>(R.id.tv_grant)
        val tvLevel = itemView.findViewById<TextView>(R.id.tv_level)
        val tvConstant = itemView.findViewById<TextView>(R.id.tv_constant)
        val tvDescribe = itemView.findViewById<TextView>(R.id.tv_describe)
    }

    override fun onCreateViewHolder(p0: ViewGroup, p1: Int): ItemViewHolder {
        mContext = p0.context
        return ItemViewHolder(LayoutInflater.from(p0.context).inflate(R.layout.item_app_permission, p0, false))
    }

    override fun getItemCount(): Int {
        return perList.size
    }

    @SuppressLint("SetTextI18n")
    override fun onBindViewHolder(p0: ItemViewHolder, p1: Int) {
        val perData = perList[p1]
        p0.apply {
            tvName.text = perData.perName
            tvGrant.text = if (perData.perStatus) mContext?.getString(R.string.app_granted) else mContext?.getString(R.string.app_not_granted)
            tvLevel.text = perData.perLevel.ifBlank { "N/A" }
            tvConstant.text = perData.perContent
            tvDescribe.text = perData.describe
            if (perData.describe.isBlank()) {tvDescribe.visibility = View.GONE}
        }
    }

    fun setApkData(list: MutableList<AppPermissionInfoData>){
        perList.clear()
        perList.addAll(list)
        notifyDataSetChanged()
    }

}