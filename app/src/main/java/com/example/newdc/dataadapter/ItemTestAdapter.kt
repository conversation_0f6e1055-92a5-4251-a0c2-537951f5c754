package com.example.newdc.dataadapter

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.example.newdc.R
import com.example.newdc.modelutil.data.MoreInfoData
import com.example.newdc.modelutil.data.StoreKeyData
import com.example.newdc.modelutil.data.TestData

class ItemTestAdapter(list: MutableList<TestData>, val onCall: (data: TestData) -> Unit = {}): RecyclerView.Adapter<ItemTestAdapter.ItemViewHolder>() {
    private val testList = list
    private var mContest: Context? = null

    inner class ItemViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView){
        val tvTitle = itemView.findViewById<TextView>(R.id.tv_test)
        val imgStatus = itemView.findViewById<ImageView>(R.id.img_status)
    }

    override fun onCreateViewHolder(p0: ViewGroup, p1: Int): ItemViewHolder {
        mContest = p0.context
        return ItemViewHolder(LayoutInflater.from(p0.context).inflate(R.layout.item_test, p0, false))
    }

    override fun getItemCount(): Int {
        return testList.size
    }

    override fun onBindViewHolder(p0: ItemViewHolder, p1: Int) {
        val testData = testList[p1]
        p0.apply {
            tvTitle.text = mContest?.getString(testData.testName)
            itemView.setOnClickListener {onCall(testData)}
            val status = StoreKeyData.getInt(testData.testIntent)
            when (status) {
                0 ->{imgStatus.setImageResource(R.mipmap.nav_icon_unknow)}
                1 ->{imgStatus.setImageResource(R.mipmap.nav_icon_ok)}
                -1 ->{imgStatus.setImageResource(R.mipmap.nav_icon_no)}
            }
        }
    }
}