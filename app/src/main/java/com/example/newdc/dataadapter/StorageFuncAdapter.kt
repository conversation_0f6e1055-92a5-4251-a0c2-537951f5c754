package com.example.newdc.dataadapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.example.newdc.R
import com.example.newdc.modelutil.data.MoreInfoData

class StorageFuncAdapter(list: MutableList<MoreInfoData>, val onCall: (type: Int) -> Unit = {}): RecyclerView.Adapter<StorageFuncAdapter.ItemViewHolder>() {
    private val infoList = list

    inner class ItemViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView){
        val tvInfo = itemView.findViewById<TextView>(R.id.tv_info_name)
        val imgBack = itemView.findViewById<RelativeLayout>(R.id.ll_back)
        val imgInfo = itemView.findViewById<ImageView>(R.id.img_info)
    }

    override fun onCreateViewHolder(p0: ViewGroup, p1: Int): ItemViewHolder {
        return ItemViewHolder(LayoutInflater.from(p0.context).inflate(R.layout.item_func_tab, p0, false))
    }

    override fun getItemCount(): Int {
        return infoList.size
    }

    override fun onBindViewHolder(p0: ItemViewHolder, p1: Int) {
        val infoData = infoList[p1]
        p0.apply {
            tvInfo.text = infoData.name
            imgBack.setBackgroundResource(infoData.backgroundColor)
            imgInfo.setImageResource(infoData.iconRes)
            itemView.setOnClickListener {onCall(infoData.type)}
        }
    }
}