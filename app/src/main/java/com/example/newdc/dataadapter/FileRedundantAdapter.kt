package com.example.newdc.dataadapter

import android.text.format.Formatter
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.*
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.bumptech.glide.request.RequestOptions
import com.example.newdc.R
import com.example.newdc.modelutil.data.ItemFileData
import com.example.newdc.modelutil.data.ReItemGroupData
import com.example.newdc.modelutil.tool.ExtraTool.dp2px
import com.example.newdc.modelutil.tool.MediaStorageTool

class FileRedundantAdapter(val click: (selectItems: MutableList<ItemFileData>) -> Unit = {}): BaseExpandableListAdapter() {
    private var groupList = mutableListOf<ReItemGroupData>()
    private var selectItemList = mutableListOf<ItemFileData>()

    override fun getGroupCount(): Int {
        return groupList.size
    }

    override fun getChildrenCount(groupPosition: Int): Int {
        return groupList[groupPosition].fileList.size
    }

    override fun getGroup(groupPosition: Int): Any {
        return groupList[groupPosition]
    }

    override fun getChild(groupPosition: Int, childPosition: Int): Any {
        return groupList[groupPosition].fileList[childPosition]
    }

    override fun getGroupId(groupPosition: Int): Long {
        return groupPosition.toLong()
    }

    override fun getChildId(groupPosition: Int, childPosition: Int): Long {
        return childPosition.toLong()
    }

    override fun hasStableIds(): Boolean {
        return true
    }

    override fun getGroupView(groupPosition: Int, isExpanded: Boolean, convertView: View?, parent: ViewGroup?): View {
        var mView = convertView
        val groupHolder: GroupViewHolder
        if (convertView == null) {
            mView = LayoutInflater.from(parent!!.context).inflate(R.layout.item_expand_parent, parent, false)
            groupHolder = GroupViewHolder(mView)
            mView?.tag = groupHolder
        } else {
            groupHolder = mView?.tag as GroupViewHolder
        }

        val data = groupList[groupPosition]

        val separator = View(parent!!.context)
        separator.layoutParams = LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, 20f.dp2px.toInt())

        if (isExpanded) {
            groupHolder.imgArrow.setImageResource(R.mipmap.nav_arrow_down)
        } else {
            groupHolder.imgArrow.setImageResource(R.mipmap.nav_arrow_right)
        }

        groupHolder.tvTitle.text = data.title
        groupHolder.allSize.text = Formatter.formatFileSize(parent?.context, data.allSize)

        groupHolder.ckAll.apply {
            setOnCheckedChangeListener(null)
            isChecked = data.isSelect
            setOnCheckedChangeListener { buttonView, isChecked ->
                data.isSelect = isChecked
                selectGroupAllItem(isChecked,groupPosition)
                if (isExpanded) notifyDataSetChanged()
                click(getSelectItems())
            }
        }

        return mView!!
    }

    override fun getChildView(
        groupPosition: Int,
        childPosition: Int,
        isLastChild: Boolean,
        convertView: View?,
        parent: ViewGroup?
    ): View {
        var mView = convertView
        val childHolder: ChildViewHolder
        if (convertView == null) {
            mView = LayoutInflater.from(parent!!.context).inflate(R.layout.item_expand_child, parent, false)
            childHolder = ChildViewHolder(mView)
            mView?.tag = childHolder
        } else {
            childHolder = mView?.tag as ChildViewHolder
        }

        val data = groupList[groupPosition].fileList[childPosition]

        childHolder.tvTitle.text = data.title
        childHolder.tvSize.text = Formatter.formatFileSize(parent?.context,data.fileSize)
        if (data.type == MediaStorageTool.TYPE_BROWSER_IMAGE || data.type == MediaStorageTool.TYPE_BROWSER_SHORTCUT){
            parent?.context?.let { Glide.with(it).load(data.filePath).apply(RequestOptions.bitmapTransform( RoundedCorners(8f.dp2px.toInt()
            ))).placeholder(R.mipmap.icon_type_image).into(childHolder.imgFile) }
        }else{
            parent?.context?.let { Glide.with(it).load(MediaStorageTool.getTypeImageIcon(data.type))
                .placeholder(R.mipmap.icon_type_unkown).into(childHolder.imgFile) }
        }

        if (childPosition == getChildrenCount(groupPosition) - 1) {
            childHolder.llBack.setBackgroundResource(R.drawable.shape_bg_item_bottom)
        }else{
            childHolder.llBack.setBackgroundResource(R.color.white)
        }

        childHolder.llBack.setOnClickListener {
            childHolder.ckItem.isChecked = !data.isSelect
        }
        childHolder.ckItem.apply {
            setOnCheckedChangeListener(null)
            isChecked = data.isSelect
            setOnCheckedChangeListener { _: CompoundButton?, b: Boolean ->
                data.isSelect = b
                if (b){
                    if (!selectItemList.contains(data)) selectItemList.add(data)
                    if (groupList[groupPosition].fileList.all{it.isSelect}){
                        groupList[groupPosition].isSelect = true
                        notifyDataSetChanged()
                    }
                }else{
                    selectItemList.remove(data)
                    if (groupList[groupPosition].isSelect){
                        groupList[groupPosition].isSelect = false
                        notifyDataSetChanged()
                    }
                }
                click(getSelectItems())
            }
        }

        return mView!!
    }

    override fun isChildSelectable(groupPosition: Int, childPosition: Int): Boolean {
        return false
    }

    private class GroupViewHolder(view: View) {
        val llBack = view.findViewById<RelativeLayout>(R.id.ll_back)
        val tvTitle = view.findViewById<TextView>(R.id.tv_title)
        val imgArrow = view.findViewById<ImageView>(R.id.img_arrow)
        val allSize = view.findViewById<TextView>(R.id.tv_size)
        val ckAll = view.findViewById<CheckBox>(R.id.ck_all)
    }

    private class ChildViewHolder(view: View) {
        val llBack = view.findViewById<RelativeLayout>(R.id.ll_back)
        val tvTitle = view.findViewById<TextView>(R.id.tv_title)
        val imgFile = view.findViewById<ImageView>(R.id.img_type)
        val tvSize = view.findViewById<TextView>(R.id.tv_size)
        val ckItem = view.findViewById<CheckBox>(R.id.ck_item)
    }

    private fun selectGroupAllItem(isClick: Boolean,position: Int){
        groupList[position].fileList.forEach {
            it.isSelect = isClick
            if (isClick){
                if (!selectItemList.contains(it)) selectItemList.add(it)
            }else{
                selectItemList.remove(it)
            }
        }
    }

    fun setData(list: MutableList<ReItemGroupData>){
        groupList.clear()
        groupList.addAll(list)
        notifyDataSetChanged()
    }

    fun getSelectItems(): MutableList<ItemFileData>{
        return selectItemList
    }


}