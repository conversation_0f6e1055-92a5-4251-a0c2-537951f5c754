package com.example.newdc.dataadapter

import android.annotation.SuppressLint
import android.content.Context
import android.provider.ContactsContract.Data
import android.text.format.Formatter
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.*
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.example.newdc.R
import com.example.newdc.content.activity.ApplicationInfoActivity
import com.example.newdc.modelutil.data.MoreFuncData
import com.example.newdc.modelutil.data.MoreInfoData
import com.example.newdc.modelutil.dev.ApkInfoData
import com.example.newdc.modelutil.dev.AppsTool
import com.example.newdc.modelutil.tool.FormatTool
import com.example.newdc.modelutil.tool.MediaStorageTool
import java.util.*

class ApkInfoAdapter(private val clickCall: (pkg: String) -> Unit = {}): RecyclerView.Adapter<ApkInfoAdapter.ItemViewHolder>() {
    private val apkList = mutableListOf<ApkInfoData>()
    private val filterList = mutableListOf<ApkInfoData>()
    private var mContext: Context? = null

    inner class ItemViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView){
        val tvTitle = itemView.findViewById<TextView>(R.id.tv_title)
        val tvSize = itemView.findViewById<TextView>(R.id.tv_size)
        val imgMore = itemView.findViewById<ImageView>(R.id.img_more)
        val imgApk = itemView.findViewById<ImageView>(R.id.img_apk)
    }

    override fun onCreateViewHolder(p0: ViewGroup, p1: Int): ItemViewHolder {
        mContext = p0.context
        return ItemViewHolder(LayoutInflater.from(p0.context).inflate(R.layout.item_brow_apk, p0, false))
    }

    override fun getItemCount(): Int {
        return filterList.size
    }

    @SuppressLint("SetTextI18n")
    override fun onBindViewHolder(p0: ItemViewHolder, p1: Int) {
        val apkData = filterList[p1]
        p0.apply {
            tvTitle.text = apkData.apkName
            tvSize.text = "${FormatTool.certDateFormat(Date(apkData.date))} | ${Formatter.formatFileSize(mContext,apkData.size)}"
            try {
                val icon = AppsTool.getApkIconDrawable(mContext!!,apkData.pkgName)
                Glide.with(mContext!!).load(icon).placeholder(R.mipmap.icon_type_apk).into(imgApk)
            } catch (e: Exception) {
                e.printStackTrace()
            }

            imgMore.setOnClickListener {
                apkData.pkgName?.let { it1 -> clickCall(it1) }
            }

            itemView.setOnClickListener {
                ApplicationInfoActivity.startApplicationInfoActivity(mContext!!,apkData.pkgName!!)
            }

        }
    }

    fun getList(): MutableList<ApkInfoData>{
        return apkList
    }

    fun filterFileList(filter: Int) {
        val tempList = when (filter) {
            1 -> {
                apkList.filter { !it.isSys }
            }
            2 -> {
                apkList.filter { it.isSys }
            }
            else -> {
                apkList
            }
        }
        filterList.clear()
        filterList.addAll(tempList)
        notifyDataSetChanged()
    }

    fun setApkData(list: MutableList<ApkInfoData>){
        apkList.clear()
        filterList.clear()
        apkList.addAll(list)
        filterList.addAll(apkList)
        notifyDataSetChanged()
    }

}