package com.example.newdc.dataadapter

import android.text.format.Formatter
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.CheckBox
import android.widget.ImageView
import android.widget.TextView
import com.bumptech.glide.Glide
import com.example.newdc.R
import com.example.newdc.modelutil.tool.MediaStorageTool

class FileItemAdapter: BaseFileBrowserAdapter() {

    inner class ItemViewHolder(itemView: View) : BaseFileBrowserAdapter.BaseItemViewHolder(itemView){
        val tvSize = itemView.findViewById<TextView>(R.id.tv_size)
    }

    override fun onCreateViewHolder(p0: ViewGroup, p1: Int): ItemViewHolder {
        mParentContext = p0.context
        return ItemViewHolder(LayoutInflater.from(p0.context).inflate(R.layout.item_brow_files, p0, false))
    }

    override fun onBindViewHolder(p0: BaseFileBrowserAdapter.BaseItemViewHolder, p1: Int) {
        super.onBindViewHolder(p0, p1)
        val fileItem = filterList[p1]
        if (p0 is ItemViewHolder) {
            p0.apply {
                tvTitle.text = fileItem.title
                Glide.with(mParentContext!!).load(MediaStorageTool.getTypeImageIcon(fileItem.type)).into(imgType)
                if (fileItem.type == MediaStorageTool.TYPE_BROWSER_DIR){
                    tvSize.text = String.format(mParentContext!!.getString(R.string.base_items),fileItem.fileSize)
                    if (barOpen) imgFolder.visibility = View.GONE else imgFolder.visibility = View.VISIBLE
                }else{
                    tvSize.text = Formatter.formatFileSize(mParentContext!!,fileItem.fileSize)
                    imgFolder.visibility = View.GONE
                }
            }
        }
    }

    override fun filterFileList(filter: String) {
        checkAllFilterItem()
        filterType = filter
        val tempList = when (filter) {
            "" -> {
                itemList
            }
            MediaStorageTool.TYPE_BROWSER_OTHER -> {
                val ignoreList = mutableListOf(MediaStorageTool.TYPE_BROWSER_VIDEO, MediaStorageTool.TYPE_BROWSER_AUDIO, MediaStorageTool.TYPE_BROWSER_IMAGE)
                itemList.filterNot { it.type in ignoreList }
            }
            else -> {
                itemList.filter { it.type == filter }
            }
        }
        filterList.clear()
        filterList.addAll(tempList)
        notifyDataSetChanged()
    }


}