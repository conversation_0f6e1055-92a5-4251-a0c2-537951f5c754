package com.example.newdc.dataadapter

import android.text.format.Formatter
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import com.bumptech.glide.Glide
import com.example.newdc.R
import com.example.newdc.modelutil.tool.MediaStorageTool

class DuplicateAdapter: BaseFileBrowserAdapter() {
    companion object{
        const val DUP_TYPE_FILE = 1
        const val DUP_TYPE_TITLE = 2
    }


    override fun filterFileList(filter: String) {
    }

    inner class ItemViewHolder(itemView: View) : BaseFileBrowserAdapter.BaseItemViewHolder(itemView){
        val tvSize = itemView.findViewById<TextView>(R.id.tv_size)
    }

    inner class TitleViewHolder(itemView: View) : BaseFileBrowserAdapter.BaseItemViewHolder(itemView){
        val tvGroup = itemView.findViewById<TextView>(R.id.tv_group)
        val ckAll = itemView.findViewById<TextView>(R.id.ck_all)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int
    ): BaseFileBrowserAdapter.BaseItemViewHolder {
        mParentContext = parent.context
        val itemView = if (viewType != DUP_TYPE_TITLE){
            LayoutInflater.from(parent.context).inflate(R.layout.item_brow_files,parent,false)
        }else{
            LayoutInflater.from(parent.context).inflate(R.layout.item_dup_group,parent,false)
        }
        return if (viewType == DUP_TYPE_TITLE) TitleViewHolder(itemView) else ItemViewHolder(itemView)
    }

    override fun onBindViewHolder(p0: BaseItemViewHolder, p1: Int) {
        val item = filterList[p1]
        if (p0 is ItemViewHolder) {
            super.onBindViewHolder(p0, p1)
            p0.apply {
                Glide.with(mParentContext!!).load(item.filePath).into(imgType)
                tvSize.text = Formatter.formatFileSize(mParentContext,item.fileSize)
                tvTitle.text = item.title
            }
        }else if (p0 is TitleViewHolder) {
            p0.apply {
                p0.itemView.isClickable = false
                tvGroup.text = item.title
            }
        }
    }


    override fun getItemViewType(position: Int): Int {
        return if (itemList[position].type == MediaStorageTool.TYPE_BROWSER_TITLE) {
            DUP_TYPE_TITLE
        }else{
            DUP_TYPE_FILE
        }
    }
}