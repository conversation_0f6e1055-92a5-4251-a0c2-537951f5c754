package com.example.newdc.dataadapter

import android.content.Context
import android.view.View
import android.widget.CheckBox
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.example.newdc.R
import com.example.newdc.modelutil.data.ItemFileData

abstract class BaseFileBrowserAdapter: RecyclerView.Adapter<BaseFileBrowserAdapter.BaseItemViewHolder>() {
    protected var mParentContext: Context? = null
    protected val itemList = mutableListOf<ItemFileData>()
    protected val filterList = mutableListOf<ItemFileData>()
    protected val selectItemList = mutableListOf<ItemFileData>()
    protected var filterType = ""
    var itemClickListener: BaseItemClickListener? = null
    var barOpen = false

    abstract fun filterFileList(filter: String)


    override fun getItemCount(): Int {
        return filterList.size
    }

    override fun onBindViewHolder(p0: BaseItemViewHolder, p1: Int) {
        val item = filterList[p1]
        p0.apply {
            if (barOpen){
                ckSelect.visibility = View.VISIBLE
            }else{
                ckSelect.visibility = View.GONE
            }

            ckSelect.apply {
                setOnCheckedChangeListener(null)
                isChecked = item.isSelect
            }

            itemView.setOnClickListener {
                if (barOpen){
                    item.isSelect = !item.isSelect
                    ckSelect.isChecked = item.isSelect
                }else{
                    item.isSelect = !item.isSelect
                }
                itemClickListener?.onItemClick(item)
            }

            ckSelect.setOnCheckedChangeListener { buttonView, isChecked ->
                item.isSelect = isChecked
                if (isChecked){
                    if (!selectItemList.contains(item)){
                        selectItemList.add(item)
                    }
                }else{
                    selectItemList.remove(item)
                }
            }

            itemView.setOnLongClickListener {
                if (!barOpen){
                    barOpen = !barOpen
                    item.isSelect = barOpen
                    ckSelect.isChecked = item.isSelect
                    itemClickListener?.onItemLongClick(barOpen)
                    notifyDataSetChanged()
                }
                true
            }
        }
    }

    fun closeControlBar(){
        barOpen = false
        checkAllItem()
        notifyDataSetChanged()
    }

    fun checkAllItem(select: Boolean = false){
        itemList.forEach {
            it.isSelect = select
        }
        if (select){
            selectItemList.addAll(itemList)
        }else{
            selectItemList.clear()
        }
    }

    fun checkAllFilterItem(select: Boolean = false){
        filterList.forEach {
            it.isSelect = select
        }
        if (select){
            selectItemList.addAll(filterList)
        }else{
            selectItemList.clear()
        }
        notifyDataSetChanged()
    }

    fun getAllSelectItems(): MutableList<ItemFileData>{
        return selectItemList
    }

    fun getFilterItems(): MutableList<ItemFileData>{
        return filterList
    }

    abstract inner class BaseItemViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView){
        val tvTitle = itemView.findViewById<TextView>(R.id.tv_title)
        val imgType = itemView.findViewById<ImageView>(R.id.img_file)
        val ckSelect = itemView.findViewById<CheckBox>(R.id.ck_item)
        val imgFolder = itemView.findViewById<ImageView>(R.id.img_folder)
    }

    interface BaseItemClickListener{
        fun onItemClick(item: ItemFileData)
        fun onItemLongClick(barOpen: Boolean)
    }

    fun setItemListener(itemClickListener: BaseItemClickListener){
        this.itemClickListener = itemClickListener
    }

    fun setData(data: MutableList<ItemFileData>){
        itemList.clear()
        filterList.clear()
        itemList.addAll(data)
        filterList.addAll(data)
        notifyDataSetChanged()
    }
}