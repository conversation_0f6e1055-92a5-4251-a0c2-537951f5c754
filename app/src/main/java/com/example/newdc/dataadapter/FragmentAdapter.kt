package com.example.newdc.dataadapter

import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.Lifecycle
import androidx.viewpager2.adapter.FragmentStateAdapter

class FragmentAdapter( pages: MutableList<Fragment>,frgManager: FragmentManager, lifecycle: Lifecycle):
    FragmentStateAdapter(frgManager,lifecycle){

    private var pageList:MutableList<Fragment> = pages

    override fun getItemCount(): Int {
        return pageList.size
    }

    override fun createFragment(p0: Int): Fragment {
        return pageList[p0]
    }
}