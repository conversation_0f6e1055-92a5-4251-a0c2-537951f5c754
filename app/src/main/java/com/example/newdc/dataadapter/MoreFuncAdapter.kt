package com.example.newdc.dataadapter

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.*
import androidx.recyclerview.widget.RecyclerView
import com.example.newdc.R
import com.example.newdc.modelutil.data.MoreFuncData
import com.example.newdc.modelutil.data.MoreInfoData

class MoreFuncAdapter(list: MutableList<MoreFuncData>, val onCall: (type: Int) -> Unit = {}): RecyclerView.Adapter<MoreFuncAdapter.ItemViewHolder>() {
    private val infoList = list
    private var mContext: Context? = null

    inner class ItemViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView){
        val tvTitle = itemView.findViewById<TextView>(R.id.tv_title)
        val tvDes = itemView.findViewById<TextView>(R.id.tv_des)
        val imgFunc = itemView.findViewById<ImageView>(R.id.img_func)
        val btnCheck = itemView.findViewById<Button>(R.id.btn_check)
        val llBack = itemView.findViewById<RelativeLayout>(R.id.ll_func)
    }

    override fun onCreateViewHolder(p0: ViewGroup, p1: Int): ItemViewHolder {
        mContext = p0.context
        return ItemViewHolder(LayoutInflater.from(p0.context).inflate(R.layout.item_more_tool, p0, false))
    }

    override fun getItemCount(): Int {
        return infoList.size
    }

    override fun onBindViewHolder(p0: ItemViewHolder, p1: Int) {
        val funcData = infoList[p1]
        p0.apply {
            tvTitle.text = mContext?.getString(funcData.title)
            imgFunc.setImageResource(funcData.icon)
            tvDes.text = mContext?.getString(funcData.description)
            btnCheck.setOnClickListener {onCall(funcData.type)}

            if (p1 % 2 == 0){llBack.setBackgroundResource(R.drawable.shape_bg_item_left)} else llBack.setBackgroundResource(R.drawable.shape_bg_item_right)

        }
    }
}