package com.example.newdc.dataadapter

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.example.newdc.R
import com.example.newdc.modelutil.data.MoreInfoData

class RateItemAdapter(): RecyclerView.Adapter<RateItemAdapter.ItemViewHolder>() {
    private var parentContext: Context? = null
    private val infoList: MutableList<String> = mutableListOf()

    inner class ItemViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView){
        val tvCount = itemView.findViewById<TextView>(R.id.tv_count)
        val tvRate = itemView.findViewById<TextView>(R.id.tv_rate)
    }

    override fun onCreateViewHolder(p0: ViewGroup, p1: Int): ItemViewHolder {
        parentContext = p0.context
        return ItemViewHolder(LayoutInflater.from(p0.context).inflate(R.layout.item_cpu, p0, false))
    }

    override fun getItemCount(): Int {
        return infoList.size
    }

    override fun onBindViewHolder(p0: ItemViewHolder, p1: Int) {
        val infoData = infoList[p1]
        p0.tvCount.text = parentContext?.getString(R.string.cpu_core) + "${p1 + 1}"
        p0.tvRate.text = infoData
    }

    fun setData(list: MutableList<String>){
        infoList.clear()
        infoList.addAll(list)
        notifyDataSetChanged()
    }
}