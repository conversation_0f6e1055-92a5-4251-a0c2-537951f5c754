package com.example.newdc.dataadapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.GridLayoutManager.SpanSizeLookup
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.example.newdc.R
import com.example.newdc.modelutil.tool.FormatTool
import com.example.newdc.modelutil.tool.MediaStorageTool

class MediaFileAdapter: BaseFileBrowserAdapter() {
    companion object{
        const val DUP_TYPE_FILE = 1
        const val DUP_TYPE_TITLE = 2
    }


    override fun filterFileList(filter: String) {
    }

    inner class ItemViewHolder(itemView: View) : BaseFileBrowserAdapter.BaseItemViewHolder(itemView){
        val tvDuration = itemView.findViewById<TextView>(R.id.tv_duration)
    }

    inner class TitleViewHolder(itemView: View) : BaseFileBrowserAdapter.BaseItemViewHolder(itemView){
        val tvGroup = itemView.findViewById<TextView>(R.id.tv_group)
        val ckAll = itemView.findViewById<TextView>(R.id.ck_all)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int
    ): BaseFileBrowserAdapter.BaseItemViewHolder {
        mParentContext = parent.context
        val itemView = if (viewType != DUP_TYPE_TITLE){
            LayoutInflater.from(parent.context).inflate(R.layout.item_brow_media,parent,false)
        }else{
            LayoutInflater.from(parent.context).inflate(R.layout.item_dup_group,parent,false)
        }
        return if (viewType == DUP_TYPE_TITLE) TitleViewHolder(itemView) else ItemViewHolder(itemView)
    }

    override fun onBindViewHolder(p0: BaseItemViewHolder, p1: Int) {
        val item = filterList[p1]
        if (p0 is ItemViewHolder) {
            super.onBindViewHolder(p0, p1)
            p0.apply {
                Glide.with(mParentContext!!).load(item.filePath).into(imgType)
                if (item.type == MediaStorageTool.TYPE_BROWSER_VIDEO){
                    tvDuration.text = FormatTool.formatFileMin(item.duration)
                    tvDuration.visibility = View.VISIBLE
                }
            }
        }else if (p0 is TitleViewHolder) {
            p0.apply {
                tvGroup.text = item.title
            }
        }
    }

    override fun onAttachedToRecyclerView(recyclerView: RecyclerView) {
        super.onAttachedToRecyclerView(recyclerView)
        val manager = recyclerView.layoutManager
        if (manager is GridLayoutManager) {
            manager.spanSizeLookup = object : SpanSizeLookup() {
                override fun getSpanSize(position: Int): Int {
                    return when (getItemViewType(position)) {
                        DUP_TYPE_TITLE -> 3
                        DUP_TYPE_FILE -> 1
                        else -> 1
                    }
                }
            }
        }
    }


    override fun getItemViewType(position: Int): Int {
        return if (itemList[position].type == MediaStorageTool.TYPE_BROWSER_TITLE) {
            DUP_TYPE_TITLE
        }else{
            DUP_TYPE_FILE
        }
    }
}