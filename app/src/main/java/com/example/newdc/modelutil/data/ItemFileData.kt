package com.example.newdc.modelutil.data

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
data class ItemFileData(
    var title: String,
    var fileSize: Long,
    var filePath: String,
    var modifyDate: Long,
    var type: String,
    var typeIcon: Int,
    var mime: String? = "",
    var duration: Long = 0,
    var isDir: Boolean = false,
    var isSelect: Boolean = false
): Parcelable