package com.example.newdc.modelutil.data

import android.content.Context
import android.os.Parcelable
import com.example.newdc.R
import kotlinx.parcelize.Parcelize

@Parcelize
data class TestData(
    val testName: Int,
    val testDescribe: Int,
    val testIntent: String
):Parcelable{
    companion object{
        fun getTestList(): MutableList<TestData>{
            return  mutableListOf<TestData>().apply {
                add(TestData(R.string.test_screen_color,R.string.test_tip_color,StoreKeyData.TEST_VALUE_COLOR))
                add(TestData(R.string.test_screen_touch,R.string.test_tip_touch,StoreKeyData.TEST_VALUE_TOUCH))
                add(TestData(R.string.test_flashlight,R.string.test_tip_flashlight,StoreKeyData.TEST_VALUE_FLASHLIGHT))
                add(TestData(R.string.test_ear_speaker,R.string.test_tip_ear_speaker,StoreKeyData.TEST_VALUE_SPEAKER))
                add(TestData(R.string.test_ear_proximity,R.string.test_tip_proximity,StoreKeyData.TEST_VALUE_PROXIMITY))
                add(TestData(R.string.test_loud_speaker,R.string.test_tip_loud,StoreKeyData.TEST_VALUE_LOUD))
                add(TestData(R.string.test_light_sensor,R.string.test_tip_light_sensor,StoreKeyData.TEST_VALUE_LIGHT))
                add(TestData(R.string.test_vibration,R.string.test_tip_vibration,StoreKeyData.TEST_VALUE_VIBRATION))
                add(TestData(R.string.test_volume_up_button,R.string.test_tip_volume_up,StoreKeyData.TEST_VALUE_VOLUME_UP))
                add(TestData(R.string.test_volume_down_button,R.string.test_tip_volume_down,StoreKeyData.TEST_VALUE_VOLUME_DOWN))
            }
        }
    }

}
