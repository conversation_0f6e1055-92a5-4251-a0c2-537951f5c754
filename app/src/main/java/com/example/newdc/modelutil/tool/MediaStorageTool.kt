package com.example.newdc.modelutil.tool

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.database.Cursor
import android.media.MediaScannerConnection
import android.net.Uri
import android.os.Environment
import android.provider.MediaStore
import android.util.Log
import android.widget.Toast
import com.example.newdc.BaseApplication
import com.example.newdc.R
import com.example.newdc.modelutil.data.ItemFileData
import com.example.newdc.modelutil.tool.FilesTool.findFileUri
import com.example.newdc.modelutil.tool.FilesTool.findFilesSize
import com.example.newdc.modelutil.tool.FilesTool.getFileMime
import java.io.File

object MediaStorageTool {
    const val TYPE_BROWSER_IMAGE = "type_browser_image"
    const val TYPE_BROWSER_AUDIO = "type_browser_audio"
    const val TYPE_BROWSER_VIDEO = "type_browser_video"
    const val TYPE_BROWSER_APK = "type_browser_apk"
    const val TYPE_BROWSER_ZIP = "type_browser_zip"
    const val TYPE_BROWSER_DOC_WORD = "type_browser_doc_word"
    const val TYPE_BROWSER_DOC_PDF = "type_browser_doc_pdf"
    const val TYPE_BROWSER_DOC_EXCEL = "type_browser_doc_excel"
    const val TYPE_BROWSER_DOC_TXT = "type_browser_doc_txt"
    const val TYPE_BROWSER_DOC_PPT = "type_browser_doc_ppt"
    const val TYPE_BROWSER_SHORTCUT = "type_browser_shortcut"
    const val TYPE_BROWSER_OTHER = "type_browser_other"
    const val TYPE_BROWSER_DIR = "type_browser_dir"
    const val TYPE_BROWSER_TITLE = "type_browser_title"
    const val TYPE_BROWSER_TEMP = "type_browser_temp"
    const val TYPE_BROWSER_LOG = "type_browser_log"


    fun updateMediaStorage(context: Context){
        context.sendBroadcast(
            Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE, Uri.parse(" file:// " + Environment.getExternalStorageDirectory().absolutePath))
        )
    }

    fun updateFilePath(path: String) {
        MediaScannerConnection.scanFile(BaseApplication.mAppContext, arrayOf(path), null) { s: String?, uri: Uri? -> }
    }

    fun findCurrentPathItem(path: String?): MutableList<ItemFileData> {
        val list = mutableListOf<ItemFileData>()
        if (path != null){
            try {
                val pathFile = File(path)
                if (pathFile.exists()) {
                    if (pathFile.isDirectory) {
                        val files = pathFile.listFiles()
                        if (files != null && files.isNotEmpty()) {
                            for (file in files) {
                                if (file.isDirectory) {
                                    var length = 0
                                    if (file.listFiles() != null) {
                                        length = (file.listFiles())?.size ?: 0
                                    }
                                    val item = ItemFileData(
                                        title = file.name, fileSize = length.toLong(), filePath =  file.absolutePath, modifyDate = file.lastModified(),
                                        type = TYPE_BROWSER_DIR, typeIcon = R.mipmap.icon_type_folder, isDir = true)
                                    list.add(item)
                                } else {
                                    val size = findFilesSize(file)
                                    val mime = getFileMime(BaseApplication.mAppContext, file)
                                    val type = findTypeFromMime(mime)
                                    val item = ItemFileData(
                                        title = file.name, fileSize = size, filePath =  file.absolutePath, modifyDate = file.lastModified(),
                                        type = type, typeIcon = 0, mime = mime)
                                    list.add(item)
                                }
                            }
                        }
                    }else {
                        val size = findFilesSize(pathFile)
                        val mime = getFileMime(BaseApplication.mAppContext, pathFile)
                        val type = findTypeFromMime(mime)
                        val item = ItemFileData(
                            title = pathFile.name, fileSize = size, filePath =  pathFile.absolutePath, modifyDate = pathFile.lastModified(),
                            type = type, typeIcon = 0, mime = mime)
                        list.add(item)
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
        return list
    }

    fun findLargeFile(context: Context,fileMax: Long): MutableList<ItemFileData>{
        var cursor: Cursor? = null
        val list = mutableListOf<ItemFileData>()
        try {
            cursor = context.contentResolver.query(
                MediaStore.Files.getContentUri("external"),
                null,
                null,
                null,
                null
            )
            if (cursor != null) {
                while (cursor.moveToNext()) {
                    val name = cursor.getString(cursor.getColumnIndexOrThrow(MediaStore.Files.FileColumns.DISPLAY_NAME))
                    val mime = cursor.getString(cursor.getColumnIndexOrThrow(MediaStore.Files.FileColumns.MIME_TYPE))
                    val size = cursor.getLong(cursor.getColumnIndexOrThrow(MediaStore.Files.FileColumns.SIZE))
                    val path = cursor.getString(cursor.getColumnIndexOrThrow(MediaStore.Files.FileColumns.DATA))
                    val modified = cursor.getLong(cursor.getColumnIndexOrThrow(MediaStore.Files.FileColumns.DATE_MODIFIED))
                    val type = findTypeFromMime(mime)
                    val item = ItemFileData(
                        title = name, fileSize = size, filePath =  path, modifyDate = modified,
                        type = type, typeIcon = 0)
                    if (size >= fileMax){
                        list.add(item)
                    }
                }
            }
        } catch (e: IllegalArgumentException) {
            e.printStackTrace()
        } finally {
            assert(cursor != null)
            cursor!!.close()
        }
        return list
    }

    @SuppressLint("Recycle")
    fun findImageFile(context: Context): MutableList<ItemFileData> {
        var cursor: Cursor? = null
        val list = mutableListOf<ItemFileData>()
        try {
            cursor = context.contentResolver.query(
                MediaStore.Images.Media.EXTERNAL_CONTENT_URI, null, null, null, null)
            if (cursor != null) {
                while (cursor.moveToNext()) {
                    val name = cursor.getString(cursor.getColumnIndexOrThrow(MediaStore.Images.Media.DISPLAY_NAME))
                    val mime = cursor.getString(cursor.getColumnIndexOrThrow(MediaStore.Images.Media.MIME_TYPE))
                    val size = cursor.getLong(cursor.getColumnIndexOrThrow(MediaStore.Images.Media.SIZE))
                    val path = cursor.getString(cursor.getColumnIndexOrThrow(MediaStore.Images.Media.DATA))
                    val modified = cursor.getLong(cursor.getColumnIndexOrThrow(MediaStore.Images.Media.DATE_MODIFIED))
                    val type = findTypeFromMime(mime)
                    val item = ItemFileData(
                        title = name, fileSize = size, filePath =  path, modifyDate = modified,
                        type = type, typeIcon = 0,mime)
                    list.add(item)
                }
            }
        } catch (e: IllegalArgumentException) {
            e.printStackTrace()
        } finally {
            assert(cursor != null)
            cursor!!.close()
        }
        return list
    }

    @SuppressLint("Recycle")
    fun findVideoFile(context: Context): MutableList<ItemFileData> {
        var cursor: Cursor? = null
        val list = mutableListOf<ItemFileData>()
        try {
            cursor = context.contentResolver.query(
                MediaStore.Video.Media.EXTERNAL_CONTENT_URI, null, null, null, null)
            if (cursor != null) {
                while (cursor.moveToNext()) {
                    val name = cursor.getString(cursor.getColumnIndexOrThrow(MediaStore.Video.Media.DISPLAY_NAME))
                    val mime = cursor.getString(cursor.getColumnIndexOrThrow(MediaStore.Video.Media.MIME_TYPE))
                    val size = cursor.getLong(cursor.getColumnIndexOrThrow(MediaStore.Video.Media.SIZE))
                    val path = cursor.getString(cursor.getColumnIndexOrThrow(MediaStore.Video.Media.DATA))
                    val modified = cursor.getLong(cursor.getColumnIndexOrThrow(MediaStore.Video.Media.DATE_MODIFIED))
                    val duration = cursor.getLong(cursor.getColumnIndexOrThrow(MediaStore.Video.Media.DURATION))
                    val type = findTypeFromMime(mime)
                    val item = ItemFileData(
                        title = name, fileSize = size, filePath =  path, modifyDate = modified,
                        type = type, typeIcon = 0,mime = mime,duration = duration)
                    list.add(item)
                }
            }
        } catch (e: IllegalArgumentException) {
            e.printStackTrace()
        } finally {
            assert(cursor != null)
            cursor!!.close()
        }
        return list
    }

    @SuppressLint("Recycle")
    fun findAudioFile(context: Context): MutableList<ItemFileData> {
        var cursor: Cursor? = null
        val list = mutableListOf<ItemFileData>()
        try {
            cursor = context.contentResolver.query(
                MediaStore.Audio.Media.EXTERNAL_CONTENT_URI,
                null,
                null,
                null,
                null
            )
            if (cursor != null) {
                while (cursor.moveToNext()) {
                    val name = cursor.getString(cursor.getColumnIndexOrThrow(MediaStore.Audio.Media.DISPLAY_NAME))
                    val mime = cursor.getString(cursor.getColumnIndexOrThrow(MediaStore.Audio.Media.MIME_TYPE))
                    val size = cursor.getLong(cursor.getColumnIndexOrThrow(MediaStore.Audio.Media.SIZE))
                    val path = cursor.getString(cursor.getColumnIndexOrThrow(MediaStore.Audio.Media.DATA))
                    val modified = cursor.getLong(cursor.getColumnIndexOrThrow(MediaStore.Audio.Media.DATE_MODIFIED))
                    val duration = cursor.getLong(cursor.getColumnIndexOrThrow(MediaStore.Audio.Media.DURATION))
                    val type = findTypeFromMime(mime)
                    val item = ItemFileData(
                        title = name, fileSize = size, filePath =  path, modifyDate = modified,
                        type = type, typeIcon = 0,mime = mime,duration = duration)
                    list.add(item)
                }
            }
        } catch (e: IllegalArgumentException) {
            e.printStackTrace()
        } finally {
            assert(cursor != null)
            cursor!!.close()
        }
        return list
    }

    @SuppressLint("Recycle")
    fun findDocFile(context: Context): MutableList<ItemFileData> {
        var cursor: Cursor? = null
        val list = mutableListOf<ItemFileData>()
        try {
            val SELECT_LIST_TYPE = "(_data LIKE '%.doc' or _data LIKE '%.docx') or " +
                    "_data LIKE '%.txt' or _data LIKE '%.pdf' or _data LIKE '%.xlsx' or _data LIKE '%.xls'"
            cursor = context.contentResolver.query(
                MediaStore.Files.getContentUri("external"),
                null,
                SELECT_LIST_TYPE,
                null,
                null
            )
            if (cursor != null) {
                while (cursor.moveToNext()) {
                    val name = cursor.getString(cursor.getColumnIndexOrThrow(MediaStore.Audio.Media.DISPLAY_NAME))
                    val mime = cursor.getString(cursor.getColumnIndexOrThrow(MediaStore.Audio.Media.MIME_TYPE))
                    val size = cursor.getLong(cursor.getColumnIndexOrThrow(MediaStore.Audio.Media.SIZE))
                    val path = cursor.getString(cursor.getColumnIndexOrThrow(MediaStore.Audio.Media.DATA))
                    val modified = cursor.getLong(cursor.getColumnIndexOrThrow(MediaStore.Audio.Media.DATE_MODIFIED))
                    val type = findTypeFromMime(mime)
                    val item = ItemFileData(
                        title = name, fileSize = size, filePath =  path, modifyDate = modified,
                        type = type, typeIcon = 0,mime = mime)
                    list.add(item)
                }
            }
        } catch (e: IllegalArgumentException) {
            e.printStackTrace()
        } finally {
            assert(cursor != null)
            cursor!!.close()
        }
        return list
    }

    @SuppressLint("Recycle")
    fun findZipFile(context: Context): MutableList<ItemFileData> {
        var cursor: Cursor? = null
        val list = mutableListOf<ItemFileData>()
        try {
            val SELECT_LIST_TYPE = "(_data LIKE '%.zip')"
            cursor = context.contentResolver.query(
                MediaStore.Files.getContentUri("external"),
                null,
                null,
                null,
                null
            )
            if (cursor != null) {
                while (cursor.moveToNext()) {
                    val name = cursor.getString(cursor.getColumnIndexOrThrow(MediaStore.Audio.Media.DISPLAY_NAME))
                    val mime = cursor.getString(cursor.getColumnIndexOrThrow(MediaStore.Audio.Media.MIME_TYPE))
                    val size = cursor.getLong(cursor.getColumnIndexOrThrow(MediaStore.Audio.Media.SIZE))
                    val path = cursor.getString(cursor.getColumnIndexOrThrow(MediaStore.Audio.Media.DATA))
                    val modified = cursor.getLong(cursor.getColumnIndexOrThrow(MediaStore.Audio.Media.DATE_MODIFIED))
                    val type = findTypeFromMime(mime)
                    val item = ItemFileData(
                        title = name, fileSize = size, filePath =  path, modifyDate = modified,
                        type = type, typeIcon = 0,mime = mime)
                    list.add(item)
                }
            }
        } catch (e: java.lang.IllegalArgumentException) {
            throw java.lang.RuntimeException(e)
        } finally {
            assert(cursor != null)
            cursor!!.close()
        }
        return list
    }

    fun findTypeFromMime(mime: String?): String {
        if (mime == null) return TYPE_BROWSER_OTHER
        val type = if (mime.contains("image/")) {
            TYPE_BROWSER_IMAGE
        } else if (mime.contains("audio/")) {
            TYPE_BROWSER_AUDIO
        } else if (mime.contains("video/")) {
            TYPE_BROWSER_VIDEO
        } else if (mime == "application/vnd.android.package-archive") {
            TYPE_BROWSER_APK
        } else if (mime == "application/zip" || mime == "application/x-zip-compressed") {
            TYPE_BROWSER_ZIP
        } else {
            getOtherType(mime)
        }
        return type
    }

    private fun getOtherType(mime: String?): String {
        return when (mime) {
            "text/plain" -> {
                TYPE_BROWSER_DOC_TXT
            }
            "application/pdf" -> {
                TYPE_BROWSER_DOC_PDF
            }
            "application/msword", "application/vnd.openxmlformats-officedocument.wordprocessingml.document" -> {
                TYPE_BROWSER_DOC_WORD
            }
            "application/vnd.ms-excel", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" -> {
                TYPE_BROWSER_DOC_EXCEL
            }
            "application/vnd.ms-powerpoint","application/vnd.openxmlformats-officedocument.presentationml.presentation",
            "application/vnd.oasis.opendocument.presentation"->{
                TYPE_BROWSER_DOC_PPT
            }
            else -> {
                TYPE_BROWSER_OTHER}
        }
    }

    fun getTypeImageIcon(type: String): Int{
        return when (type) {
            TYPE_BROWSER_DIR -> {R.mipmap.icon_type_folder}
            TYPE_BROWSER_IMAGE -> {R.mipmap.icon_type_image}
            TYPE_BROWSER_AUDIO -> {R.mipmap.icon_type_audio}
            TYPE_BROWSER_VIDEO -> {R.mipmap.icon_type_mp4}
            TYPE_BROWSER_APK -> {R.mipmap.icon_type_apk}
            TYPE_BROWSER_ZIP -> {R.mipmap.icon_type_zip}
            TYPE_BROWSER_DOC_WORD -> {R.mipmap.icon_type_word}
            TYPE_BROWSER_DOC_PDF -> {R.mipmap.icon_type_pdf}
            TYPE_BROWSER_DOC_EXCEL -> {R.mipmap.icon_type_excel}
            TYPE_BROWSER_DOC_TXT -> {R.mipmap.icon_type_txt}
            TYPE_BROWSER_DOC_PPT -> {R.mipmap.icon_type_ppt}
            TYPE_BROWSER_SHORTCUT -> {R.mipmap.icon_type_image}
            TYPE_BROWSER_LOG, TYPE_BROWSER_TEMP -> {R.mipmap.icon_type_log}
            else ->{R.mipmap.icon_type_unkown}
        }
    }

    fun fileOpen(context: Context, path: String,mime: String?, type: String){
        try {
            val file = File(path)
            if (!file.exists()) return
            val uri = findFileUri(context, file)
            val intent = Intent(Intent.ACTION_VIEW)
            intent.addCategory(Intent.CATEGORY_DEFAULT)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)

            val dataType = when (type) {
                TYPE_BROWSER_IMAGE,TYPE_BROWSER_SHORTCUT -> { "image/*"
                }
                TYPE_BROWSER_VIDEO -> {
                    "video/*"
                }
                TYPE_BROWSER_AUDIO -> {
                    "audio/*"
                }
                TYPE_BROWSER_APK -> {
                    "application/vnd.android.package-archive"
                }
                TYPE_BROWSER_ZIP -> {
                    "application/zip"
                }
                TYPE_BROWSER_DOC_TXT -> {
                    "text/plain"
                }
                TYPE_BROWSER_DOC_PDF -> {
                    "application/pdf"
                }
                TYPE_BROWSER_DOC_WORD -> {
                    "application/msword"
                }
                TYPE_BROWSER_DOC_EXCEL -> {
                    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                }
                else -> {mime ?: "*/*"}
            }
            intent.setDataAndType(uri, dataType)
            if (intent.resolveActivity(context.packageManager) != null) {
                context.startActivity(intent)
            } else {
                try {
                    val chooser = Intent.createChooser(intent, "Choose a browser")
                    context.startActivity(chooser)
                } catch (e: Exception) {
                    Toast.makeText(context, context.getString(R.string.base_open_fail), Toast.LENGTH_SHORT).show()
                }
            }
        } catch (e: Exception) {
            Toast.makeText(context, context.getString(R.string.base_open_fail), Toast.LENGTH_SHORT).show()
        }
    }
}