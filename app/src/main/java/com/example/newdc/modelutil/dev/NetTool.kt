package com.example.newdc.modelutil.dev

import android.content.Context
import android.net.ConnectivityManager
import android.net.DhcpInfo
import android.net.NetworkCapabilities
import android.net.wifi.WifiManager
import android.os.Build
import android.telephony.TelephonyManager
import com.example.newdc.R
import java.lang.reflect.Method
import java.net.*
import java.nio.ByteBuffer
import java.nio.ByteOrder
import java.util.*
import java.util.concurrent.TimeUnit


object NetTool {
    fun findWifiInfo(context: Context): WifiInfoData {
        val wifiManager = context.applicationContext.getSystemService(Context.WIFI_SERVICE) as WifiManager
        val connectStatus = if (getWifiConnectStatus(context)) context.getString(R.string.base_connect) else context.getString(R.string.base_no_connect)

        val wifiInfo = wifiManager.connectionInfo
        val ssid = wifiInfo.ssid
        val bssid = wifiInfo.bssid
        val linkSpeed = "${wifiInfo.linkSpeed} Mbps"
        val frequency = "${wifiInfo.frequency}Hz"
        val support5G = has5Gz(context, wifiManager)

        val ipv4 = findIpv4()
        val ipv6 = findIpv6()
        val ipAddress = ipv4.second ?: findIpParamFromAddress(wifiInfo.ipAddress)
        val ipInterface = ipv4.first

        val dhcpInfo = wifiManager.dhcpInfo
        val dhcpAddress = findIpParamFromAddress(dhcpInfo.serverAddress) ?: ""
        val gateway = findIpParamFromAddress(dhcpInfo.gateway) ?: ""
        val netmask = findIpParamFromAddress(dhcpInfo.netmask) ?: ""
        val dns1 = findIpParamFromAddress(dhcpInfo.dns1) ?: ""
        val dns2 = findIpParamFromAddress(dhcpInfo.dns2) ?: ""

        val leaseDuration = getLeaseDuration(context,dhcpInfo)
        val wifiDirect = getSupportedDirectStatus(context, wifiManager
        )

        return WifiInfoData(connectStatus = connectStatus,ssid = ssid, bssid = bssid,dhcpAddress = dhcpAddress,leaseDuration = leaseDuration,gateway = gateway,netmask = netmask,
            dns1 = dns1,dns2 = dns2,ipv4 = ipAddress ?: "",ipv6 = ipv6,ipInterface = ipInterface,wifiDirect = wifiDirect,
            support5G = support5G, linkSpeed =  linkSpeed, frequency = frequency)
    }

    fun findSimInfo(context: Context): SimInfoData{
        val connectStatus = if (getMobileConnectStatus(context)) context.getString(R.string.base_connect) else context.getString(R.string.base_no_connect)
        val multiSupported = if (multiSimSupportedStatus(context)) context.getString(R.string.base_supported) else context.getString(R.string.base_no_supported)
        val wifiIpv4 = findIpv4()
        val apn = findAPN(context)
        val ipv4 = findSimIpv4(context)
        val ipv6 = findIpv6()
        val ipInterface = wifiIpv4.first

        return SimInfoData(connectStatus,multiSupported,apn,ipv4,ipv6,ipInterface)
    }

    private fun getLeaseDuration(context: Context,dhcpInfo: DhcpInfo): String{
        return try {
            run {
                val raw  = dhcpInfo.leaseDuration * 1000L
                val timeUnit = TimeUnit.MILLISECONDS
                val hours = timeUnit.toHours(raw)
                val minutes = timeUnit.toMinutes(raw)
                String.format( "%02d:%02d:%02d", hours, minutes - TimeUnit.HOURS.toMinutes(hours), timeUnit.toSeconds(raw) - TimeUnit.MINUTES.toSeconds(minutes))
            }
        } catch (e: Exception) {
            context.getString(R.string.base_unknown)
        }
    }

    private fun getSupportedDirectStatus(context: Context, wifiManager: WifiManager): String{
        return if (wifiManager.isWifiEnabled) context.getString(R.string.base_supported) else context.getString(R.string.base_no_supported)
    }

    private fun findIpv4(): Pair<String, String?>{
        val interfaces = NetworkInterface.getNetworkInterfaces()
        while (interfaces.hasMoreElements()) {
            val networkInterface = interfaces.nextElement()
            val interfaceName = networkInterface.displayName
            val addresses = networkInterface.inetAddresses
            while (addresses.hasMoreElements()) {
                val address = addresses.nextElement()
                if (address != null && !address.isLoopbackAddress && !address.isLinkLocalAddress) {
                    val ipAddress = address.hostAddress
                    return Pair(interfaceName,ipAddress)
                }
            }
        }
        return Pair("","")
    }

    private fun findIpv6(): String{
        val sb = StringBuffer()
        val interfaces = NetworkInterface.getNetworkInterfaces()

        while (interfaces.hasMoreElements()) {
            val networkInterface = interfaces.nextElement()

            val addresses = networkInterface.inetAddresses
            while (addresses.hasMoreElements()) {
                val address = addresses.nextElement()

                if (address is Inet6Address && !address.isLoopbackAddress()) {
                    val ipv6Address = address.hostAddress
                    sb.append(ipv6Address)
                    sb.append("\n")
                }
            }
        }
        return sb.toString()
    }

    private fun has5Gz(context: Context,wifiManager: WifiManager): String {
        val wifiInfo = wifiManager.connectionInfo
        return if (wifiInfo.frequency in 5000..5999) context.getString(R.string.base_supported) else context.getString(R.string.base_no_supported)
    }

    fun getWifiConnectStatus(context: Context): Boolean {
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val network = connectivityManager.activeNetwork ?: return false
            val networkCapabilities = connectivityManager.getNetworkCapabilities(network)
                ?: return false
            return networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI)
        } else {
            val networkInfo = connectivityManager.activeNetworkInfo
            return networkInfo != null && networkInfo.isConnected && networkInfo.type == ConnectivityManager.TYPE_WIFI
        }
    }

    private fun findIpParamFromAddress(type: Int): String? {
        return InetAddress.getByAddress(ByteBuffer.allocate(4).order(ByteOrder.LITTLE_ENDIAN).putInt(type).array()).hostAddress
    }

    fun findWifiSignalStrength(wifiManager: WifiManager): Pair<String, String> {
        val dbm = wifiManager.connectionInfo.rssi
        if (dbm > 0 || dbm < 100){
            val signalStrength = WifiManager.calculateSignalLevel(dbm,100)
            return Pair("$dbm dBm","$signalStrength%")
        }
        return Pair("$- dBm","")
    }

    fun getMobileConnectStatus(context: Context): Boolean {
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val network = connectivityManager.activeNetwork ?: return false
            val networkCapabilities = connectivityManager.getNetworkCapabilities(network)
                ?: return false
            return networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR)
        } else {
            val networkInfo = connectivityManager.activeNetworkInfo
            return networkInfo?.type == ConnectivityManager.TYPE_MOBILE && networkInfo.isConnected
        }
    }

    fun simCardStatus(context: Context): Boolean {
        val telephonyManager = context.getSystemService(Context.TELEPHONY_SERVICE) as TelephonyManager
        return telephonyManager.simState != TelephonyManager.SIM_STATE_ABSENT
    }

    fun multiSimSupportedStatus(context: Context): Boolean {
        val telephonyManager = context.getSystemService(Context.TELEPHONY_SERVICE) as TelephonyManager
        return if (Build.VERSION.SDK_INT >= 23) {
            telephonyManager.phoneCount > 1
        } else {
            try {
                val method: Method? = TelephonyManager::class.java.getMethod("getSimCount")
                method?.isAccessible = true
                val simCount = method?.invoke(telephonyManager) as? Int ?: 0
                simCount > 1
            } catch (e: Exception) {
                try {
                    val method: Method? = TelephonyManager::class.java.getMethod("isMultiSimEnabled")
                    method?.isAccessible = true
                    val isMultiSimEnabled = method?.invoke(telephonyManager) as? Boolean ?: false
                    isMultiSimEnabled
                } catch (e: Exception) {
                    false
                }
            }
        }
    }

    fun findAPN(context: Context): String{
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        return connectivityManager.activeNetworkInfo?.extraInfo ?: ""
    }

    fun findSimIpv4(context: Context): String {
        val cm = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        val activeNetwork = cm.activeNetworkInfo
        if (activeNetwork != null && activeNetwork.isConnected) {
            // 获取网络接口
            try {
                val networkInterfaces = NetworkInterface.getNetworkInterfaces()
                for (ni in Collections.list(networkInterfaces)) {
                    val inetAddresses = ni.inetAddresses
                    for (inetAddress in Collections.list(inetAddresses)) {
                        if (!inetAddress.isLoopbackAddress && inetAddress is Inet4Address) {
                            // 这是一个IPv4地址
                            return inetAddress.getHostAddress() ?: ""
                        }
                    }
                }
            } catch (e: SocketException) {
                e.printStackTrace()
            }
        }
        return ""
    }

}

data class WifiInfoData(
    val connectStatus: String = "",
    val ssid: String = "",
    val bssid: String = "",
    val dhcpAddress: String = "",
    val leaseDuration: String = "",
    val gateway: String = "",
    val netmask: String = "",
    val dns1: String = "",
    val dns2: String = "",
    val ipv4: String = "",
    val ipv6: String = "",
    val ipInterface: String = "",
    val wifiDirect: String = "",
    val support5G: String = "",
    val linkSpeed: String = "",
    val frequency: String = ""
)

data class SimInfoData(
    val connectStatus: String = "",
    val multiSim: String = "",
    val APN: String = "",
    val ipv4: String = "",
    val ipv6: String = "",
    val ipInterface: String = ""
)