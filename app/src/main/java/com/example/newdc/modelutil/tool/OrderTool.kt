package com.example.newdc.modelutil.tool

import com.example.newdc.modelutil.data.ItemFileData
import com.example.newdc.modelutil.dev.ApkInfoData

object OrderTool {

    const val TYPE_ORDER_NEW = 0
    const val TYPE_ORDER_OLD = 1
    const val TYPE_ORDER_LARGE = 2
    const val TYPE_ORDER_SMALL = 3
    const val TYPE_ORDER_A_Z = 4
    const val TYPE_ORDER_Z_A = 5

    fun mediaOrderByTime(fileList: MutableList<ItemFileData>): MutableList<ItemFileData> {
        timeOrder(fileList,true)
        val orderList = mutableListOf<ItemFileData>()
        var groupDate = ""
        for (item in fileList) {
            val date = FormatTool.ymdTimeFormat(item.modifyDate)
            if (groupDate == date) {
                orderList.add(item)
            } else {
                val dataModel = ItemFileData(date,0, "",item.modifyDate,MediaStorageTool.TYPE_BROWSER_TITLE,0)
                orderList.add(dataModel)
                orderList.add(item)
                groupDate = date
            }
        }
        return orderList
    }

    fun orderFileType(orderType: Int, fileList: MutableList<ItemFileData>): MutableList<ItemFileData> {
        when (orderType) {
            TYPE_ORDER_NEW -> {
                timeOrder(fileList,true)
            }
            TYPE_ORDER_OLD -> {
                timeOrder(fileList,false)
            }
            TYPE_ORDER_LARGE -> {
                sizeOrder(fileList,true)
            }
            TYPE_ORDER_SMALL -> {
                sizeOrder(fileList,false)
            }
            TYPE_ORDER_Z_A -> {
                nameOrder(fileList,true)
            }
            TYPE_ORDER_A_Z -> {
                nameOrder(fileList,false)
            }
        }
        return fileList
    }

    fun orderApkType(orderType: Int, apkList: MutableList<ApkInfoData>): MutableList<ApkInfoData> {
        when (orderType) {
            TYPE_ORDER_NEW -> {
                timeApkOrder(apkList,true)
            }
            TYPE_ORDER_OLD -> {
                timeApkOrder(apkList,false)
            }
            TYPE_ORDER_LARGE -> {
                sizeApkOrder(apkList,true)
            }
            TYPE_ORDER_SMALL -> {
                sizeApkOrder(apkList,false)
            }
            TYPE_ORDER_Z_A -> {
                nameApkOrder(apkList,true)
            }
            TYPE_ORDER_A_Z -> {
                nameApkOrder(apkList,false)
            }
        }
        return apkList
    }

    private fun timeOrder(fileItems: MutableList<ItemFileData>, ascending: Boolean = false) {
        if (ascending) {
            fileItems.sortWith(compareByDescending(ItemFileData::isDir).thenByDescending { it.modifyDate })
        } else {
            fileItems.sortWith(compareByDescending(ItemFileData::isDir).thenBy { it.modifyDate })
        }
    }

    fun nameOrder(fileItems: MutableList<ItemFileData>, ascending: Boolean = false) {
        if (ascending) {
            fileItems.sortWith(compareByDescending(ItemFileData::isDir).thenByDescending { it.title })
        } else {
            fileItems.sortWith(compareByDescending(ItemFileData::isDir).thenBy { it.title })
        }
    }

    private fun sizeOrder(fileItems: MutableList<ItemFileData>, ascending: Boolean = false) {
        if (ascending) {
            fileItems.sortWith(compareByDescending(ItemFileData::isDir).thenByDescending { it.fileSize })
        } else {
            fileItems.sortWith(compareByDescending(ItemFileData::isDir).thenBy { it.fileSize })
        }
    }

    private fun timeApkOrder(fileItems: MutableList<ApkInfoData>, ascending: Boolean = false) {
        if (ascending) {
            fileItems.sortByDescending { it.date }
        } else {
            fileItems.sortBy { it.date }
        }
    }

    fun nameApkOrder(fileItems: MutableList<ApkInfoData>, ascending: Boolean = false) {
        if (ascending) {
            fileItems.sortByDescending { it.apkName }
        } else {
            fileItems.sortBy { it.apkName }
        }
    }

    private fun sizeApkOrder(fileItems: MutableList<ApkInfoData>, ascending: Boolean = false) {
        if (ascending) {
            fileItems.sortByDescending { it.size }
        } else {
            fileItems.sortBy { it.size }
        }
    }

}