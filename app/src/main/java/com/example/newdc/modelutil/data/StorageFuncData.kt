package com.example.newdc.modelutil.data

import android.content.Context
import android.os.Parcelable
import com.example.newdc.R
import kotlinx.parcelize.Parcelize

@Parcelize
data class StorageFuncData(
    val type : Int,
    val name : String,
    val iconRes : Int,
    val backgroundColor : Int
): Parcelable {
    companion object{
        fun getHomeMoreInfoData(context: Context) : MutableList<StorageFuncData>{
            return mutableListOf(
                StorageFuncData(1,context.getString(R.string.title_device),R.mipmap.icon_device,R.drawable.bg_item_device),
                StorageFuncData(2,context.getString(R.string.title_system),R.mipmap.icon_system,R.drawable.bg_item_system),
                StorageFuncData(3,context.getString(R.string.title_display),R.mipmap.icon_display,R.drawable.bg_item_display),
                StorageFuncData(4,context.getString(R.string.title_network),R.mipmap.icon_network,R.drawable.bg_item_network),
                StorageFuncData(5,context.getString(R.string.title_cpu),R.mipmap.icon_cpu,R.drawable.bg_item_cpu),
                StorageFuncData(6,context.getString(R.string.title_battery),R.mipmap.icon_battery,R.drawable.bg_item_battery),
                StorageFuncData(7,context.getString(R.string.title_apps),R.mipmap.icon_app,R.drawable.bg_item_app),
                StorageFuncData(8,context.getString(R.string.title_tests),R.mipmap.icon_test,R.drawable.bg_item_test)
            )
        }
    }
}