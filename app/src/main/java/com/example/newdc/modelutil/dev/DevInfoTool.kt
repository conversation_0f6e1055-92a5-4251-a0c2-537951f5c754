package com.example.newdc.modelutil.dev

import android.annotation.SuppressLint
import android.content.Context
import android.content.pm.ApplicationInfo
import android.net.wifi.WifiInfo
import android.net.wifi.WifiManager
import android.os.Build
import android.provider.Settings
import com.example.newdc.R
import com.example.newdc.modelutil.tool.ExtraTool.getSysProFromString
import java.net.NetworkInterface

object DevInfoTool {
    private val devList = arrayOf(
        "ro.vendor.asus.product.mkt_name",
        "ro.lge.petname",
        "ro.boot.vendor.lge.petname",
        "ro.product.odm.marketname"
    )

    fun findDevInfo(): DevInfoData{
        val deviceName = findDevNameFromManu()
        val model = Build.MODEL
        val manu = Build.MANUFACTURER
        val device = Build.DEVICE
        val hardware = "ro.hardware".getSysProFromString()
        val board = Build.BOARD
        val brand = Build.BRAND

        return DevInfoData(name = deviceName,model = model, manual = manu,
                    device = device, hardware = hardware, board = board, brand = brand)
    }

    fun findDevSerInfo(context: Context): SerInfoData{
        val deviceId = findDevId(context)
        val hardSerial = findDevSerial()
        val buildFinger = Build.FINGERPRINT
        val wifiMac = findWifiMac(context)
        val bluetoothMac = findBlueToothMac(context)
        val usbDebug = if (0 != context.applicationInfo.flags and ApplicationInfo.FLAG_DEBUGGABLE) context.getString(
            R.string.base_on) else context.getString(R.string.base_off)

        return SerInfoData(deviceId = deviceId, hardSerial = hardSerial, buildFinger = buildFinger, wifiMac = wifiMac,
            blueToothMac = bluetoothMac,usbDebug = usbDebug)
    }

    @SuppressLint("HardwareIds")
    private fun findDevId(context: Context): String {
        return Settings.Secure.getString(context.contentResolver, Settings.Secure.ANDROID_ID)
    }

    @SuppressLint("HardwareIds")
    private fun findWifiMac(context: Context): String {
        val wifiManager = context.applicationContext.getSystemService(Context.WIFI_SERVICE) as WifiManager
        val wifiInfo: WifiInfo? = wifiManager.connectionInfo
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            // Android 10 及以上版本需要特殊处理
            wifiInfo?.macAddress ?: ""
        } else {
            // Android 9 及以下版本可以直接获取 MAC 地址
            wifiManager.connectionInfo.macAddress
        }
    }

    @SuppressLint("HardwareIds")
    private fun findDevSerial(): String{
        var serial = "getprop ro.serialno".getSysProFromString()
        if (serial.isBlank()){
            serial = Build.SERIAL
        }
        return serial
    }

    private fun findBlueToothMac(context: Context): String {
        try {
            val interfaces = NetworkInterface.getNetworkInterfaces()
            while (interfaces.hasMoreElements()) {
                val networkInterface = interfaces.nextElement()
                if (networkInterface.name.equals("bluetooth0", ignoreCase = true)) {
                    val macBytes = networkInterface.hardwareAddress
                    if (macBytes != null && macBytes.isNotEmpty()) {
                        val builder = StringBuilder()
                        for (b in macBytes) {
                            builder.append(String.format("%02X:", b))
                        }
                        if (builder.isNotEmpty()) {
                            builder.deleteCharAt(builder.length - 1)
                        }
                        return builder.toString()
                    }
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return context.getString(R.string.base_unknown)
    }

    fun findDevNameFromManu(): String{
        val mau = Build.MANUFACTURER
        var name = mau
        if (hwDevice()){
            name = "ro.config.marketing_name".getSysProFromString()
        } else {
            if (opDevice()) {
                name = "ro.oppo.market.name".getSysProFromString()
                if (name.isBlank()) {
                    name = "ro.vendor.oplus.market.name".getSysProFromString()
                }
            } else {
                if (xmDevice()) {
                    name = "ro.product.marketname".getSysProFromString()
                } else {
                    if (vvDevice()) {
                        "ro.vendor.vivo.market.name".getSysProFromString()
                    } else {
                        if (ztDevice()) {
                            "ro.vendor.product.ztename".getSysProFromString()
                        } else if(motoDevice()){
                            "ro.product.display".getSysProFromString()
                        }
                    }
                }
            }
        }
        if (name.isBlank()){
            devList.forEach {
                val temp = it.getSysProFromString()
                if (temp.isNotBlank())return temp
            }
        }
        return name
    }

    fun hwDevice(): Boolean{
        val mau = Build.MANUFACTURER
        if ("huawei".equals(mau, ignoreCase = true) || "honor".equals(mau, ignoreCase = true)) {
            return true
        }
        val brand = Build.BRAND
        return "huawei".equals(brand, ignoreCase = true) || "honor".equals(brand, ignoreCase = true)
    }

    fun opDevice(): Boolean{
        val mau = Build.MANUFACTURER
        return !(!"oppo".equals(mau,true) && !"realme".equals(mau,true) && !"oneplus".equals(mau,true))
    }

    fun xmDevice(): Boolean{
        val mau = Build.MANUFACTURER
        return !(!"xiaomi".equals(mau,true) && !"redmi".equals(mau,true) && !"poco".equals(mau,true))
    }

    fun vvDevice(): Boolean{
        val mau = Build.MANUFACTURER
        return !(!"vivo".equals(mau,true) && !"iqoo".equals(mau,true))
    }

    fun ztDevice(): Boolean{
        val mau = Build.MANUFACTURER
        return !(!"zte".equals(mau,true) && !"nubia".equals(mau,true))
    }

    fun motoDevice(): Boolean{
        val mau = Build.MANUFACTURER
        return "motorola".equals(mau,true)
    }
}

data class DevInfoData(
    val name: String = "",
    val model: String = "",
    val manual: String = "",
    val device: String = "",
    val hardware: String = "",
    val board: String = "",
    val brand: String = ""
)

data class SerInfoData(
    val deviceId: String = "",
    val hardSerial: String = "",
    val buildFinger: String = "",
    val wifiMac: String = "",
    val blueToothMac: String = "",
    val usbDebug: String = ""
)