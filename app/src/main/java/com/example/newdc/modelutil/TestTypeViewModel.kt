package com.example.newdc.modelutil

import android.content.Context
import android.hardware.Sensor
import android.hardware.SensorEvent
import android.hardware.SensorEventListener
import android.hardware.SensorManager
import android.hardware.camera2.CameraAccessException
import android.hardware.camera2.CameraCharacteristics
import android.hardware.camera2.CameraManager
import android.media.AudioAttributes
import android.media.AudioManager
import android.media.MediaPlayer
import android.media.RingtoneManager
import android.os.Build
import android.os.VibrationEffect
import android.os.Vibrator
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel

class TestTypeViewModel: ViewModel() {
    private var cameraId = ""
    private var cameraManager: CameraManager? = null

    private var mediaPlayer: MediaPlayer? = null
    private var audioManager: AudioManager? = null

    private var sensorManager: SensorManager? = null
    private var bodySensor: Sensor? = null
    private var sensorListener: SensorEventListener? = null
    private var vibrator: Vibrator? = null

    var getLightLx = MutableLiveData<Float>()
    var getProximityStatus = MutableLiveData<Boolean>()


    fun flashOpen(context: Context){
        cameraManager = context.getSystemService(Context.CAMERA_SERVICE) as CameraManager
        try {
            val cameraIdList = cameraManager?.cameraIdList!!
            for (id in cameraIdList) {
                val flashAvailable = cameraManager?.getCameraCharacteristics(id)?.get(CameraCharacteristics.FLASH_INFO_AVAILABLE) ?: false
                if (flashAvailable) {
                    cameraId = id
                    break
                }
            }
            cameraManager?.setTorchMode(cameraId, true)
        } catch (e: CameraAccessException) {
            e.printStackTrace()
        }
    }

    fun flashClose(context: Context){
        try {
            cameraManager?.setTorchMode(cameraId, false)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun earSoundPlay(context: Context,out: Boolean){
        earSoundStop()
        audioManager = context.getSystemService(Context.AUDIO_SERVICE) as AudioManager
        mediaPlayer = MediaPlayer().apply {
            setDataSource(context,RingtoneManager.getDefaultUri(1))
            prepare()
        }

        if (out){
            outSpeakerPlay()
        }else{
            innerSpeakerPlay()
        }
    }

    private fun outSpeakerPlay() {
        val audioAttributes = AudioAttributes.Builder()
            .setLegacyStreamType(AudioManager.STREAM_RING)
            .build()
        mediaPlayer?.setAudioAttributes(audioAttributes)
        audioManager?.apply {
            isSpeakerphoneOn = true
            mode = AudioManager.MODE_NORMAL
        }
        mediaPlayer?.start()
    }

    private fun innerSpeakerPlay() {
        audioManager?.apply {
            isSpeakerphoneOn = false
            setStreamVolume(AudioManager.STREAM_VOICE_CALL, audioManager!!.getStreamMaxVolume(AudioManager.STREAM_VOICE_CALL), 0)
            mode = AudioManager.MODE_IN_COMMUNICATION
        }
        mediaPlayer?.start()
    }

    fun earSoundStop() {
        if (mediaPlayer != null && mediaPlayer!!.isPlaying) {
            mediaPlayer!!.stop()
        }
    }




    fun getSensorValue(context: Context,type: Int){
        sensorManager = context.getSystemService(Context.SENSOR_SERVICE) as SensorManager
        bodySensor = sensorManager?.getDefaultSensor(type)

        sensorListener = object : SensorEventListener {
            override fun onAccuracyChanged(sensor: Sensor?, accuracy: Int) {
            }

            override fun onSensorChanged(event: SensorEvent) {
                if (event.sensor.type == Sensor.TYPE_LIGHT) {
                    val lightIntensity = event.values[0]
                    getLightLx.value = lightIntensity
                }
                if (event.sensor.type == Sensor.TYPE_PROXIMITY) {
                    val distance = event.values[0]
                    getProximityStatus.value = distance >= 0.0f && distance < bodySensor!!.maximumRange
                }
            }
        }
        bodySensor?.let { sensor ->
            sensorManager?.registerListener(
                sensorListener, sensor, SensorManager.SENSOR_DELAY_NORMAL
            )
        }
    }

    fun destroyBodySensor(){
        sensorManager?.unregisterListener(sensorListener)
    }

    fun sensorVibStart(context: Context,isOneShot: Boolean = false){
        vibrator = context.getSystemService(Context.VIBRATOR_SERVICE) as Vibrator
        val pattern = if (isOneShot) longArrayOf(0, 500) else longArrayOf(1000, 500)
        if (vibrator!!.hasVibrator()) {
            try {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    vibrator!!.vibrate(VibrationEffect.createWaveform(pattern, if (isOneShot) -1 else 0))
                } else {
                    vibrator!!.vibrate(pattern, if (isOneShot) -1 else 0)
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    fun sensorVibStop(){
        if (vibrator != null) {
            vibrator!!.cancel()
        }
    }

}