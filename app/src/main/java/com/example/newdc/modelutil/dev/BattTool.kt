package com.example.newdc.modelutil.dev

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.BatteryManager
import com.example.newdc.R

object BatteryTool {
    fun getBatteryInfo(context: Context): BatteryInfoData{
        val intentFilter = IntentFilter(Intent.ACTION_BATTERY_CHANGED)
        val mBatt = context.registerReceiver(null, intentFilter)

        if (mBatt != null) {
            val level = mBatt.getIntExtra("level", 0)
            val scale = mBatt.getIntExtra("scale", 0)
            val voltage = mBatt.getIntExtra("voltage", 0)
            val status = mBatt.getIntExtra("status", -1)
            val plugged = mBatt.getIntExtra("plugged", -1)
            val health = mBatt.getIntExtra("health", -1)
            val technology = mBatt.getStringExtra("technology") ?: context.getString(R.string.base_unknown)
            val temperature = findBatteryTemp(context,mBatt)

            val chargeStatus = findChargeStatus(context,status)
            val pluggedStatus = findPluggedStatus(context,plugged)
            val healthStats = findHealthStatus(context,health)
            val capability = "${findBattCapacity(context)} mAh"
            val batteryCurrent = "${findBattCurrent(context)} mA"

            return BatteryInfoData(chargeStatus = chargeStatus, pluggedStatus = pluggedStatus, healthStatus = healthStats,
                capability = capability,level = "$level%",voltage = String.format("%.2f V",voltage / 1000f),technology = technology,
                temperature = "${temperature}°C", batteryCurrent = batteryCurrent)
        }
        return BatteryInfoData()
    }

    private fun findChargeStatus(context: Context, status: Int): String {
        return if (status == BatteryManager.BATTERY_STATUS_CHARGING || status == BatteryManager.BATTERY_STATUS_FULL) context.getString(
            R.string.batt_charge) else context.getString(R.string.batt_discharge)
    }

    private fun findPluggedStatus(context: Context, code: Int): String {
        return when (code) {
            BatteryManager.BATTERY_PLUGGED_AC -> context.getString(R.string.batt_plugged_ac)
            BatteryManager.BATTERY_PLUGGED_USB -> context.getString(R.string.batt_plugged_usb)
            BatteryManager.BATTERY_PLUGGED_WIRELESS -> context.getString(R.string.batt_plugged_wireless)
            else -> context.getString(R.string.batt_plugged_battery)
        }
    }

    @SuppressLint("PrivateApi")
    private fun findBattCapacity(context: Context?): Double {
        val powerClazz = "com.android.internal.os.PowerProfile"
        return try {
            val mPowerProfile = Class.forName(powerClazz).getConstructor(Context::class.java).newInstance(context)
            Class.forName(powerClazz).getMethod("getBatteryCapacity").invoke(mPowerProfile) as Double
        } catch (e: Exception) {
            e.printStackTrace()
            0.0
        }
    }

    private fun findBattCurrent(context: Context): Int {
        var batteryCurrent = 0
        try {
            val batteryManager = context.getSystemService(Context.BATTERY_SERVICE) as BatteryManager
            batteryCurrent = batteryManager.getIntProperty(BatteryManager.BATTERY_PROPERTY_CURRENT_NOW)
            if (batteryCurrent >= 20000 || batteryCurrent <= -20000) {
                return batteryCurrent / 1000
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return batteryCurrent
    }

    private fun findHealthStatus(context: Context, code: Int): String {
        val status = when (code) {
            BatteryManager.BATTERY_HEALTH_GOOD ->
                context.getString(R.string.batt_heal_good)
            BatteryManager.BATTERY_HEALTH_OVERHEAT ->
                context.getString(R.string.batt_heal_overheat)
            BatteryManager.BATTERY_HEALTH_DEAD ->
                context.getString(R.string.batt_heal_dead)
            BatteryManager.BATTERY_HEALTH_OVER_VOLTAGE ->
                context.getString(R.string.batt_heal_over_voltage)
            BatteryManager.BATTERY_HEALTH_COLD ->
                context.getString(R.string.batt_heal_low_temp)
            BatteryManager.BATTERY_HEALTH_UNSPECIFIED_FAILURE ->
                context.getString(R.string.batt_heal_failure)
            else -> context.getString(R.string.base_unknown)
        }
        return status
    }

    fun findBatteryTemp(context: Context?,intent: Intent?): Float {
        try {
            val battIntent = if (intent == null){
                val filter = IntentFilter(Intent.ACTION_BATTERY_CHANGED)
                context?.registerReceiver(null, filter)
            }else {
                intent
            }
            val temp = battIntent?.getIntExtra(BatteryManager.EXTRA_TEMPERATURE, 0)
            return temp?.toFloat()?.div(10) ?: 0f
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return 0f
    }

    fun getBatterPercent(context: Context?): Int{
        try {
            val batteryManager = context?.getSystemService(Context.BATTERY_SERVICE) as BatteryManager
            return batteryManager.getIntProperty(BatteryManager.BATTERY_PROPERTY_CAPACITY)
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return 0
    }

}

data class BatteryInfoData(
    val chargeStatus: String ="",
    val pluggedStatus: String ="",
    val healthStatus: String ="",
    val capability: String ="",
    val level: String ="",
    val voltage: String ="",
    val technology: String ="",
    val temperature: String ="",
    val batteryCurrent: String =""
)