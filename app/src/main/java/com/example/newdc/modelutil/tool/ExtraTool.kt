package com.example.newdc.modelutil.tool

import android.annotation.SuppressLint
import android.app.Activity
import android.app.KeyguardManager
import android.content.Context
import android.content.res.Resources
import android.graphics.Color
import android.os.PowerManager
import android.util.Log
import android.util.TypedValue
import android.view.View
import android.view.WindowManager
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import com.example.newdc.BuildConfig
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.launch

object ExtraTool {
    fun String.getSysProFromString(): String{
        return try {
            val cls = Class.forName("android.os.SystemProperties")
            cls.getDeclaredMethod("get", String::class.java).invoke(cls, this) as String
        } catch (e: Exception) {
            ""
        }
    }

    val Float.px2dp
        get() = TypedValue.applyDimension(
            TypedValue.COMPLEX_UNIT_PX,
            this,
            Resources.getSystem().displayMetrics
        )

    val Float.dp2px
        get() = TypedValue.applyDimension(
            TypedValue.COMPLEX_UNIT_DIP,
            this,
            Resources.getSystem().displayMetrics
        )

    @SuppressLint("InternalInsetResource")
    fun getToolBarSize(context: Context): Int {
        val resourceId = context.resources.getIdentifier("status_bar_height", "dimen", "android")
        return if (resourceId > 0) context.resources.getDimensionPixelSize(resourceId) else 0
    }

    fun setStatusHind(activity: Activity?) {
        if (activity == null) return
        activity.window.apply {
            decorView.systemUiVisibility = View.SYSTEM_UI_FLAG_LAYOUT_STABLE or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN or View.SYSTEM_UI_FLAG_LIGHT_NAVIGATION_BAR
            attributes.flags = this.attributes.flags and WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS.inv()
            statusBarColor = Color.TRANSPARENT
        }
    }

    fun Context.isLocked(): Boolean {
        val keyguardManager = getSystemService(Context.KEYGUARD_SERVICE) as KeyguardManager
        return keyguardManager.isKeyguardLocked
    }

    fun Context.isScreenOn(): Boolean {
        val powerManager = getSystemService(Context.POWER_SERVICE) as PowerManager
        return powerManager.isScreenOn
    }

    fun LifecycleOwner.countDownFlow(start: () -> Unit ={}, end: () -> Unit,
        next: (time: Int) -> Unit, time: Int = 3, delay: Long = 1000
    ): Job {
        return lifecycleScope.launch {
            flow {
                (time downTo 0).forEach {
                    delay(delay)
                    emit(it)
                }
            }.onStart {
                start()
            }.onCompletion {
                end()
            }.catch {
                end()
            }.collect {
                next(time - it)
            }
        }
    }

    fun Any.debug(tag: String, msg: String) {
        if (BuildConfig.DEBUG) {
            Log.d(tag, msg)
        }
    }

}