package com.example.newdc.modelutil.tool

import android.annotation.SuppressLint
import android.text.SpannableString
import android.text.Spanned
import android.text.style.AbsoluteSizeSpan
import java.text.SimpleDateFormat
import java.util.*

object FormatTool {

    @SuppressLint("SimpleDateFormat")
    fun ymdTimeFormat(time: Long): String {
        return try {
            val format = SimpleDateFormat("yyyy-MM-dd")
            if (time.toString().length <= 10) {
                format.format(Date(time * 1000))
            } else {
                format.format(Date(time))
            }
        } catch (e: Exception) {
            e.printStackTrace()
            ""
        }
    }

    @SuppressLint("SimpleDateFormat")
    fun mdyTimeFormat(date: Long): String{
        var nDate = ""
        try {
            val format = SimpleDateFormat("MMM dd,yyyy", Locale.ENGLISH)
            nDate = if (date.toString().length <= 10) {
                format.format(Date(date * 1000))
            } else {
                format.format(Date(date))
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return nDate
    }

    @SuppressLint("SimpleDateFormat")
    fun formatFileMin(time: Long): String{
        val format = SimpleDateFormat("mm:ss")
        return format.format(time)
    }

    @SuppressLint("SimpleDateFormat")
    fun getUTCTime(): String{
        val format = "yyyy-MM-dd"
        val sdf = SimpleDateFormat(format)
        sdf.timeZone = TimeZone.getTimeZone("UTC")
        return sdf.format(Date())
    }

    fun certDateFormat(date: Date): String {
        val sdf = SimpleDateFormat("yy/MM/dd HH:mmaa", Locale.getDefault())
        return sdf.format(date)
    }

    fun spannableStringBySize(text: String, numberSize: Int, letterSize: Int): SpannableString {
        val spannableString = SpannableString(text)
        var index = 0
        while (index < text.length) {
            if (text[index] in '0'..'9' || text[index] == '.') {
                // 如果是数字或小数点，设置数字的字体大小
                spannableString.setSpan(AbsoluteSizeSpan(numberSize, true), index, index + 1, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
            } else {
                // 如果是字母，设置字母的字体大小
                spannableString.setSpan(AbsoluteSizeSpan(letterSize, true), index, index + 1, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
            }
            index++
        }
        return spannableString
    }
}