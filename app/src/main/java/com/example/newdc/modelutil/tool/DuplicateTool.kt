package com.example.newdc.modelutil.tool

import android.content.Context
import android.os.Environment
import com.example.newdc.BaseApplication
import com.example.newdc.R
import com.example.newdc.modelutil.data.ItemFileData
import org.apache.commons.io.FileUtils
import org.apache.commons.io.IOUtils
import java.io.File
import java.io.FileInputStream
import java.io.IOException

object DuplicateUtil {
    //video
    private const val DUP_TYPE_MP4 = "dup_mime_mp4"
    private const val DUP_TYPE_MOV = "dup_mime_mov"
    private const val DUP_TYPE_M4V = "dup_mime_m4v"
    private const val DUP_TYPE_AVI = "dup_mime_avi"
    private const val DUP_TYPE_3GP = "dup_mime_3gp"
    private const val DUP_TYPE_WEBM = "dup_mime_webm"
    private const val DUP_TYPE_WM = "dup_mime_wm"
    private const val DUP_TYPE_MKV = "dup_mime_mkv"
    private const val DUP_TYPE_FLV = "dup_mime_flv"
    private const val DUP_TYPE_DV = "dup_mime_dv"
    private const val DUP_TYPE_ASX = "dup_mime_asx"

    //image
    private const val DUP_TYPE_MPEG = "dup_mime_mpeg"
    private const val DUP_TYPE_JPG = "dup_mime_jpg"
    private const val DUP_TYPE_JPEG = "dup_mime_jpeg"
    private const val DUP_TYPE_PNG = "dup_mime_png"
    private const val DUP_TYPE_BMP = "dup_mime_bmp"
    private const val DUP_TYPE_GIF = "dup_mime_gif"
    private const val DUP_TYPE_WBMP = "dup_mime_wbmp"


    private var groupIndex = 0

    fun findDuplicatesFiles(context: Context): MutableList<ItemFileData> {
        groupIndex = 0
        val filesHashMap = hashMapOf<String, MutableList<ItemFileData>>()
        val duplicatesList = mutableListOf<ItemFileData>()
        filesHashMap.clear()
        getMediaFiles(context,filesHashMap)
        val keysList = filesHashMap.keys
        for (key in keysList) {
            val fileList = filesHashMap[key]
            if (fileList != null) {
                val fupFiles = findDuplicatesFiles(context,fileList)
                duplicatesList.addAll(fupFiles)
            }
        }
        return  duplicatesList
    }

    private fun initTypeContentHashMap(contextList: HashMap<String, MutableList<ItemFileData>>) {
        contextList[DUP_TYPE_PNG] = mutableListOf()
        contextList[DUP_TYPE_JPEG] = mutableListOf()
        contextList[DUP_TYPE_GIF] = mutableListOf()
        contextList[DUP_TYPE_WBMP] = mutableListOf()
        contextList[DUP_TYPE_MP4] = mutableListOf()
        contextList[DUP_TYPE_MOV] = mutableListOf()
        contextList[DUP_TYPE_M4V] = mutableListOf()
        contextList[DUP_TYPE_AVI] = mutableListOf()
        contextList[DUP_TYPE_3GP] = mutableListOf()
        contextList[DUP_TYPE_WEBM] = mutableListOf()
        contextList[DUP_TYPE_MKV] = mutableListOf()
        contextList[DUP_TYPE_FLV] = mutableListOf()
        contextList[DUP_TYPE_DV] = mutableListOf()
    }

    private fun findDuplicatesFiles(context: Context,fileList: MutableList<ItemFileData>): MutableList<ItemFileData> {
        var duplicateMap = hashMapOf<Long, MutableList<ItemFileData>>()
        if (fileList.isNotEmpty()) {
            duplicateMap = findDuplicatesBySize(fileList)
        }
        val tempList = mutableListOf<ItemFileData>()
        for ((_, files) in duplicateMap) {
            val sampleList = mutableListOf<ItemFileData>()
            if (files.size > 1) {
                for (i in 0 until files.size - 1) {
                    try {
                        for (j in i + 1 until files.size) {
                            val file1 = File(files[i].filePath)
                            val file2 = File(files[j].filePath)
                            if (contentEquals(file1, file2)) {
                                addFileList(sampleList, files[i])
                                addFileList(sampleList, files[j])
                            }
                        }
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                }
            }
            if (sampleList.size > 1) {
                groupIndex ++
                val item = ItemFileData(context.getString(R.string.base_groups, groupIndex),0,"",0, MediaStorageTool.TYPE_BROWSER_TITLE,0)
                tempList.add(item)
                tempList.addAll(sampleList)
            }
        }
        return tempList
    }

    private fun findDuplicatesBySize(fileList: MutableList<ItemFileData>): HashMap<Long, MutableList<ItemFileData>> {
        val fileSizeList: HashMap<Long, MutableList<ItemFileData>> = HashMap()
        for (it in fileList) {
            if (fileSizeList.containsKey(it.fileSize)) {
                fileSizeList[it.fileSize]?.add(it)
            } else {
                val newFile = mutableListOf<ItemFileData>()
                newFile.add(it)
                fileSizeList[it.fileSize] = newFile
            }
        }
        return fileSizeList
    }

    private fun addFileList(list: MutableList<ItemFileData>, data: ItemFileData) {
        if (list.contains(data)) return
        if (isRootFile(data.filePath)) {
            list.add(0, data)
        } else {
            list.add(data)
        }
    }

    private fun getMediaFiles(context: Context,contextList: HashMap<String, MutableList<ItemFileData>>) {
        initTypeContentHashMap(contextList)
        val imageList = MediaStorageTool.findImageFile(context)
        val videoList = MediaStorageTool.findVideoFile(context)
        collectImageList(imageList,contextList)
        collectVideoList(videoList,contextList)
    }

    private fun isRootFile(path: String): Boolean {
        val rootPath = Environment.getExternalStorageDirectory().absolutePath
        return (path.startsWith(rootPath + "/" + Environment.DIRECTORY_DCIM) || path.startsWith(
            rootPath + "/" + Environment.DIRECTORY_PICTURES
        )
                || path.startsWith(rootPath + "/" + Environment.DIRECTORY_PICTURES) || path.startsWith(
            rootPath + "/" + Environment.DIRECTORY_MOVIES
        )
                || path.startsWith(rootPath + "/" + Environment.DIRECTORY_ALARMS))
    }

    private fun collectImageList(imageList: MutableList<ItemFileData>,contextList: HashMap<String, MutableList<ItemFileData>>) {
        for (it in imageList) {
            when (it.mime) {
                "image/png" -> contextList[DUP_TYPE_PNG]!!.add(it)
                "image/jpeg" -> contextList[DUP_TYPE_JPEG]!!.add(it)
                "image/jpg" -> contextList[DUP_TYPE_JPG]!!.add(it)
                "image/gif" -> contextList[DUP_TYPE_GIF]!!.add(it)
                "image/x-ms-bmp" -> contextList[DUP_TYPE_WBMP]!!.add(it)
            }
        }
    }

    private fun collectVideoList(videoList: List<ItemFileData>,contextList: HashMap<String, MutableList<ItemFileData>>) {
        for (it in videoList) {
            when (it.mime) {
                "video/mp4" -> contextList[DUP_TYPE_MP4]!!.add(it)
                "video/quicktime" -> contextList[DUP_TYPE_MOV]!!.add(it)
                "video/x-m4v" -> contextList[DUP_TYPE_M4V]!!.add(it)
                "application/x-troff-msvideo" -> contextList[DUP_TYPE_AVI]!!.add(it)
                "video/3gpp" -> contextList[DUP_TYPE_3GP]!!.add(it)
                "video/webm" -> contextList[DUP_TYPE_WEBM]!!.add(it)
                "video/x-ms-wmv" -> contextList[DUP_TYPE_MKV]!!.add(it)
                "video/x-flv" -> contextList[DUP_TYPE_FLV]!!.add(it)
                "video/dv" -> contextList[DUP_TYPE_DV]!!.add(it)
            }
        }
    }

    @Throws(IOException::class)
    fun contentEquals(file1: File, file2: File): Boolean {
        if (!file1.exists()) {
            return false
        }
        if (!file2.exists()) {
            return false
        }
        if (file1.length() != file2.length()) {
            return false
        }
        if (file1.length() <= 3000) {
            return try {
                FileUtils.contentEquals(file1, file2)
            } catch (e: IOException) {
                e.printStackTrace()
                false
            }
        }
        if (file1.exists()) {
            try {
                FileInputStream(file1).use { input1 ->
                    FileInputStream(file2).use { input2 ->
                        val startBufferFile1 = ByteArray(512)
                        IOUtils.read(input1, startBufferFile1, 0, 512)
                        var str = String(startBufferFile1)
                        IOUtils.skip(input1, file1.length() / 2 - 256)
                        val midBufferFile1 = ByteArray(512)
                        IOUtils.read(input1, midBufferFile1, 0, 512)
                        val midTextFile1 = String(midBufferFile1)
                        IOUtils.skip(input1, file1.length() - 512)
                        val endBufferFile1 = ByteArray(512)
                        IOUtils.read(input1, endBufferFile1, 0, 512)
                        val endTextFile1 = String(endBufferFile1)
                        val startBufferFile2 = ByteArray(512)
                        IOUtils.read(input2, startBufferFile2, 0, 512)
                        str = String(startBufferFile2)
                        IOUtils.skip(input2, file2.length() / 2 - 256)
                        val midBufferFile2 = ByteArray(512)
                        IOUtils.read(input2, midBufferFile2, 0, 512)
                        str = String(midBufferFile2)
                        IOUtils.skip(input2, file2.length() - 512)
                        val endBufferFile2 = ByteArray(512)
                        IOUtils.read(input2, endBufferFile2, 0, 512)
                        val endTextFile2 = String(endBufferFile2)
                        if (midTextFile1 == str && endTextFile1 == endTextFile2) {
                            return true
                        }
                        input1.close()
                        input2.close()
                        input1.close()
                        input2.close()
                        return false
                    }
                }
            } catch (e: IOException) {
                e.printStackTrace()
            }
        }
        return false
    }
}