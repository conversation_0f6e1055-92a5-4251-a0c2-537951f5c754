package com.example.newdc.modelutil.dev

import android.content.Context
import android.os.Build
import android.telephony.PhoneStateListener
import android.telephony.SignalStrength
import android.telephony.TelephonyManager

class SimSignalClass {
    var phoneStateListener: PhoneStateListener? = null
    var formattedSignalStrength: String? = null

    fun startSignalStrengthMonitoring(context: Context, callback: (str: String?) -> Unit) {
        val telephonyManager = context.getSystemService(Context.TELEPHONY_SERVICE) as TelephonyManager
        phoneStateListener = object : PhoneStateListener() {
            override fun onSignalStrengthsChanged(signalStrength: SignalStrength) {
                val dbm = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                    signalStrength.cellSignalStrengths[0].dbm
                } else {
                    signalStrength.gsmSignalStrength * 2 - 113 // 根据规则转换为 dBm
                }
                val asu = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                    signalStrength.cellSignalStrengths[0].asuLevel
                } else {
                    signalStrength.gsmSignalStrength
                }
                formattedSignalStrength = String.format("%d dBm, %d asu", dbm, asu)
                callback(formattedSignalStrength)
            }
        }
        telephonyManager.listen(phoneStateListener, PhoneStateListener.LISTEN_SIGNAL_STRENGTHS)
    }

    fun stopSignalStrengthMonitoring(context: Context) {
        if (phoneStateListener != null) {
            val telephonyManager = context.getSystemService(Context.TELEPHONY_SERVICE) as TelephonyManager
            telephonyManager.listen(phoneStateListener, PhoneStateListener.LISTEN_NONE)
            formattedSignalStrength = null
        }
    }
}