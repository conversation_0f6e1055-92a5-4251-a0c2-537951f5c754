package com.example.newdc.modelutil.data

import android.content.Context
import com.additional.biservice.BiServiceClass
import com.additional.biservice.basedata.AdParamData
import com.additional.biservice.basedata.RemoteAdData
import com.additional.biservice.basedata.RemoteConfigData
import com.additional.biservice.basedata.RemoteNotifyData
import com.additional.firebase.FirebaseEventPost
import com.additional.firebase.InstanceTool
import com.additional.max.AdConfig
import com.example.newdc.BaseApplication
import com.example.newdc.modelutil.tool.EncodeTool
import com.example.newdc.modelutil.tool.ExtraTool.debug
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlin.math.abs

object RemoteConfigClass {
    private const val TAG = "RemoteConfigClass"
    private var notifyContentData: MutableList<NotificationData>? = null
    private var notifyParamData: RemoteNotifyData? = null
    private var adConfigData: RemoteAdData? = null

    @Synchronized
    fun getRemoteConfig(){
        GlobalScope.launch(Dispatchers.IO) {
            if (!StoreKeyData.getString(StoreKeyData.BI_USER_ID).isNullOrEmpty()){
                val currentTime = System.currentTimeMillis()
                val interval = when(abs(currentTime - StoreKeyData.getLong(StoreKeyData.BI_GET_USER_TIME))/ 1000 / 60 ){
                    in 0 .. 2 -> { 10 }
                    in 2 .. 10 -> { 5 * 60 }
                    in 10 .. 20 -> { 10 * 60 }
                    in 20 .. 60 -> { 30 * 60 }
                    in 60 .. 120 -> { 40 * 60 }
                    else -> {60 * 60}
                }
                debug(TAG,"get remote interval $interval ${abs(currentTime - StoreKeyData.getLong(StoreKeyData.BI_CONFIG_FETCH_LAST))}")
                if (abs(currentTime - StoreKeyData.getLong(StoreKeyData.BI_CONFIG_FETCH_LAST)) >= interval * 1000){
                    BiServiceClass().postRemoteConfig(
                        success = {
                            StoreKeyData.setLong(StoreKeyData.BI_CONFIG_FETCH_LAST, System.currentTimeMillis())
                            debug(TAG,"RemoteConfigClass Success \n $it")
                            FirebaseEventPost.event("get_remote_config")
                            try {
                                if (it != null) {
                                    formatRemoteData(it)
                                    FirebaseEventPost.event("dispatch_config")
                                    debug(TAG,"dispatch_config")
                                }
                            } catch (e: Exception) {
                                getConfigFromFirebase()
                                FirebaseEventPost.event("dispatch_config_fail")
                                debug(TAG,"dispatch_config_fail")
                            }
                        },
                        fail = {
                            getConfigFromFirebase()
                            debug(TAG,"get_remote_fail")
                        })
                }else{
                    getConfigFromFirebase()
                }
            }else{
                debug(TAG,"get remote by firebase")
                getConfigFromFirebase()
            }
        }
    }

    fun findRemoteConfigByStart(){
        val endTime = System.currentTimeMillis() + 2 * 60 * 1000L
        GlobalScope.launch(Dispatchers.IO) {
            while (System.currentTimeMillis() < endTime) {
                delay(2000L)
                if (System.currentTimeMillis() < endTime) {
                    getRemoteConfig()
                }
            }
        }
    }

    private fun getConfigFromFirebase(){
        val cacheJson = StoreKeyData.getString(StoreKeyData.BI_REMOTE_CONFIG_STR)
        if (cacheJson.isNullOrBlank()){
            return
            debug(TAG,"find remote config from firebase")
            FirebaseEventPost.event("get_fb_config")
            notifyParamData = InstanceTool.getRemoteNotifyParam()
            notifyContentData = InstanceTool.getRemoteContent()
            adConfigData = InstanceTool.getRemoteAdData()
            if (!adConfigData?.sdk_key.isNullOrEmpty()){
                GlobalScope.launch {
                    AdConfig.initialMaxSDK(BaseApplication.mAppContext)
                }
            }
        }else{
            try {
                debug(TAG,"get remote config from cache")
                FirebaseEventPost.event("get_cache_config")
                formatRemoteData(cacheJson)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    private fun formatRemoteData(json: String){
        val data = Gson().fromJson(json, RemoteConfigData::class.java)
        if (data != null){
            data.config_notify_content?.apply {
                try {
                    val decodeJson = EncodeTool.baseDecrypt(EncodeTool.CONFIG_KEY,this)
                    val jsonType = object : TypeToken<MutableList<NotificationData>>() {}.type
                    notifyContentData = Gson().fromJson(decodeJson, jsonType)
                    debug(TAG,"format config_notify_content: $notifyContentData")
                    FirebaseEventPost.event("remote_content_dispatch")
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
            data.config_notify_param?.apply {
                try {
                    val decodeJson = EncodeTool.baseDecrypt(EncodeTool.CONFIG_KEY,this)
                    notifyParamData = Gson().fromJson(decodeJson, RemoteNotifyData::class.java)
                    debug(TAG,"format notifyParamData: $notifyParamData")
                    FirebaseEventPost.event("remote_notify_dispatch")
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
            data.config_ad?.apply {
                try {
                    val decodeJson = EncodeTool.baseDecrypt(EncodeTool.CONFIG_KEY,this)
                    val typeToken = object : TypeToken<RemoteAdData>() {}.type
                    adConfigData = Gson().fromJson(decodeJson, typeToken)
                    debug(TAG,"format adConfigData: $adConfigData")
                    FirebaseEventPost.event("remote_ad_dispatch")
                    GlobalScope.launch {
                        AdConfig.initialMaxSDK(BaseApplication.mAppContext)
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
            StoreKeyData.setString(StoreKeyData.BI_REMOTE_CONFIG_STR, json)
        }
    }

    private fun getRemoteConfigData(): RemoteNotifyData {
        getConfigFromFirebase()
        debug(TAG,"notifyDta: $notifyParamData")
        return if (notifyParamData != null) {
            notifyParamData!!
        }else{
            FirebaseEventPost.event("get_local_config_noty")
            RemoteNotifyData()
        }
    }

    private fun getRemoteContentData(context: Context): MutableList<NotificationData> {
        getConfigFromFirebase()
        debug(TAG,"contentData: $notifyContentData")
        return if (notifyContentData != null) {
            notifyContentData!!
        }else{
            FirebaseEventPost.event("get_local_config_content")
            NotificationData.getNotificationList(context)
        }
    }

    fun getRemoteAdConfigData(): RemoteAdData{
        getConfigFromFirebase()
        debug(TAG,"adConfigData: $adConfigData")
        return if (adConfigData != null){
            adConfigData!!
        }else{
            FirebaseEventPost.event("get_local_config_ad")
            RemoteAdData()
        }
    }

    fun getAdParam(type: String): AdParamData?{
        var adData: AdParamData? = null
        try {
            val params = getRemoteAdConfigData().type_param
            adData = params.find { it.ad_type == type }
        }catch (e: Exception){
            e.printStackTrace()
        }
        return adData
    }

    fun notifyFirstShowInterval() = getRemoteConfigData().first_show_interval

    fun notifyNormalShowInterval() = getRemoteConfigData().normal_show_interval

    fun notifyContentList(context: Context) = getRemoteContentData(context)

    fun notifyShowMax() = getRemoteConfigData().post_show_max

    fun notifyShotTimes() = getRemoteConfigData().shot_times

    fun notifySlideTime() = getRemoteConfigData().slide_times

    fun notifyCloseTime() = getRemoteConfigData().close_times




}