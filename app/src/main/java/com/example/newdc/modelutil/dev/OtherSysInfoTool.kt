package com.example.newdc.modelutil.dev

import android.content.Context
import android.media.MediaDrm
import android.os.Build
import android.os.SystemClock
import android.text.format.Formatter
import com.example.newdc.R
import com.example.newdc.modelutil.tool.ExtraTool.getSysProFromString
import com.example.newdc.modelutil.tool.FormatTool
import java.io.*
import java.util.*
import java.util.concurrent.TimeUnit

object OtherSysInfoTool {

    fun findJvmInfo(context: Context): JvmInfoData {
        val jRuntime = (System.getProperty("java.runtime.name") ?: "") + " " + System.getProperty("java.runtime.version")
        val jvm = findJvm()
        val jStackSize = findJvmStackSize(context)
        val kernelArch = System.getProperty("os.arch") ?: ""
        val kernelVersion = System.getProperty("os.version") ?: ""
        val openGLES = ScreenTool.findOpenGlVersion(context)
        val seLinuxStatus = findSELinuxStatus().isNullOrBlank().let { context.getString(R.string.base_un_determine) }
        val openSSLVersion = System.getProperty("android.openssl.version") ?: ""

        return JvmInfoData(
            jRuntime = jRuntime,jvm = jvm,jStackSize = jStackSize,kernelArch = kernelArch,kernelVersion = kernelVersion,
            openGLES = openGLES,seLinuxStatus = seLinuxStatus, openSSLVersion = openSSLVersion,
        )
    }

    fun findDRMInfo(): DRMInfoData?{
        try {
            val drm = MediaDrm(UUID( -1301668207276963122L,-6645017420763422227L))

            val vendor = drm.getPropertyString(MediaDrm.PROPERTY_VENDOR)

            val version = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                drm.getPropertyString(MediaDrm.PROPERTY_VERSION)
            } else {
                "N/A"
            }

            val description = drm.getPropertyString(MediaDrm.PROPERTY_DESCRIPTION)

            val algorithms = drm.getPropertyString(MediaDrm.PROPERTY_ALGORITHMS)

            val securityLevel = drm.getPropertyString("securityLevel")

            val systemId = drm.getPropertyString("systemId")

            val hdcpLevel = drm.getPropertyString("hdcpLevel")

            val maxHdcpLevel = drm.getPropertyString("maxHdcpLevel")

            val usageReportingSupport = drm.getPropertyString("usageReportingSupport")

            val maxNumberOfSessions = drm.getPropertyString("maxNumberOfSessions")

            val numberOfOpenSessions = drm.getPropertyString("numberOfOpenSessions")

            return DRMInfoData(
                vendor = vendor,version = version, description = description, algorithm = algorithms, securityLevel = securityLevel,
                systemId = systemId,hdcpLevel = hdcpLevel, maxHdcpLevel = maxHdcpLevel, usageReportingSupport = usageReportingSupport,
                maxNumberOfSessions = maxNumberOfSessions,numberOfOpenSessions = numberOfOpenSessions
            )
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return null
    }

    fun findSysInfo(context: Context): SysInfoData{
        val buildNumber = Build.DISPLAY
        val buildTime = FormatTool.ymdTimeFormat(Build.TIME)
        val buildId = Build.ID
        val patchLevel = findPatchLevel()
        val baseHand = Build.getRadioVersion()
        val lang = findLocalLanguage()
        val timeZone = findLocalTimeZone()
        val root = if (rootedGranted()) context.getString(R.string.base_yes) else context.getString(R.string.base_no)
        val boosterTime = getBoosterTime()
        val sysRoot = if (systemRoot()) context.getString(R.string.base_supported) else context.getString(R.string.base_no_supported)
        val seamlessUpdates = if (seamlessUpdatesSupported()) context.getString(R.string.base_supported) else context.getString(R.string.base_no_supported)
        val dyPartitions = if (dynamicPartitionsSupported()) context.getString(R.string.base_supported) else context.getString(R.string.base_no_supported)
        val projectTreble = trebleSupported(context)
        val releaseType = isReleaseType(context)

        return SysInfoData(
            buildNumber = buildNumber,buildTime = buildTime,buildId = buildId, patchLevel = patchLevel,baseBand = baseHand,
            localLanguage = lang,timeZone = timeZone,root = root, boosterTime = boosterTime, sysRoot = sysRoot,
            seamless = seamlessUpdates, dynamicPartitions = dyPartitions, treble = projectTreble,releaseType = releaseType
        )
    }

    fun findVersionInfo(): VersionData{
        val api = Build.VERSION.SDK_INT
        val versionCode = "ANDROID ${androidVerCode(api)}"
        val versionName = "Android ${androidVerName(api)}"
        val releaseTime = androidReleaseTime(api)

        return VersionData(api, versionName, versionCode, releaseTime)
    }

    private fun isReleaseType(context: Context): String {
        val releaseType = "ro.build.type".getSysProFromString()
        return if (releaseType == "user") context.getString(R.string.info_release) else context.getString(R.string.info_debug)
    }

    private fun trebleSupported(context: Context): String {
        val trebleEnabledProp = "ro.treble.enabled".getSysProFromString()
        val vndkVersion = "ro.vndk.version".getSysProFromString()

        val isTrebleSupported = trebleEnabledProp == "true"

        return if (isTrebleSupported) {
            if (vndkVersion.isNotBlank()) "${context.getString(R.string.info_release)}(VNDK $vndkVersion)" else (context.getString(R.string.base_supported))
        }else context.getString(R.string.base_no_supported)
    }

    fun getBoosterTime(): String{
        val statTime = SystemClock.elapsedRealtime()
        val h = TimeUnit.MILLISECONDS.toHours(statTime)
        val m = TimeUnit.MILLISECONDS.toMinutes(statTime) % 60
        val s = TimeUnit.MILLISECONDS.toSeconds(statTime) % 60
        return String.format("%02d:%02d:%02d", h, m, s)
    }

    private fun findLocalLanguage(): String{
        val locale = Locale.getDefault()
        return "${locale.displayLanguage} (${locale.displayCountry})"
    }

    private fun findLocalTimeZone(): String{
        val timeZone = TimeZone.getDefault()
        val offset = timeZone.rawOffset
        val offsetHours = offset / (1000 * 60 * 60)
        val offsetMinutes = (offset / (1000 * 60)) % 60
        val offsetString = String.format("%+03d:%02d", offsetHours, offsetMinutes)
        return "${timeZone.displayName} (GMT$offsetString)"
    }

    private fun seamlessUpdatesSupported(): Boolean {
        val abUpdateProp = "ro.build.ab_update".getSysProFromString()
        val slotSuffixProp = "ro.boot.slot_suffix".getSysProFromString()

        return abUpdateProp == "true" && slotSuffixProp.isNotEmpty()
    }

    private fun dynamicPartitionsSupported(): Boolean {
        return "ro.boot.dynamic_partitions".getSysProFromString() == "true"
    }

   private fun systemRoot(): Boolean {
        val buildPropFile = File("/system/build.prop")
        if (buildPropFile.exists()) {
            try {
                val reader = BufferedReader(FileReader(buildPropFile))
                var line: String?
                while (reader.readLine().also { line = it } != null) {
                    if (line?.startsWith("ro.build.system_root_image") == true) {
                        return true
                    }
                }
            } catch (e: IOException) {
                e.printStackTrace()
            }
        }
        return false
    }

    private fun rootedGranted(): Boolean{
        val buildTags = Build.TAGS
        if (buildTags != null && buildTags.contains("test-keys")) {
            return true
        }
        try {
            val file = File("/system/app/Superuser.apk")
            if (file.exists()) {
                return true
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return false
    }

    private fun findPatchLevel(): String{
        var level = ""
        if (DevInfoTool.hwDevice()){
            level = "ro.huawei.build.version.security_patch".getSysProFromString()
        }
        if (level.isBlank()) {
            level = Build.VERSION.SECURITY_PATCH
        }
        return level
    }

    private fun findJvm(): String {
        var result: String? = null
        result = System.getProperty("java.vm.name")
        if (result != null) {
            result += System.getProperty("java.vm.version")
        }
        return result ?: ""
    }

    private fun findJvmStackSize(context: Context): String {
        val size =  Runtime.getRuntime().maxMemory()
        return Formatter.formatFileSize(context,size)
    }

    private fun findSELinuxStatus(): String? {
        var status: String? = null
        try {
            val process = Runtime.getRuntime().exec("getenforce")
            val inputStream = process.inputStream
            val reader = BufferedReader(InputStreamReader(inputStream))
            status = reader.readLine()
            reader.close()
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return status
    }

    fun androidVerName(versionCode: Int): String {
        return when (versionCode) {
            1 -> "Android 1"
            2 -> "Petite Four"
            3 -> "Cupcake"
            4 -> "Donut"
            5 -> "Eclair"
            6 -> "Eclair 0_1"
            7 -> "Eclair MR1"
            8 -> "Froyo"
            9 -> "Gingerbread"
            10 -> "Gingerbread MR1"
            11 -> "Honeycomb"
            12 -> "Honeycomb MR1"
            13 -> "Honeycomb MR2"
            14 -> "Ice Cream Sandwich"
            15 -> "Ice Cream Sandwich MR1"
            16 -> "Jelly Bean"
            17 -> "Jelly Bean MR1"
            18 -> "Jelly Bean MR2"
            19 -> "KitKat"
            20 -> "KitKat WATCH"
            21 -> "LOLLIPOP"
            22 -> "LOLLIPOP_MR1"
            23 -> "M"
            24 -> "N"
            25 -> "N_MR1"
            26 -> "O"
            27 -> "O_MR1"
            28 -> "P"
            29 -> "Q"
            30 -> "R"
            31 -> "SNOW CONE"
            32 -> "S_V2"
            33 -> "TIRAMISU"
            34 -> "UPSIDE_DOWN_CAKE"
            else -> ""
        }
    }

    fun androidVerCode(versionCode: Int): String {
        return when (versionCode) {
            1 -> "1"
            2 -> "1.1"
            3 -> "1.5"
            4 -> "1.6"
            5 -> "2.0"
            6 -> "2.0"
            7 -> "2.1"
            8 -> "2.2"
            9 -> "2.3"
            10 -> "2.3"
            11 -> "3.0"
            12 -> "3.1"
            13 -> "3.2"
            14 -> "4.0"
            15 -> "4.0"
            16 -> "4.1"
            17 -> "4.2"
            18 -> "4.3"
            19 -> "4.4"
            20 -> "4.4W"
            21 -> "5.0"
            22 -> "5.1"
            23 -> "6.0"
            24 -> "7.0"
            25 -> "7.1"
            26 -> "8.0"
            27 -> "8.1"
            28 -> "9"
            29 -> "10"
            30 -> "11"
            31, 32 -> "12"
            33 -> "13"
            34 -> "14"
            else -> ""
        }
    }

    fun androidReleaseTime(versionCode: Int): String{
        return when (versionCode) {
            21 -> "Nov 12, 2014"
            22 -> "Mar 9, 2015"
            23 -> "Oct 5, 2015"
            24 -> "Aug 22, 2016"
            25 -> "Oct 4, 2016"
            26 -> "Aug 21, 2017"
            27 -> "Dec 5, 2017"
            28 -> "Aug 6, 2018"
            29 -> "Sep 3, 2019"
            30 -> "Sep 8, 2020"
            31 -> "Oct 4, 2021"
            32 -> "Mar 7, 2022"
            33 -> "Aug 15, 2022"
            34 -> "Oct 4, 2023"
            else -> ""
        }
    }
}

data class JvmInfoData(
    val jRuntime: String = "",
    val jvm: String = "",
    val jStackSize: String = "",
    val kernelArch: String = "",
    val kernelVersion: String = "",
    val openGLES: String = "",
    val seLinuxStatus: String = "",
    val openSSLVersion: String = ""
)

data class DRMInfoData(
    val vendor: String = "",
    val version: String = "",
    val description: String = "",
    val algorithm: String = "",
    val securityLevel: String = "",
    val systemId: String = "",
    val hdcpLevel: String = "",
    val maxHdcpLevel: String = "",
    val usageReportingSupport: String = "",
    val maxNumberOfSessions: String = "",
    val numberOfOpenSessions: String = ""
)

data class SysInfoData(
    val buildNumber: String = "",
    val buildTime: String = "",
    val buildId: String = "",
    val patchLevel: String = "",
    val baseBand: String = "",
    val localLanguage: String = "",
    val timeZone: String = "",
    val root: String = "",
    val boosterTime: String = "",
    val sysRoot: String = "",
    val seamless: String = "",
    val dynamicPartitions: String = "",
    val treble: String = "",
    val releaseType: String = ""
)

data class VersionData(
    val api: Int,
    val versionName: String = "",
    val versionCode: String = "",
    val releaseTime: String = ""
)