package com.example.newdc.modelutil.tool

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Environment
import android.os.PowerManager
import android.provider.Settings
import androidx.annotation.RequiresApi
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.fragment.app.FragmentActivity
import androidx.fragment.app.FragmentManager
import com.additional.firebase.FirebaseEventPost
import com.example.newdc.BaseApplication
import com.example.newdc.BaseLifeCycle
import com.example.newdc.content.activity.StarterActivity
import com.example.newdc.content.view.dialog.RequestDialog
import com.example.newdc.modelutil.data.StoreKeyData
import kotlin.math.abs

object PermissionTool {
    const val PERMISSION_STORAGE_MANAGER_CODE = 301
    const val PERMISSION_POST_NOTIFY_CODE = 302
    const val PERMISSION_OPTIMIZATION_CODE = 303

    private const val BATTERY_OPTIMIZATION_INTERVAL = 12 * 60 * 60 * 1000L
    private const val BATTERY_OPTIMIZATION_MAX_COUNT = 5
    private const val POST_NOTIFY_MAX_COUNT = 10


    fun checkPermissionGranted(context: Context, permission: String): Boolean {
        return ContextCompat.checkSelfPermission(context, permission) == PackageManager.PERMISSION_GRANTED
    }

    fun allowStoragePermission(context: Context): Boolean{
        return Build.VERSION.SDK_INT >= Build.VERSION_CODES.R && Environment.isExternalStorageManager()
                || checkPermissionGranted(context,Manifest.permission.READ_EXTERNAL_STORAGE)
    }

    fun allowBatteryOptimization(activity: Activity): Boolean{
        val powerManager = activity.getSystemService(Context.POWER_SERVICE) as PowerManager
        return powerManager.isIgnoringBatteryOptimizations(activity.packageName)
    }

    fun checkPostNotifyPermission(context: Context): Boolean {
        return ContextCompat.checkSelfPermission(context, Manifest.permission.POST_NOTIFICATIONS) == PackageManager.PERMISSION_GRANTED
    }

    fun permissionGrantStorage(activity: Activity){
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R){
            val intent = Intent(Settings.ACTION_MANAGE_APP_ALL_FILES_ACCESS_PERMISSION)
            try {
                intent.data = Uri.parse("package:" + activity.packageName)
                if (intent.resolveActivity(activity.packageManager) != null) {
                    activity.startActivityForResult(intent, PERMISSION_STORAGE_MANAGER_CODE)
                    BaseLifeCycle.splashFlag = true
                } else {
                    activity.startActivityForResult(
                        Intent(Settings.ACTION_MANAGE_ALL_FILES_ACCESS_PERMISSION),
                        PERMISSION_STORAGE_MANAGER_CODE
                    )
                    BaseLifeCycle.splashFlag = true
                }
            } catch (e: Exception) {
                try {
                    activity.startActivityForResult(Intent(Settings.ACTION_MANAGE_ALL_FILES_ACCESS_PERMISSION), PERMISSION_STORAGE_MANAGER_CODE)
                    BaseLifeCycle.splashFlag = true
                } catch (ex: Exception) {
                    ex.printStackTrace()
                }
            }
        }else{
            ActivityCompat.requestPermissions(activity, arrayOf(Manifest.permission.WRITE_EXTERNAL_STORAGE), PERMISSION_STORAGE_MANAGER_CODE)
        }
    }

    fun requestBatteryOptimization(activity: Activity){
        val intent = Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS)
        intent.data = Uri.parse("package:" + activity.packageName)
        try {
            activity.startActivityForResult(intent, PERMISSION_OPTIMIZATION_CODE)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun allowBatteryOptimizationRequest(fsm: FragmentManager,activity: Activity): Boolean{
        if (!allowBatteryOptimization(activity)) {
            var showCount = StoreKeyData.getInt(StoreKeyData.PERMISSION_BATTERY_OPT_COUNT)
            val interval = StoreKeyData.getLong(StoreKeyData.PERMISSION_BATTERY_OPT_LAST)
            if (showCount < BATTERY_OPTIMIZATION_MAX_COUNT && abs(System.currentTimeMillis() - interval) >= BATTERY_OPTIMIZATION_INTERVAL) {
                RequestDialog(RequestDialog.TYPE_BATTERY,{
                    requestBatteryOptimization(activity)
                }).show(fsm,"")
                StoreKeyData.setInt(StoreKeyData.PERMISSION_BATTERY_OPT_COUNT, ++showCount)
                StoreKeyData.setLong(StoreKeyData.PERMISSION_BATTERY_OPT_LAST,System.currentTimeMillis())
                FirebaseEventPost.event("per_opt_request")
                return true
            }
        }
        return false
    }

    @RequiresApi(Build.VERSION_CODES.TIRAMISU)
    fun allowPostNotifyRequest(showCount: Int): Boolean{
        if (checkPostNotifyPermission(BaseApplication.mAppContext) || showCount >= POST_NOTIFY_MAX_COUNT) return false
        val last = StoreKeyData.getLong(StoreKeyData.PERMISSION_NOTIFY_LAST)
        val interval = if (showCount == 0){
            0
        }else if (showCount == 1){
            2
        } else if (showCount >= 2 && abs(System.currentTimeMillis() - StoreKeyData.getLong(StoreKeyData.BASE_FIRST_ENTER_TIME)) < 24 * 60 * 60 * 1000) {
            60
        }else{
            12 * 60
        }
        return abs(System.currentTimeMillis() - last) > interval * 1000 * 60
    }

    @Synchronized
    @RequiresApi(Build.VERSION_CODES.TIRAMISU)
    fun requestNotifyPost(activity: FragmentActivity){
        val permission = Manifest.permission.POST_NOTIFICATIONS
        var count = StoreKeyData.getInt(StoreKeyData.PERMISSION_NOTIFY_COUNT)
        if (allowPostNotifyRequest(count)){
            if (!StoreKeyData.getBoolean(StoreKeyData.PERMISSION_NOTIFY_FIRST)){
                ActivityCompat.requestPermissions(activity, arrayOf(permission), PERMISSION_POST_NOTIFY_CODE)
                StoreKeyData.apply {
                    setBoolean(PERMISSION_NOTIFY_FIRST,true)
                    setLong(PERMISSION_NOTIFY_LAST,System.currentTimeMillis())
                    setInt(PERMISSION_NOTIFY_COUNT,++count)
                }
                FirebaseEventPost.event("per_notify_request")
            }else{
                if (activity !is StarterActivity){
                    try{
                        RequestDialog(RequestDialog.TYPE_NOTIFY,{
                            if (ActivityCompat.shouldShowRequestPermissionRationale(activity, permission)){
                                ActivityCompat.requestPermissions(activity, arrayOf(permission), PERMISSION_POST_NOTIFY_CODE)
                            }else{
                                postNotifySetting(activity)
                            }
                        }).show(activity.supportFragmentManager,"notify")
                        StoreKeyData.apply {
                            setLong(PERMISSION_NOTIFY_LAST,System.currentTimeMillis())
                            setInt(PERMISSION_NOTIFY_COUNT,++count)
                        }
                        FirebaseEventPost.event("per_notify_request")
                    }catch (e: Exception){
                        e.printStackTrace()
                    }
                }
            }
        }
    }


    fun postNotifySetting(activity: FragmentActivity){
            val intent = Intent("android.settings.APP_NOTIFICATION_SETTINGS")
            intent.putExtra("android.provider.extra.APP_PACKAGE", activity.packageName)
            try {
                activity.startActivityForResult(intent, PERMISSION_POST_NOTIFY_CODE)
                BaseLifeCycle.splashFlag = true
            } catch (e: Exception) {
                val sIntent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
                sIntent.setData(Uri.fromParts("package", activity.packageName, null))
                try {
                    activity.startActivity(sIntent)
                    BaseLifeCycle.splashFlag = true
                } catch (ex: Exception) {
                    ex.printStackTrace()
                }
            }
    }

}