package com.example.newdc.modelutil.data

import android.content.Context
import android.os.Parcelable
import com.example.newdc.BaseApplication
import com.example.newdc.R
import com.example.newdc.service.notify.NotifyConfig
import kotlinx.parcelize.Parcelize

@Parcelize
data class NotificationData(
    val id: Int,
    val type: String,
    val title: String,
    val info: String,
    val enterCode: Int
): Parcelable{
    companion object{
        fun getNotificationList(context: Context):MutableList<NotificationData>{
            return mutableListOf<NotificationData>().apply {
                add(NotificationData(1101,NotifyConfig.TYPE_NOTY_BATTERY,context.getString(R.string.noty_title_battery),context.getString(R.string.noty_sub_battery),1))
                add(NotificationData(1102,NotifyConfig.TYPE_NOTY_STORAGE,context.getString(R.string.noty_title_storage),context.getString(R.string.noty_sub_storage),1))
                add(NotificationData(1103,NotifyConfig.TYPE_NOTY_RAM,context.getString(R.string.noty_title_ram),context.getString(R.string.noty_sub_ram),1))
                add(NotificationData(1104,NotifyConfig.TYPE_NOTY_INFO,context.getString(R.string.noty_title_info),context.getString(R.string.noty_sub_info),1))
            }
        }

        val TYPE_BATTERY = NotificationData(1101,NotifyConfig.TYPE_NOTY_BATTERY,
            BaseApplication.mAppContext.getString(R.string.noty_title_battery),BaseApplication.mAppContext.getString(R.string.noty_sub_battery),1)
        val TYPE_STORAGE = NotificationData(1102,NotifyConfig.TYPE_NOTY_STORAGE,BaseApplication.mAppContext.getString(R.string.noty_title_storage),BaseApplication.mAppContext.getString(R.string.noty_sub_storage),1)
        val TYPE_RAM = NotificationData(1103,NotifyConfig.TYPE_NOTY_RAM,BaseApplication.mAppContext.getString(R.string.noty_title_ram),BaseApplication.mAppContext.getString(R.string.noty_sub_ram),1)

    }
}
