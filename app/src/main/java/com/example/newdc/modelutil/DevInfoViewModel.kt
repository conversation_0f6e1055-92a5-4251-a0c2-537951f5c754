package com.example.newdc.modelutil

import android.content.Context
import android.net.wifi.WifiManager
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.newdc.modelutil.dev.*
import kotlinx.coroutines.launch

class DevInfoViewModel: ViewModel() {
    // device info livedata
    private val _info_dev_brand = MutableLiveData<String>()
    private val _info_dev_deviceName = MutableLiveData<String>()
    private val _info_dev_model = MutableLiveData<String>()
    private val _info_dev_manu = MutableLiveData<String>()
    private val _info_dev_device = MutableLiveData<String>()
    private val _info_dev_board = MutableLiveData<String>()
    private val _info_dev_hardware = MutableLiveData<String>()

    private val _info_dev_deviceID = MutableLiveData<String>()
    private val _info_dev_serial = MutableLiveData<String>()
    private val _info_dev_fingerprint = MutableLiveData<String>()
    private val _info_dev_mac_add = MutableLiveData<String>()
    private val _info_dev_blue_add = MutableLiveData<String>()
    private val _info_dev_debugging = MutableLiveData<String>()

    val getDevBrand : LiveData<String> = _info_dev_brand
    val getDevDeviceName : LiveData<String> = _info_dev_deviceName
    val getDevDeviceModel : LiveData<String> = _info_dev_model
    val getDevManufacturer : LiveData<String> = _info_dev_manu
    val getDevDevice: LiveData<String> = _info_dev_device
    val getDevBoard : LiveData<String> = _info_dev_board
    val getDevHardware : LiveData<String> = _info_dev_hardware
    val getDevDeviceID : LiveData<String> = _info_dev_deviceID
    val getDevSerial : LiveData<String> = _info_dev_serial
    val getDevFingerprint : LiveData<String> = _info_dev_fingerprint
    val getDevMacAdd : LiveData<String> = _info_dev_mac_add
    val getDevBlueAdd : LiveData<String> = _info_dev_blue_add
    val getDevDebugging : LiveData<String> = _info_dev_debugging

    fun getDeviceInfoData(context: Context){
        viewModelScope.launch {
            val devInfo = DevInfoTool.findDevInfo()
            _info_dev_deviceName.postValue(devInfo.name)
            _info_dev_brand.postValue(devInfo.brand)
            _info_dev_model.postValue(devInfo.model)
            _info_dev_manu.postValue(devInfo.manual)
            _info_dev_device.postValue(devInfo.device)
            _info_dev_hardware.postValue(devInfo.hardware)
            _info_dev_board.postValue(devInfo.board)

            val serInfo = DevInfoTool.findDevSerInfo(context)
            _info_dev_deviceID.postValue(serInfo.deviceId)
            _info_dev_serial.postValue(serInfo.hardSerial)
            _info_dev_fingerprint.postValue(serInfo.buildFinger)
            _info_dev_mac_add.postValue(serInfo.wifiMac)
            _info_dev_blue_add.postValue(serInfo.blueToothMac)
            _info_dev_debugging.postValue(serInfo.usbDebug)
        }
    }

    // system info livedata
    private val _info_sys_code = MutableLiveData<String>()
    private val _info_sys_code_name  = MutableLiveData<String>()
    private val _info_sys_api = MutableLiveData<Int>()
    private val _info_sys_data  = MutableLiveData<String>()
    private val _info_sys_build_number  = MutableLiveData<String>()
    private val _info_sys_build_time = MutableLiveData<String>()
    private val _info_sys_buildId  = MutableLiveData<String>()
    private val _info_sys_patch  = MutableLiveData<String>()
    private val _info_sys_baseband  = MutableLiveData<String>()
    private val _info_sys_lang = MutableLiveData<String>()
    private val _info_sys_zone  = MutableLiveData<String>()
    private val _info_sys_root  = MutableLiveData<String>()
    private val _info_sys_uptime  = MutableLiveData<String>()
    private val _info_sys_as_root = MutableLiveData<String>()
    private val _info_sys_seamless  = MutableLiveData<String>()
    private val _info_sys_dynamic  = MutableLiveData<String>()
    private val _info_sys_treble  = MutableLiveData<String>()

    fun getSysInfoData(context: Context){
        viewModelScope.launch {
            val verInfo = OtherSysInfoTool.findVersionInfo()
            _info_sys_code.postValue(verInfo.versionCode)
            _info_sys_code_name.postValue(verInfo.versionName)
            _info_sys_api.postValue(verInfo.api)
            _info_sys_data.postValue(verInfo.releaseTime)

            OtherSysInfoTool.findSysInfo(context).apply {
                _info_sys_build_number.postValue(buildNumber)
                _info_sys_build_time.postValue(buildTime)
                _info_sys_buildId.postValue(buildId)
                _info_sys_patch.postValue(patchLevel)
                _info_sys_baseband.postValue(baseBand)
                _info_sys_lang.postValue(localLanguage)
                _info_sys_zone.postValue(timeZone)
                _info_sys_uptime.postValue(boosterTime)
                _info_sys_root.postValue(root)
                _info_sys_as_root.postValue(sysRoot)
                _info_sys_seamless.postValue(seamless)
                _info_sys_dynamic.postValue(dynamicPartitions)
                _info_sys_treble.postValue(treble)
            }

        }
    }

    fun getRunningTime(){
        viewModelScope.launch {
            val time = OtherSysInfoTool.getBoosterTime()
            _info_sys_uptime.postValue(time)
        }
    }

    private val _info_sys_java_runtime = MutableLiveData<String>()
    private val _info_sys_jvm  = MutableLiveData<String>()
    private val _info_sys_jvm_stack  = MutableLiveData<String>()
    private val _info_sys_kernel  = MutableLiveData<String>()
    private val _info_sys_kernel_ver = MutableLiveData<String>()
    private val _info_sys_open_es  = MutableLiveData<String>()
    private val _info_sys_sel  = MutableLiveData<String>()
    private val _info_sys_open_ssl  = MutableLiveData<String>()

    fun getSysJvmInfoData(context: Context){
        viewModelScope.launch {
            OtherSysInfoTool.findJvmInfo(context).apply {
                _info_sys_java_runtime.postValue(jRuntime)
                _info_sys_jvm.postValue(jvm)
                _info_sys_jvm_stack.postValue(jStackSize)
                _info_sys_kernel.postValue(kernelArch)
                _info_sys_kernel_ver.postValue(kernelVersion)
                _info_sys_open_es.postValue(openGLES)
                _info_sys_sel.postValue(seLinuxStatus)
                _info_sys_open_ssl.postValue(openSSLVersion)
            }
        }
    }

    private val _info_sys_drm_vendor = MutableLiveData<String>()
    private val _info_sys_drm_ver  = MutableLiveData<String>()
    private val _info_sys_des  = MutableLiveData<String>()
    private val _info_sys_algor  = MutableLiveData<String>()
    private val _info_sys_security = MutableLiveData<String>()
    private val _info_sys_sysId  = MutableLiveData<String>()
    private val _info_sys_hdcp  = MutableLiveData<String>()
    private val _info_sys_max_hdcp  = MutableLiveData<String>()
    private val _info_sys_reporting  = MutableLiveData<String>()
    private val _info_sys_max_session  = MutableLiveData<String>()
    private val _info_sys_open_session  = MutableLiveData<String>()

    fun getSysDrmInfoData(context: Context){
        OtherSysInfoTool.findDRMInfo()?.apply {
            _info_sys_drm_vendor.postValue(vendor)
            _info_sys_drm_ver.postValue(version)
            _info_sys_des.postValue(description)
            _info_sys_algor.postValue(algorithm)
            _info_sys_security.postValue(securityLevel)
            _info_sys_sysId.postValue(systemId)
            _info_sys_hdcp.postValue(hdcpLevel)
            _info_sys_max_hdcp.postValue(maxHdcpLevel)
            _info_sys_reporting.postValue(usageReportingSupport)
            _info_sys_max_session.postValue(maxNumberOfSessions)
            _info_sys_open_session.postValue(numberOfOpenSessions)
        }
    }

    val getSysCode : LiveData<String> = _info_sys_code
    val getSysCodeName : LiveData<String> = _info_sys_code_name
    val getSysApi : LiveData<Int> = _info_sys_api
    val getReleaseData : LiveData<String> = _info_sys_data
    val getSysBuildNumber : LiveData<String> = _info_sys_build_number
    val getSysBuildTime : LiveData<String> = _info_sys_build_time
    val getSysBuildId : LiveData<String> = _info_sys_buildId
    val getSysPatch : LiveData<String> = _info_sys_patch
    val getSysBaseBand : LiveData<String> = _info_sys_baseband
    val getSysLang : LiveData<String> = _info_sys_lang
    val getSysZone : LiveData<String> = _info_sys_zone
    val getSysRoot : LiveData<String> = _info_sys_root
    val getSysUpTime : LiveData<String> = _info_sys_uptime
    val getSysAsRoot : LiveData<String> = _info_sys_as_root
    val getSysSeamless : LiveData<String> = _info_sys_seamless
    val getSysDynamic : LiveData<String> = _info_sys_dynamic
    val getSysTreble : LiveData<String> = _info_sys_treble

    val getSysJavaRuntime : LiveData<String> = _info_sys_java_runtime
    val getSysJvm : LiveData<String> = _info_sys_jvm
    val getSysJvmStack : LiveData<String> = _info_sys_jvm_stack
    val getSysKernel : LiveData<String> = _info_sys_kernel
    val getSysKernelVer: LiveData<String> = _info_sys_kernel_ver
    val getSysOpenerEs : LiveData<String> = _info_sys_open_es
    val getSysSel : LiveData<String> = _info_sys_sel
    val getSysOpenSsl : LiveData<String> = _info_sys_open_ssl

    val getSysDrmVendor : LiveData<String> = _info_sys_drm_vendor
    val getSysDenVer : LiveData<String> = _info_sys_drm_ver
    val getSysDes : LiveData<String> = _info_sys_des
    val getSysAlgor : LiveData<String> = _info_sys_algor
    val getSysSecurity : LiveData<String> = _info_sys_security
    val getSysSysId : LiveData<String> = _info_sys_sysId
    val getSysHdcp : LiveData<String> = _info_sys_hdcp
    val getSysMaxHdcp : LiveData<String> = _info_sys_max_hdcp
    val getSysReporting : LiveData<String> = _info_sys_reporting
    val getSysMaxSession : LiveData<String> = _info_sys_max_session
    val getSysOpenSession : LiveData<String> = _info_sys_open_session

    // info display livedata
    private val _info_dis_name = MutableLiveData<String>()
    private val _info_dis_height = MutableLiveData<String>()
    private val _info_dis_width = MutableLiveData<String>()
    private val _info_dis_size = MutableLiveData<String>()
    private val _info_dis_phy_size = MutableLiveData<String>()
    private val _info_dis_orientation = MutableLiveData<String>()
    private val _info_dis_rate = MutableLiveData<String>()
    private val _info_dis_hdr = MutableLiveData<String>()
    private val _info_dis_brightness = MutableLiveData<String>()
    private val _info_dis_timeout = MutableLiveData<String>()

    private val _info_dis_bucket = MutableLiveData<String>()
    private val _info_dis_dpi = MutableLiveData<String>()
    private val _info_dis_xdpi = MutableLiveData<String>()
    private val _info_dis_ydpi = MutableLiveData<String>()
    private val _info_dis_logical = MutableLiveData<String>()
    private val _info_dis_scaled = MutableLiveData<String>()
    private val _info_dis_font = MutableLiveData<String>()

    fun getDisplayInfo(context: Context){
        val screenInfo =ScreenTool.findScreenInfo(context).apply {
            _info_dis_name.postValue(displayName)
            _info_dis_height.postValue(screenHeight)
            _info_dis_width.postValue(screenWidth)
            _info_dis_size.postValue(screenSize)
            _info_dis_phy_size.postValue(physicalSize)
            _info_dis_orientation.postValue(defaultOrientation)
            _info_dis_rate.postValue(refreshRate)
            _info_dis_hdr.postValue(isHDRSupported)
            _info_dis_brightness.postValue(brightnessMode)
            _info_dis_timeout.postValue(screenTimeout)
        }

        ScreenTool.findDensInfo(context).apply {
            _info_dis_bucket.postValue(displayBucket)
            _info_dis_dpi.postValue(displayDpi)
            _info_dis_xdpi.postValue(xdpi)
            _info_dis_ydpi.postValue(ydpi)
            _info_dis_logical.postValue(logicalDensity)
            _info_dis_scaled.postValue(scaledDensity)
            _info_dis_font.postValue(fontScale)
        }
    }

    val getDisName :LiveData<String> = _info_dis_name
    val getDisHeight :LiveData<String> = _info_dis_height
    val getDisWidth :LiveData<String> = _info_dis_width
    val getDisSize :LiveData<String> = _info_dis_size
    val getDisPhySize :LiveData<String> = _info_dis_phy_size
    val getDisOrientation :LiveData<String> = _info_dis_orientation
    val getDisRate :LiveData<String> = _info_dis_rate
    val getDisHdr :LiveData<String> = _info_dis_hdr
    val getDisBrightness :LiveData<String> = _info_dis_brightness
    val getDisTimeOut :LiveData<String> = _info_dis_timeout
    val getDisBucket : LiveData<String> = _info_dis_bucket
    val getDisDpi : LiveData<String> = _info_dis_dpi
    val getDisXdpi : LiveData<String> = _info_dis_xdpi
    val getDisYdpi : LiveData<String> = _info_dis_ydpi
    val getDisLogical : LiveData<String> = _info_dis_logical
    val getDisScaled : LiveData<String> = _info_dis_scaled
    val getDisFont :LiveData<String> = _info_dis_font

    //info network livedata
    private val _info_wifi_status = MutableLiveData<String>()
    private val _info_wifi_safety = MutableLiveData<String>()
    private val _info_wifi_bssid = MutableLiveData<String>()
    private val _info_wifi_dhcp = MutableLiveData<String>()
    private val _info_wifi_duration = MutableLiveData<String>()
    private val _info_wifi_gateway = MutableLiveData<String>()
    private val _info_wifi_netmask = MutableLiveData<String>()
    private val _info_wifi_dns1 = MutableLiveData<String>()
    private val _info_wifi_dns2 = MutableLiveData<String>()
    private val _info_wifi_ip = MutableLiveData<String>()
    private val _info_wifi_ipv6 = MutableLiveData<String>()
    private val _info_wifi_interface = MutableLiveData<String>()
    private val _info_wifi_dircet = MutableLiveData<String>()
    private val _info_wifi_5g = MutableLiveData<String>()
    private val _info_wifi_speed = MutableLiveData<String>()
    private val _info_wifi_frequency = MutableLiveData<String>()
    private val _info_wifi_signal = MutableLiveData<String>()
    private val _info_wifi_signalPer = MutableLiveData<String>()

    fun getWifiInfoData(context: Context){
        NetTool.findWifiInfo(context).apply {
            _info_wifi_status.postValue(connectStatus)
            _info_wifi_bssid.postValue(bssid)
            _info_wifi_dhcp.postValue(dhcpAddress)
            _info_wifi_duration.postValue(leaseDuration)
            _info_wifi_gateway.postValue(gateway)
            _info_wifi_netmask.postValue(netmask)
            _info_wifi_dns1.postValue(dns1)
            _info_wifi_dns2.postValue(dns2)
            _info_wifi_ip.postValue(ipv4)
            _info_wifi_ipv6.postValue(ipv6)
            _info_wifi_interface.postValue(ipInterface)
            _info_wifi_dircet.postValue(wifiDirect)
            _info_wifi_5g.postValue(support5G)
            _info_wifi_speed.postValue(linkSpeed)
            _info_wifi_frequency.postValue(frequency)
        }
    }

    fun getWifiSignal(context: Context){
        val wifimanager = context.applicationContext.getSystemService(Context.WIFI_SERVICE) as WifiManager
        val signal = NetTool.findWifiSignalStrength(wifimanager)
        _info_wifi_signal.postValue(signal.first)
        _info_wifi_signalPer.postValue(signal.second)


    }

    private val _info_mobile_status = MutableLiveData<String>()
    private val _info_mobile_Multi = MutableLiveData<String>()
    private val _info_mobile_apn = MutableLiveData<String>()
    private val _info_mobile_ip = MutableLiveData<String>()
    private val _info_mobile_ipv6 = MutableLiveData<String>()
    private val _info_mobile_interface = MutableLiveData<String>()

    fun getMobileInfoData(context: Context){
        NetTool.findSimInfo(context).apply {
            _info_mobile_status.postValue(connectStatus)
            _info_mobile_Multi.postValue(multiSim)
            _info_mobile_apn.postValue(APN)
            _info_mobile_ip.postValue(ipv4)
            _info_mobile_ipv6.postValue(ipv6)
            _info_mobile_interface.postValue(ipInterface)
        }
    }

    val getWifiStatus :LiveData<String> = _info_wifi_status
    val getWifiSafety :LiveData<String> = _info_wifi_safety
    val getWifiBassId :LiveData<String> = _info_wifi_bssid
    val getWifiDhcp :LiveData<String> = _info_wifi_dhcp
    val getWifiDuration :LiveData<String> = _info_wifi_duration
    val getWifiGateway :LiveData<String> = _info_wifi_gateway
    val getWifiNetmask :LiveData<String> = _info_wifi_netmask
    val getWifiDns1 :LiveData<String> = _info_wifi_dns1
    val getWifiDns2 :LiveData<String> = _info_wifi_dns2
    val getWifiIp :LiveData<String> = _info_wifi_ip
    val getWifiIpv6 :LiveData<String> = _info_wifi_ipv6
    val getWifiInterface :LiveData<String> = _info_wifi_interface
    val getWifiDirect :LiveData<String> = _info_wifi_dircet
    val getWifi5G :LiveData<String> = _info_wifi_5g
    val getWifiSpeed :LiveData<String> = _info_wifi_speed
    val getWifiFrequency :LiveData<String> = _info_wifi_frequency
    val getMobileStatus :LiveData<String> = _info_mobile_status
    val getMobileMulti :LiveData<String> = _info_mobile_Multi
    val getMobileAPN :LiveData<String> = _info_mobile_apn
    val getMobileIp :LiveData<String> = _info_mobile_ip
    val getMobileIpv6 :LiveData<String> = _info_mobile_ipv6
    val getMobileInterface :LiveData<String> = _info_mobile_interface
    val getWifiSignal :LiveData<String> = _info_wifi_signal
    val getWifiSignalPer :LiveData<String> = _info_wifi_signalPer


    //cpu info livedata
    private val _info_cpu_processor = MutableLiveData<String>()
    private val _info_cpu_frequency = MutableLiveData<String>()
    private val _info_cpu_abis = MutableLiveData<String>()
    private val _info_cpu_hardware = MutableLiveData<String>()
    private val _info_cpu_governor = MutableLiveData<String>()
    private val _info_cpu_cores = MutableLiveData<String>()
    private val _info_cpu_range = MutableLiveData<String>()
    private val _info_gpu_renderer = MutableLiveData<String>()
    private val _info_gpu_vendor = MutableLiveData<String>()
    private val _info_gpu_version = MutableLiveData<String>()
    private val _info_gpu_vulkan = MutableLiveData<String>()
    private val _info_gpu_frequency = MutableLiveData<String>()
    private val _info_gpu_current = MutableLiveData<String>()

    fun getCpuInfoData(context: Context){
        viewModelScope.launch {
            HardInfoTool.findCpuInfo(context).apply {
                _info_cpu_processor.postValue(processorName)
                _info_cpu_frequency.postValue(cpuFreq)
                _info_cpu_abis.postValue(abis)
                _info_cpu_hardware.postValue(cpuHardware)
                _info_cpu_governor.postValue(cpuGovernor)
                _info_cpu_cores.postValue(cpuCores)
                _info_cpu_range.postValue(cpuFreqRange)
            }
        }
    }

    fun getGpuInfoData(context: Context){
        viewModelScope.launch {
            HardInfoTool.findGpuInfo(context).apply {
                _info_gpu_vulkan.postValue(vulkan)
                _info_gpu_frequency.postValue(freq)
                _info_gpu_current.postValue(currFreq)
            }
        }
    }

    val getCpuProcessor :LiveData<String> = _info_cpu_processor
    val getCpuFrequency :LiveData<String> = _info_cpu_frequency
    val getCpuApi : LiveData<String> = _info_cpu_abis
    val getCpuHardware :LiveData<String> = _info_cpu_hardware
    val getCpuGovernor :LiveData<String> = _info_cpu_governor
    val getCpuCores :LiveData<String> = _info_cpu_cores
    val getCpuRange :LiveData<String> = _info_cpu_range
    val getGpuRender :LiveData<String> = _info_gpu_renderer
    val getGpuVendor :LiveData<String> = _info_gpu_vendor
    val getGpuVersion :LiveData<String> = _info_gpu_version
    val getGpuVulkan : LiveData<String> = _info_gpu_vulkan
    val getGpuFrequency : LiveData<String> = _info_gpu_frequency
    val getGpuCurrent : LiveData<String> = _info_gpu_current

    //battery info livedata
    private val _info_batt_capacity = MutableLiveData<String>()
    private val _info_batt_current = MutableLiveData<String>()
    private val _info_batt_temp = MutableLiveData<String>()
    private val _info_batt_health = MutableLiveData<String>()
    private val _info_batt_source = MutableLiveData<String>()
    private val _info_batt_voltage = MutableLiveData<String>()
    private val _info_batt_tecnology = MutableLiveData<String>()
    private val _info_batt_charge = MutableLiveData<String>()
    private val _info_batt_per = MutableLiveData<String>()

    val getBattCapacity: LiveData<String> = _info_batt_capacity
    val getBattCurrent: LiveData<String> = _info_batt_current
    val getBattTemp: LiveData<String> = _info_batt_temp
    val getBattHealth: LiveData<String> = _info_batt_health
    val getBattSource: LiveData<String> = _info_batt_source
    val getBattVoltage: LiveData<String> = _info_batt_voltage
    val getBattTecnology: LiveData<String> = _info_batt_tecnology
    val getBattCharge: LiveData<String> = _info_batt_charge
    val getBattPer: LiveData<String> = _info_batt_per

    fun getBatterInfo(context: Context){
        viewModelScope.launch {
            BatteryTool.getBatteryInfo(context).apply {
                _info_batt_capacity.postValue(capability)
                _info_batt_current.postValue(batteryCurrent)
                _info_batt_temp.postValue(temperature)
                _info_batt_health.postValue(healthStatus)
                _info_batt_source.postValue(pluggedStatus)
                _info_batt_voltage.postValue(voltage)
                _info_batt_tecnology.postValue(technology)
                _info_batt_charge.postValue(chargeStatus)
                _info_batt_per.postValue(level)
            }
        }
    }

}