package com.example.newdc.modelutil

import android.content.Context
import android.text.format.Formatter
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.newdc.modelutil.dev.ApkInfoData
import com.example.newdc.modelutil.dev.AppPermissionInfoData
import com.example.newdc.modelutil.dev.AppsTool
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class AppInfoViewModel: ViewModel() {
    private val _appPackageList = MutableLiveData<MutableList<ApkInfoData>>()
    private val _appPermissionList = MutableLiveData<MutableList<AppPermissionInfoData>>()

    fun getAppPackageList(): MutableLiveData<MutableList<ApkInfoData>> = _appPackageList
    fun getAppPermissionList(): LiveData<MutableList<AppPermissionInfoData>> = _appPermissionList

    //application info
    var _genaralAppName = MutableLiveData<String>()
    var _genaralPackName = MutableLiveData<String>()
    var _genaralVerName = MutableLiveData<String>()
    var _genaralVerCode = MutableLiveData<String>()
    var _genaralSize = MutableLiveData<String>()
    var _genaralTarget = MutableLiveData<String>()
    var _genaralTargetVersion = MutableLiveData<String>()
    var _genaralMin = MutableLiveData<String>()
    var _genaralMinName = MutableLiveData<String>()
    var _genaralSystem = MutableLiveData<String>()
    var _genaralProcess = MutableLiveData<String>()
    var _genaralNative = MutableLiveData<String>()
    var _genaralFirstIns = MutableLiveData<String>()
    var _genaralUpdate = MutableLiveData<String>()
    var _genaralSource = MutableLiveData<String>()
    var _genaralInstaller = MutableLiveData<String>()

    var _cerificationAlgorithm = MutableLiveData<String>()
    var _cerificationValidFr = MutableLiveData<String>()
    var _cerificationValidTo = MutableLiveData<String>()
    var _cerificationPubMD5 = MutableLiveData<String>()
    var _cerificationMD5 = MutableLiveData<String>()
    var _cerificationSerNum = MutableLiveData<String>()
    var _cerificationIssName = MutableLiveData<String>()
    var _cerificationIssOrg = MutableLiveData<String>()
    var _cerificationIssCountry = MutableLiveData<String>()
    var _cerificationSubName = MutableLiveData<String>()
    var _cerificationSubOrg = MutableLiveData<String>()
    var _cerificationSubCountry = MutableLiveData<String>()



    fun getAppPackage(context: Context){
        viewModelScope.launch(Dispatchers.IO) {
            val appList = AppsTool.getApkPackageList(context)
            _appPackageList.postValue(appList)
        }
    }

    fun getAppGeneralInfo(context: Context,packageName:String){
        viewModelScope.launch(Dispatchers.IO) {
            val genInfo = AppsTool.findGeneralInfo(context,packageName)
            genInfo.apply {
                _genaralAppName.postValue(appName)
                _genaralPackName.postValue(packageName)
                _genaralVerName.postValue(versionName)
                _genaralVerCode.postValue(versionCode.toString())
                _genaralSize.postValue(Formatter.formatFileSize(context,apkSize))
                _genaralTarget.postValue(targetSdkCode.toString())
                _genaralTargetVersion.postValue(targetSdkVersionName)
                _genaralMin.postValue(minSdkVersionCode.toString())
                _genaralMinName.postValue(minSdkVersionName)
                _genaralSystem.postValue(isUserApp.toString())
                _genaralProcess.postValue(processName)
                _genaralNative.postValue(nativeLibrary)
                _genaralFirstIns.postValue(firstInstallTime)
                _genaralUpdate.postValue(lastUpdateTime)
                _genaralSource.postValue(source)
                _genaralInstaller.postValue(installer)
            }
        }
    }

    fun getAppCertificateInfo(context: Context,packageName:String){
        viewModelScope.launch(Dispatchers.IO) {
            val certificateInfo = AppsTool.findCertificationInfo(context, packageName)
            certificateInfo.apply {
                _cerificationAlgorithm.postValue(algorithm)
                _cerificationValidFr.postValue(validFrom)
                _cerificationValidTo.postValue(validTo)
                _cerificationPubMD5.postValue(publicKey)
                _cerificationMD5.postValue(md5)
                _cerificationSerNum.postValue(serialNumber)
                _cerificationIssName.postValue(issuerName)
                _cerificationIssOrg.postValue(issuerOrg)
                _cerificationIssCountry.postValue(issuerCountry)
                _cerificationSubName.postValue(subjectName)
                _cerificationSubOrg.postValue(subjectOrg)
                _cerificationSubCountry.postValue(subjectCountry)
            }
        }
    }

    fun getAppAllPermissionUse(context: Context,packageName:String){
        viewModelScope.launch(Dispatchers.IO) {
            val permissions = AppsTool.findAppPermissionsInfo(context, packageName)
            _appPermissionList.postValue(permissions)
        }
    }

}