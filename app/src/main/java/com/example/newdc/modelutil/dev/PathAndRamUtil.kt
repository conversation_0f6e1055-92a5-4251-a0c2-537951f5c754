package com.example.newdc.modelutil.dev

import android.app.ActivityManager
import android.content.Context
import android.os.Environment
import android.os.StatFs

object PathAndRamUtil {

    fun getPathAvailBlock(path: String?): Long {
        return try {
            val statFs = StatFs(path)
            statFs.blockSizeLong * statFs.availableBlocksLong
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
            0L
        }
    }

    fun getPathTotalBlock(path: String?): Long {
        return try {
            val statFs = StatFs(path)
            statFs.blockSizeLong * statFs.blockCountLong
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
            0L
        }
    }

    fun getStoragePercent(): Int{
        val exPath = Environment.getExternalStorageDirectory().absolutePath
        val storageTotal = getPathTotalBlock(exPath)
        val storageAvail = getPathAvailBlock(exPath)
        val storageUsed = storageTotal - storageAvail
        return try {
            (storageUsed * 100 / storageTotal).toInt()
        }catch (e: Exception){
            0
        }
    }

    fun getRamAvailAndTotal(context: Context): Pair<Long, Long> {
        val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        return try {
            val memoryInfo = ActivityManager.MemoryInfo()
            activityManager.getMemoryInfo(memoryInfo)
            val totalRam = memoryInfo.totalMem
            val availableRam = memoryInfo.availMem
            Pair(availableRam, totalRam)
        } catch (e: Exception) {
            e.printStackTrace()
            Pair(0L, 0L)
        }
    }

    fun getRamPercent(context: Context): Int{
        val ramValue = getRamAvailAndTotal(context)
        val ramTotal = ramValue.second
        val ramAvail = ramValue.first
        val ramUsed = ramTotal - ramAvail
        val percent = try {
            (ramUsed * 100 / ramTotal).toInt()
        }catch (e: Exception){
            0
        }
        return percent
    }

}