package com.example.newdc.modelutil.data

import android.annotation.SuppressLint
import android.text.TextUtils
import com.example.newdc.modelutil.tool.FormatTool
import com.tencent.mmkv.MMKV

object StoreKeyData {

    const val DEFAULT_KEY= "treeDefault"
    const val TEST_VALUE_COLOR = "test_value_color"
    const val TEST_VALUE_TOUCH = "test_value_touch"
    const val TEST_VALUE_FLASHLIGHT = "test_value_flashlight"
    const val TEST_VALUE_SPEAKER = "test_value_speaker"
    const val TEST_VALUE_PROXIMITY = "test_value_proximity"
    const val TEST_VALUE_LOUD = "test_value_loud"
    const val TEST_VALUE_LIGHT = "test_value_light"
    const val TEST_VALUE_VIBRATION = "test_value_vibration"
    const val TEST_VALUE_VOLUME_UP = "test_value_volume_up"
    const val TEST_VALUE_VOLUME_DOWN = "test_value_volume_down"

    const val BASE_KEY_AGREE_PRIVACY = "base_key_agree_privacy"
    const val BASE_FIRST_ENTER_TIME = "base_first_enter_time"
    const val BASE_RECORD_TIME = "base_record_time"

    const val PERMISSION_BATTERY_OPT_COUNT = "permission_batter_opt_count"
    const val PERMISSION_BATTERY_OPT_LAST = "permission_batter_opt_last"
    const val PERMISSION_NOTIFY_COUNT = "permission_notify_count"
    const val PERMISSION_NOTIFY_LAST = "permission_notify_last"
    const val PERMISSION_NOTIFY_FIRST = "permission_notify_first"
    const val PERMISSION_HAS_GUILD = "permission_has_guild"
    const val PERMISSION_NOTIFY_SWITCH = "permission_notify_switch"

    const val NOTIFY_SHOW_INDEX = "notify_show_index"
    const val NOTIFY_SHOW_FIRST = "notify_show_first"
    const val NOTIFY_SHOW_LAST = "notify_show_last"
    const val NOTIFY_SHOW_COUNT = "notify_show_count"

    const val BI_USER_ID = "bi_user_id"
    const val BI_MMP_ID = "bi_mmp_id"
    const val BI_INSTANCE_ID = "bi_instance_id"
    const val BI_INSTALL_HAS = "bi_install_has"
    const val BI_CONFIG_FETCH_LAST = "bi_config_fetch_last"
    const val BI_GET_USER_TIME = "bi_get_user_time"
    const val BI_REMOTE_CONFIG_STR = "bi_remote_config_str"
    const val BI_RECORD_EVENT = "bi_record_event"
    const val FB_CONFIG_FETCH_LAST = "fb_config_fetch_last"

    const val REVIEW_HAS_GET = "review_has_get"
    const val REVIEW_SHOW_COUNT = "review_show_count"

    const val AD_INTERSTITIAL_LAST = "ad_interstitial_last"
    const val AD_OPEN_LAST = "ad_open_last"
    const val AD_TOTAL_VALUE = "ad_total_value"

    val getDefaultKey = MMKV.mmkvWithID(DEFAULT_KEY)


    fun setString(key: String?, value: String?) {
        getDefaultKey.putString(key, value)
    }

    fun getString(key: String?,default: String = ""): String? {
        return getDefaultKey.getString(key, default)
    }

    fun setBoolean(key: String?, value: Boolean) {
        getDefaultKey.putBoolean(key, value)
    }

    fun getBoolean(key: String?,default: Boolean = false): Boolean {
        return getDefaultKey.getBoolean(key, default)
    }

    fun setInt(key: String?, value: Int) {
        getDefaultKey.putInt(key, value)
    }

    fun getInt(key: String?,default: Int = 0): Int {
        return getDefaultKey.getInt(key, default)
    }

    fun setFloat(key: String?, value: Float) {
        getDefaultKey.putFloat(key, value)
    }

    fun getFloat(key: String?,default: Float = 0f): Float {
        return getDefaultKey.getFloat(key, default)
    }

    fun setLong(key: String?, value: Long) {
        getDefaultKey.putLong(key, value)
    }

    fun getLong(key: String?,default: Long = 0L): Long {
        return getDefaultKey.getLong(key, default)
    }

    fun restTodayConfig(){
        setInt(NOTIFY_SHOW_COUNT,0)
        setBoolean(BI_RECORD_EVENT,false)
    }

    @SuppressLint("SimpleDateFormat")
    fun recordTime(): Boolean {
        try {
            val saveTime = getString(BASE_RECORD_TIME, "2024-01-01")
            val todayTime = FormatTool.getUTCTime()
            if (!TextUtils.isEmpty(todayTime) && !TextUtils.isEmpty(saveTime)) {
                if (saveTime != todayTime) {
                    setString(BASE_RECORD_TIME,todayTime)
                    return true
                }
            }
        } catch (e: Exception) {
            return true
        }
        return false
    }

}