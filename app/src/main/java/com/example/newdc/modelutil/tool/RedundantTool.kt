package com.example.newdc.modelutil.tool

import android.content.Context
import android.os.Build
import android.os.Environment
import com.example.newdc.R
import com.example.newdc.modelutil.data.ItemFileData
import com.example.newdc.modelutil.data.ReItemGroupData
import java.io.File

object RedundantTool {
    private var reApkTotalSize = 0L
    private var reDownloadTotalSize = 0L
    private var reShortcutTotalSize = 0L
    private var reTempTotalSize = 0L
    private var reLogTotalSize = 0L

    private var apksList = mutableListOf<ItemFileData>()
    private var tempList = mutableListOf<ItemFileData>()
    private var logList = mutableListOf<ItemFileData>()


    fun startCollectRedundantFile(context: Context): MutableList<ReItemGroupData> {
        reApkTotalSize = 0L
        reDownloadTotalSize = 0L
        reShortcutTotalSize = 0L
        reTempTotalSize = 0L
        reLogTotalSize = 0L
        apksList.clear()
        tempList.clear()
        logList.clear()

        val pathFile = File(Environment.getExternalStorageDirectory().absolutePath)
        findDupFile(pathFile)
        val downloadFiles = findDownLoadFile(context,mutableListOf(),pathFile)
        val shortcutFiles = findScreenShot(context)

        val groupList = mutableListOf<ReItemGroupData>()
        groupList.let {
            it.add(ReItemGroupData(context.getString(R.string.type_apks), apksList, reApkTotalSize))
            it.add(ReItemGroupData(context.getString(R.string.type_download_files),downloadFiles.first,downloadFiles.second))
            it.add(ReItemGroupData(context.getString(R.string.type_screenshots),shortcutFiles.first,shortcutFiles.second))
            it.add(ReItemGroupData(context.getString(R.string.type_log_files), logList, reLogTotalSize))
            it.add(ReItemGroupData(context.getString(R.string.type_temp_files), tempList, reTempTotalSize))
        }

        return groupList
    }


    fun findDupFile(pathFile: File){
        if(pathFile.exists()){
            if (pathFile.isDirectory) {
                val files = pathFile.listFiles()
                if (files != null) {
                    for (item in files) {
                        if (item.isDirectory) {
                            findDupFile(item)
                        } else {
                            val size = FilesTool.findFilesSize(item)
                            if (item.name.endsWith(".apk")) {
                                val mFile = ItemFileData(
                                    title = item.name, fileSize = size, filePath =  item.absolutePath, modifyDate = item.lastModified(),
                                    type = MediaStorageTool.TYPE_BROWSER_APK, typeIcon = 0, mime = "application/octet-stream")
                                apksList.add(mFile)
                                reApkTotalSize += size
                            }else if (item.name.endsWith(".temp")){
                                val mFile = ItemFileData(
                                    title = item.name, fileSize = size, filePath =  item.absolutePath, modifyDate = item.lastModified(),
                                    type = MediaStorageTool.TYPE_BROWSER_TEMP, typeIcon = 0, mime = "*/*")
                                tempList.add(mFile)
                                reTempTotalSize += size
                            }else if (item.name.endsWith(".log")) {
                                val mFile = ItemFileData(
                                    title = item.name, fileSize = size, filePath =  item.absolutePath, modifyDate = item.lastModified(),
                                    type = MediaStorageTool.TYPE_BROWSER_LOG, typeIcon = 0, mime = "*/*")
                                logList.add(mFile)
                                reLogTotalSize += size
                            }
                        }
                    }
                }
            }
        }
    }

    fun findDownLoadFile(context: Context, containList: MutableList<ItemFileData>, pathFile: File): Pair<MutableList<ItemFileData>,Long> {
        if (pathFile.exists()){
            if (pathFile.isDirectory) {
                val files = pathFile.listFiles()
                if (files != null) {
                    for (item in files) {
                        if (item.isDirectory) {
                            findDownLoadFile(context,containList,item)
                        } else {
                            val size = FilesTool.findFilesSize(item)
                            if (!item.name.endsWith(".apk") && !item.name.endsWith(".log") && !item.name.endsWith(".temp")) {
                                val mime = FilesTool.getFileMime(context, item)
                                val type = MediaStorageTool.findTypeFromMime(mime)
                                val mFile = ItemFileData(
                                    title = item.name, fileSize = size, filePath =  item.absolutePath, modifyDate = item.lastModified(),
                                    type = type, typeIcon = 0, mime = mime)
                                containList.add(mFile)
                                reDownloadTotalSize += size
                            }
                        }
                    }
                }
            }
        }
        return Pair(containList, reDownloadTotalSize)
    }

    fun findScreenShot(context: Context): Pair<MutableList<ItemFileData>,Long> {
        val list = mutableListOf<ItemFileData>()
        var file: File? = null
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            val path = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_SCREENSHOTS).absolutePath
            file = File(path)
        }
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.Q || file == null || !file.exists()) {
            val path = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES).absolutePath
            file = File(path, "Screenshots")
            if (!file.exists()) {
                file = File(
                    Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DCIM).absolutePath,
                    "Screenshots"
                )
            }
        }
        if (file.isDirectory) {
            val files = file.listFiles()
            if (files != null && files.isNotEmpty()) {
                for (item in files) {
                    if (item.isFile) {
                        val size = FilesTool.findFilesSize(item)
                        val mime = FilesTool.getFileMime(context, item)
                        val type = MediaStorageTool.findTypeFromMime(mime)
                        if (mime != null && mime.contains("image")){
                            val mFile = ItemFileData(
                                title = item.name, fileSize = size, filePath =  item.absolutePath, modifyDate = item.lastModified(),
                                type = type, typeIcon = 0, mime = mime)
                            list.add(mFile)
                            reShortcutTotalSize += size
                        }
                    }
                }
            }
        } else {
            if (file.isFile) {
                val size = FilesTool.findFilesSize(file)
                val mime = FilesTool.getFileMime(context, file)
                val type = MediaStorageTool.findTypeFromMime(mime)
                if (mime != null && mime.contains("image")){
                    val mFile = ItemFileData(
                        title = file.name, fileSize = size, filePath =  file.absolutePath, modifyDate = file.lastModified(),
                        type = type, typeIcon = 0, mime = mime)
                    list.add(mFile)
                    reShortcutTotalSize += size
                }
            }
        }
        return Pair(list, reShortcutTotalSize)
    }

}