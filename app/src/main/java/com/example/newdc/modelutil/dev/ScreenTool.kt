package com.example.newdc.modelutil.dev

import android.app.ActivityManager
import android.content.Context
import android.content.res.Configuration
import android.graphics.Point
import android.os.Build
import android.util.DisplayMetrics
import android.view.Display
import android.view.WindowManager
import com.example.newdc.R
import kotlin.math.*

object ScreenTool {

    fun findOpenGlVersion(context: Context): String {
       return try {
           (context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager).deviceConfigurationInfo.glEsVersion
        } catch (e: Exception) {
            e.printStackTrace()
           ""
        }
    }

    fun findScreenInfo(context: Context): DisplayInfoData {
        val displayName = findScreenName(context)
        val screenWidth = "${findScreenWidth(context)} px"
        val screenHeight = "${findScreenHeight(context)} px"
        val screenSize = findScreenSize(context)
        val physicalSize = String.format("%.2f ${context.getString(R.string.info_inches)}", findPhysicalSize(context))
        val defaultOrientation = defaultOrientation(context)
        val refreshRate = "${round(findScreenRate(context))}Hz"
        val isHDRSupported = getHDRSupported(context)
        val brightnessMode = findBrightnessMode(context)
        val screenTimeout = "${findScreenTimeout(context)} ${context.getString(R.string.info_seconds)}"

        return DisplayInfoData(displayName = displayName,screenWidth = screenWidth,screenHeight = screenHeight,screenSize = screenSize,physicalSize = physicalSize,
            defaultOrientation = defaultOrientation,refreshRate = refreshRate, isHDRSupported = isHDRSupported, brightnessMode = brightnessMode,screenTimeout = screenTimeout)
    }

    fun findDensInfo(context: Context): DensityInfo {
        val displayBucket = findScreenBucket(context)
        val displayDpi = findDisplayDpi(context)
        val xdpi = findXdpi(context)
        val ydpi = findYdpi(context)
        val logicalDensity = findLogicalDensity(context).toString()
        val scaledDensity = findScaledDensity(context).toString()
        val fontScale = findFontScale(context).toString()

        return DensityInfo(displayBucket = displayBucket,displayDpi = displayDpi,xdpi = xdpi, ydpi = ydpi,
            logicalDensity = logicalDensity,scaledDensity = scaledDensity,fontScale = fontScale)
    }

    private fun findDisplayDpi(context: Context): String {
        val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        val display = windowManager.defaultDisplay
        val metrics = DisplayMetrics()
        display.getRealMetrics(metrics)
        return "${metrics.densityDpi}dpi"
    }

    private fun findPhysicalSize(context: Context): Float {
        val point = Point()
        val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        val defaultDisplay = windowManager.defaultDisplay
        defaultDisplay.getRealSize(point)
        val displayMetrics = DisplayMetrics()
        defaultDisplay.getRealMetrics(displayMetrics)
        return sqrt((point.y / displayMetrics.ydpi).toDouble().pow(2.0) + (point.x / displayMetrics.xdpi).toDouble().pow(2.0)).toFloat()
    }

    private fun findScreenRate(context: Context): Float{
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
            windowManager.defaultDisplay.mode.refreshRate
        } else {
            0f
        }
    }

    private fun defaultOrientation(context: Context): String {
        val res =  when (context.resources.configuration.orientation) {
            Configuration.ORIENTATION_LANDSCAPE -> {
                R.string.ori_land
            }
            Configuration.ORIENTATION_PORTRAIT -> {
                R.string.ori_por
            }
            else -> {
                R.string.base_undefined
            }
        }
        return context.getString(res)
    }

    private fun findScreenSize(context: Context): String {
        val res =  when (context.resources.configuration.screenLayout and Configuration.SCREENLAYOUT_SIZE_MASK) {
            Configuration.SCREENLAYOUT_SIZE_SMALL -> {
                R.string.dis_small
            }
            Configuration.SCREENLAYOUT_SIZE_NORMAL -> {
                R.string.dis_normal
            }
            Configuration.SCREENLAYOUT_SIZE_LARGE -> {
                R.string.dis_large
            }
            Configuration.SCREENLAYOUT_SIZE_XLARGE -> {
                R.string.dis_x_large
            }
            else -> {
                R.string.base_undefined
            }
        }
        return context.getString(res)
    }

    private fun findScreenHeight(context: Context): Int {
        val point = Point()
        val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        val defaultDisplay = windowManager.defaultDisplay
        defaultDisplay.getRealSize(point)
        return point.x
    }

    private fun findScreenWidth(context: Context): Int {
        val point = Point()
        val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        val defaultDisplay = windowManager.defaultDisplay
        defaultDisplay.getRealSize(point)
        return point.y
    }

    private fun findLogicalDensity(context: Context): Float {
        return context.resources.displayMetrics.density
    }

    private fun findScaledDensity(context: Context): Float {
        return context.resources.displayMetrics.scaledDensity
    }

    private fun findFontScale(context: Context): Float {
        return context.resources.configuration.fontScale
    }

    private fun findScreenDpi(context: Context): Int {
        val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        val display = windowManager.defaultDisplay
        val metrics = DisplayMetrics()
        display.getRealMetrics(metrics)
        return metrics.densityDpi
    }

    private fun findXdpi(context: Context): String {
        val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        val display = windowManager.defaultDisplay
        val metrics = DisplayMetrics()
        display.getRealMetrics(metrics)
        return "${metrics.xdpi}dpi"
    }

    private fun findYdpi(context: Context): String {
        val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        val display = windowManager.defaultDisplay
        val metrics = DisplayMetrics()
        display.getRealMetrics(metrics)
        return "${metrics.ydpi}dpi"
    }

    private fun findScreenTimeout(context: Context): Int {
        val contentResolver = context.contentResolver
        return android.provider.Settings.System.getInt(
            contentResolver, android.provider.Settings.System.SCREEN_OFF_TIMEOUT, 0
        ) / 1000
    }

    private fun findScreenBucket(context: Context): String {
        val density = context.resources.displayMetrics.density
        return if (density <= 0.75) {
            "ldpi"
        } else if (density <= 1.0) {
            "mdpi"
        } else if (density <= 1.5) {
            "hdpi"
        } else if (density <= 2.0) {
            "xhdpi"
        } else if (density <= 3.0) {
            "xxhdpi"
        } else {
            "xxxhdpi"
        }
    }

    private fun findBrightnessMode(context: Context): String {
        val contentResolver = context.contentResolver
        val mode =  android.provider.Settings.System.getInt(
            contentResolver, android.provider.Settings.System.SCREEN_BRIGHTNESS_MODE,
            android.provider.Settings.System.SCREEN_BRIGHTNESS_MODE_MANUAL
        )
        val rMode = when(mode){
            1 -> R.string.base_adaptive
            0 -> R.string.base_manual
            else -> R.string.base_unknown
        }
        return context.getString(rMode)
    }

    private fun findScreenName(context: Context): String{
        return try {
            val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
            windowManager.defaultDisplay.name
        } catch (e: Exception) {
            e.printStackTrace()
            ""
        }
    }

    private fun getHDRSupported(context: Context): String {
        val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        val display = windowManager.defaultDisplay
        val sb = StringBuilder()
        val hdrCapabilities = display.hdrCapabilities
        if (hdrCapabilities != null){
            val supportedHdrTypes = hdrCapabilities.supportedHdrTypes
            if (supportedHdrTypes.isNotEmpty()){
                for (hdrType in supportedHdrTypes) {
                    when (hdrType) {
                        Display.HdrCapabilities.HDR_TYPE_HDR10 -> {
                            sb.append("HDR10/PQ,")
                        }
                        Display.HdrCapabilities.HDR_TYPE_DOLBY_VISION -> {
                            sb.append("Dolby Vision,")
                        }
                        Display.HdrCapabilities.HDR_TYPE_HDR10_PLUS -> {
                            sb.append("HDR10+,")
                        }
                        Display.HdrCapabilities.HDR_TYPE_HLG -> {
                            sb.append("Hybrid Log-Gamma,")
                        }
                    }
                }
                if (sb.isNotEmpty()){
                    sb.delete(sb.length - 1, sb.length)
                }else{
                    sb.append(context.getString(R.string.base_supported))
                }
            }
        }
        if (sb.isBlank()) sb.append(context.getString(R.string.base_no_supported))
        return sb.toString()
    }
}

data class DisplayInfoData(
    val displayName: String = "",
    val screenWidth: String = "",
    val screenHeight: String = "",
    val screenSize: String = "",
    val physicalSize: String = "",
    val defaultOrientation: String = "",
    val refreshRate: String = "",
    val isHDRSupported: String = "",
    val brightnessMode: String = "",
    val screenTimeout: String = ""
)

data class DensityInfo(
    val displayBucket: String = "",
    val displayDpi: String = "",
    val xdpi: String = "",
    val ydpi: String = "",
    val logicalDensity: String = "",
    val scaledDensity: String = "",
    val fontScale: String = ""
)