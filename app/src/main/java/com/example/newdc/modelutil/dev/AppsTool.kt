package com.example.newdc.modelutil.dev

import android.content.Context
import android.content.Intent
import android.content.pm.*
import android.graphics.drawable.Drawable
import android.net.Uri
import android.os.Build
import android.os.Parcelable
import android.util.Log
import com.example.newdc.R
import com.example.newdc.modelutil.tool.FilesTool
import com.example.newdc.modelutil.tool.FormatTool
import com.example.newdc.modelutil.tool.PermissionTool
import kotlinx.parcelize.Parcelize
import java.io.ByteArrayInputStream
import java.io.File
import java.security.MessageDigest
import java.security.cert.CertificateFactory
import java.security.cert.X509Certificate

object AppsTool {

    fun getApkPackageList(context: Context): MutableList<ApkInfoData> {
        val apkList = (mutableListOf <ApkInfoData>())
        val installedApps = context.packageManager.getInstalledPackages(PackageManager.GET_META_DATA)
        for (pkInfo in installedApps) {
            try {
                if (pkInfo.packageName != context.packageName) {
                    try {
                        val isSystem = pkInfo.applicationInfo.flags and ApplicationInfo.FLAG_SYSTEM != 0
                        val apkInfo = pkInfo.applicationInfo
                        val file = File(apkInfo.sourceDir)
                        val size = FilesTool.findFilesSize(file)
                        val name = apkInfo.loadLabel(context.packageManager).toString()
                        val appInfoModel = ApkInfoData(name, pkInfo.packageName, apkInfo, pkInfo.firstInstallTime, size, isSystem)
                        apkList.add(appInfoModel)
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                }
            } catch (e: Exception) {
                throw RuntimeException(e)
            }
        }
        return apkList
    }

    fun getApkIconDrawable(context: Context, pkg: String?): Drawable? {
        if (pkg == null) return null
        var icon: Drawable? = null
        try {
            val pm = context.packageManager
            val appInfo = pm.getApplicationInfo(pkg!!, 0)
            if (appInfo != null) {
                icon = appInfo.loadIcon(pm)
            }
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        }
        return icon
    }

    fun openGpApp(context: Context, packageName: String?) {
        if (packageName == null) return
        val gpPackage = "com.android.vending"
        try {
            if (isPksExits(context, gpPackage)) {
                val intent = Intent(Intent.ACTION_VIEW, Uri.parse("market://details?id=$packageName"))
                intent.setPackage(gpPackage)
                context.startActivity(intent)
            } else {
                val intent = Intent(Intent.ACTION_VIEW, Uri.parse("https://play.google.com/store/apps/details?id=$packageName"))
                context.startActivity(intent)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun isPksExits(context: Context, packageName: String?): Boolean {
        val packageManager = context.packageManager
        return try {
            val packageInfo = packageManager.getPackageInfo(packageName!!, PackageManager.GET_ACTIVITIES)
            packageInfo != null
        } catch (e: PackageManager.NameNotFoundException) {
            e.printStackTrace()
            false
        }
    }

    fun findGeneralInfo(context: Context, pkgName: String?): AppGeneralInfo {
        val appInfo = AppGeneralInfo()
        val pm = context.packageManager
        var applicationInfo: ApplicationInfo? = null
        var packageInfo: PackageInfo? = null
        try {
            applicationInfo = pm.getApplicationInfo(pkgName!!, 0)
        } catch (e: PackageManager.NameNotFoundException) {
            e.printStackTrace()
        }
        try {
            packageInfo = pm.getPackageInfo(pkgName!!, 0)
        } catch (e: PackageManager.NameNotFoundException) {
            e.printStackTrace()
        }
        if (applicationInfo != null) {
            val appName = pm.getApplicationLabel(applicationInfo).toString()
            val targetSdkCode = applicationInfo.targetSdkVersion
            val targetVersionName = "Android " + OtherSysInfoTool.androidVerCode(targetSdkCode) + " " + OtherSysInfoTool.androidVerName(targetSdkCode)
            val packageName = applicationInfo.packageName
            var minSDKCode = 21
            var minSDKName = ""
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                minSDKCode = applicationInfo.minSdkVersion
                minSDKName = "Android " + OtherSysInfoTool.androidVerCode(minSDKCode) + " " + OtherSysInfoTool.androidVerName(minSDKCode)
            }
            val processName = applicationInfo.processName
            val nativeBit = findAppLib(applicationInfo)
            val size = FilesTool.findFilesSize(File(applicationInfo.sourceDir) )
            appInfo.apply {
                this.appName = appName
                this.packageName = packageName
                minSdkVersionCode = minSDKCode
                minSdkVersionName = minSDKName
                this.targetSdkCode = targetSdkCode
                targetSdkVersionName = targetVersionName
                this.processName = processName
                nativeLibrary = nativeBit
                apkSize = size
            }
        }
        if (packageInfo != null) {
            val verCode = packageInfo.versionCode
            val verName = packageInfo.versionName
            val mFirstInstallTime = packageInfo.firstInstallTime
            val mUpdateTime = packageInfo.lastUpdateTime
            val appFirstIns = FormatTool.mdyTimeFormat(mFirstInstallTime)
            val appUpdate = FormatTool.mdyTimeFormat(mUpdateTime)

            appInfo.apply {
                versionCode = verCode
                versionName = verName
                firstInstallTime = appFirstIns
                lastUpdateTime = appUpdate
            }
        }
        val isSysApp = isSysApp(context, pkgName)
        val installer = findAppInstaller(pm, pkgName)
        var installerName = ""
        if (installer != "N/A") {
            try {
                installerName = pm.getApplicationLabel(pm.getApplicationInfo(installer, PackageManager.GET_META_DATA)).toString()
            } catch (e: PackageManager.NameNotFoundException) {
                e.printStackTrace()
            }
        }
        appInfo.apply {
            this.isUserApp = !isSysApp
            this.installer = installerName
            source = installer
        }
        return appInfo
    }

    fun findCertificationInfo(context: Context,packageName: String): AppCertificateInfo{
        val certificateInfo = AppCertificateInfo()
        try {
            val packageInfo = context.packageManager.getPackageInfo(packageName, PackageManager.GET_SIGNATURES)
            val signatures = packageInfo.signatures
            if (signatures != null && signatures.isNotEmpty()) {
                val signature = signatures[0]
                findSignature(signature,certificateInfo)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return certificateInfo
    }

    fun findAppPermissionsInfo(context: Context, pkgName: String): MutableList<AppPermissionInfoData> {
        val permissionList = mutableListOf<AppPermissionInfoData>()
        try {
            val pm = context.packageManager
            val packageInfo = pm.getPackageInfo(pkgName, PackageManager.GET_PERMISSIONS)
            if (packageInfo != null) {
                val permissions = packageInfo.requestedPermissions
                if (permissions != null && permissions.isNotEmpty()) {
                    for (permission in permissions) {
                        val name = permission.substring(permission.lastIndexOf(".") + 1)
                        var model: AppPermissionInfoData? = null
                        var permissionInfo: PermissionInfo? = null
                        try {
                            permissionInfo = pm.getPermissionInfo(permission, 0)
                        } catch (e: PackageManager.NameNotFoundException) {
                            e.printStackTrace()
                        }
                        if (permissionInfo != null) {
                            val level = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                                findPermissionLevel(context,
                                    permissionInfo.protection,
                                    permissionInfo.protectionFlags
                                )
                            } else {
                                findPermissionLevel(context,
                                    permissionInfo.protectionLevel and PermissionInfo.PROTECTION_MASK_BASE,
                                    permissionInfo.protectionLevel and PermissionInfo.PROTECTION_MASK_BASE.inv()
                                )
                            }
                            var describe: String? = ""
                            try {
                                describe = permissionInfo.loadDescription(pm)?.toString()
                            } catch (e: java.lang.Exception) {
                                e.printStackTrace()
                            }
                            val isGranted = PermissionTool.checkPermissionGranted(context, permission)
                            model = AppPermissionInfoData(name, permission, isGranted, level, describe ?: "")
                        } else if (permission.isNotBlank()) {
                            model = AppPermissionInfoData(name, permission)
                        }
                        permissionList.add(model!!)
                    }
                }
            }
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        }
        return permissionList
    }

    private fun findSignature(signature: Signature,data: AppCertificateInfo) {
        try {
            val certFactory = CertificateFactory.getInstance("X.509")
            val stream = ByteArrayInputStream(signature.toByteArray())
            val cert = certFactory.generateCertificate(stream) as X509Certificate
            val mAlgorithm = cert.sigAlgName
            val valid_from = FormatTool.certDateFormat(cert.notBefore)
            val valid_to = FormatTool.certDateFormat(cert.notAfter)
            val publicMD5 = findMd5PublicKey(signature)
            val certMD5 = findMd5Signature(signature)
            val serialNum = cert.serialNumber.toString()
            data.apply {
                serialNumber = serialNum
                validFrom = valid_from
                validTo = valid_to
                algorithm = mAlgorithm
                publicKey = publicMD5
                md5 = certMD5
            }
            findIssuerDN(cert.issuerDN.name,data)
            findSubjectDN(cert.subjectDN.name,data)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun findIssuerDN(issuerDN: String,data: AppCertificateInfo) {
        val issuerParts = issuerDN.split(",".toRegex()).dropLastWhile { it.isEmpty() }
            .toTypedArray()
        for (item in issuerParts) {
            if (item.startsWith("C=")) {
                val issuerCou = item.substring(2)
                data.issuerCountry = issuerCou
            }
            if (item.startsWith("CN=")) {
                val issuerName = item.substring(3)
                data.issuerName = issuerName
            }
            if (item.startsWith("O=")) {
                val issuerOrg = item.substring(2)
                data.issuerOrg = issuerOrg
            }
        }
    }

    private fun findSubjectDN(subjectDN: String,data: AppCertificateInfo) {
        val subjectParts = subjectDN.split(",".toRegex()).dropLastWhile { it.isEmpty() }
            .toTypedArray()
        for (item in subjectParts) {
            if (item.startsWith("C=")) {
                val subjectCou = item.substring(2)
                data.subjectCountry = subjectCou
            }
            if (item.startsWith("CN=")) {
                val subjectName = item.substring(3)
                data.subjectName = subjectName
            }
            if (item.startsWith("O=")) {
                val subjectOrg = item.substring(2)
                data.subjectOrg = subjectOrg
            }
        }
    }



    private fun findAppLib(appInfo: ApplicationInfo): String {
        val nativeLibraryDir = appInfo.nativeLibraryDir
        return if (nativeLibraryDir != null) {
            if (nativeLibraryDir.endsWith("arm64") || nativeLibraryDir.endsWith("x86_64")) {
                "64bit"
            } else {
                "32bit"
            }
        } else "N/A"
    }

    private fun findMd5PublicKey(signature: Signature): String {
        return try {
            val input = ByteArrayInputStream(signature.toByteArray())
            val certFactory = CertificateFactory.getInstance("X509")
            val cert = certFactory.generateCertificate(input) as X509Certificate
            val md = MessageDigest.getInstance("MD5")
            val publicKey = cert.publicKey.encoded
            md.update(publicKey)
            bytesToString(md.digest())
        } catch (e: Exception) {
            e.printStackTrace()
            ""
        }
    }

    private fun findMd5Signature(signature: Signature): String {
        return try {
            val md = MessageDigest.getInstance("MD5")
            md.update(signature.toByteArray())
            bytesToString(md.digest())
        } catch (e: Exception) {
            e.printStackTrace()
            ""
        }
    }

    fun isSysApp(context: Context, pkg: String?): Boolean {
        return try {
            val info = context.packageManager.getPackageInfo(pkg!!, PackageManager.GET_ACTIVITIES)
            info.applicationInfo.flags and ApplicationInfo.FLAG_SYSTEM == 0
        } catch (e: PackageManager.NameNotFoundException) {
            false
        }
    }

    private fun findAppInstaller(pm: PackageManager, pkgName: String?): String {
        if (pkgName == null) return "N/A"
        var installerPackageName: String? = null
        try {
            installerPackageName = pm.getInstallerPackageName(pkgName)
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        }
        return installerPackageName ?: "N/A"
    }

    private fun bytesToString(bytes: ByteArray): String {
        val sb = StringBuilder()
        for (b in bytes) {
            sb.append(String.format("%02X:", b))
        }
        if (sb.isNotEmpty()) {
            sb.deleteCharAt(sb.length - 1)
        }
        return sb.toString()
    }

    private fun findPermissionLevel(context: Context,protectionLevel: Int, protectionFlags: Int): String {
        var protectionLevelStr: String?
        when (protectionLevel) {
            PermissionInfo.PROTECTION_NORMAL -> protectionLevelStr =
                context.getString(R.string.app_per_normal)
            PermissionInfo.PROTECTION_DANGEROUS -> protectionLevelStr =
                context.getString(R.string.app_per_dangerous)
            PermissionInfo.PROTECTION_SIGNATURE -> {
                protectionLevelStr = context.getString(R.string.app_per_signature)
                if (protectionFlags != 0) {
                    val levels = permissionMerge(protectionFlags)
                    if (levels.isNotBlank()) {
                        protectionLevelStr += " | "
                        protectionLevelStr += levels
                    }
                }
            }
            PermissionInfo.PROTECTION_SIGNATURE_OR_SYSTEM -> protectionLevelStr =
                context.getString(R.string.app_per_signature_or_sys)
            else -> {
                val levelStr = permissionMerge(protectionLevel)
                protectionLevelStr = levelStr.ifBlank {
                    "N/A"
                }
            }
        }
        return protectionLevelStr
    }

    private fun permissionMerge(protectionLevel: Int): String {
        val flags = findPermissionFlag(protectionLevel)
        val levelStr = java.lang.StringBuilder()
        for (i in flags.indices) {
            when (flags[i]) {
                "PROTECTION_FLAG_APPOP" -> {
                    levelStr.append("appop")
                }
                "PROTECTION_FLAG_PRIVILEGED" -> {
                    levelStr.append("privileged")
                }
                "PROTECTION_FLAG_SYSTEM" -> {
                    levelStr.append("system")
                }
                "PROTECTION_FLAG_DEVELOPMENT" -> {
                    levelStr.append("development")
                }
                "PROTECTION_FLAG_PRE23" -> {
                    levelStr.append("pre23")
                }
                "PROTECTION_FLAG_INSTALLER" -> {
                    levelStr.append("installer")
                }
                "PROTECTION_FLAG_VERIFIER" -> {
                    levelStr.append("verifier")
                }
                "PROTECTION_FLAG_PREINSTALLED" -> {
                    levelStr.append("preinstalled")
                }
                "PROTECTION_FLAG_SETUP" -> {
                    levelStr.append("setup")
                }
                "PROTECTION_FLAG_INSTANT" -> {
                    levelStr.append("instant")
                }
                "PROTECTION_FLAG_RUNTIME_ONLY" -> {
                    levelStr.append("runtime_only")
                }
                "PROTECTION_FLAG_OEM" -> {
                    levelStr.append("oem")
                }
                "PROTECTION_FLAG_SYSTEM_TEXT_CLASSIFIER" -> {
                    levelStr.append("text_classifier")
                }
                "PROTECTION_FLAG_CONFIGURATOR" -> {
                    levelStr.append("configurator")
                }
                "PROTECTION_FLAG_INCIDENT_REPORT_APPROVER" -> {
                    levelStr.append("report_approver")
                }
                "PROTECTION_FLAG_APP_PREDICTOR" -> {
                    levelStr.append("app_predictor")
                }
                "PROTECTION_FLAG_COMPANION" -> {
                    levelStr.append("companion")
                }
                "PROTECTION_FLAG_RETAIL_DEMO" -> {
                    levelStr.append("retail_demo")
                }
                "PROTECTION_FLAG_RECENTS" -> {
                    levelStr.append("recents")
                }
                "PROTECTION_FLAG_ROLE" -> {
                    levelStr.append("role")
                }
                "PROTECTION_FLAG_KNOWN_SIGNER" -> {
                    levelStr.append("known_signer")
                }
            }
            if (i != flags.size - 1) {
                levelStr.append(" | ")
            }
        }
        return levelStr.toString()
    }

    private fun findPermissionFlag(protectionLevel: Int): ArrayList<String> {
        val flags = ArrayList<String>()
        for (field in PermissionInfo::class.java.declaredFields) {
            if (field.name.startsWith("PROTECTION_FLAG_")) {
                try {
                    val flagValue = field.getInt(null)
                    if (protectionLevel and flagValue != 0) {
                        flags.add(field.name)
                    }
                } catch (e: IllegalAccessException) {
                    e.printStackTrace()
                }
            }
        }
        return flags
    }

}

@Parcelize
data class ApkInfoData(
    val apkName: String?,
    val pkgName: String?,
    val appInfo: ApplicationInfo?,
    val date: Long = 0,
    val size: Long = 0,
    val isSys: Boolean = false
): Parcelable

data class AppGeneralInfo(
    var appName: String = "",
    var packageName: String = "",
    var versionName: String = "",
    var versionCode: Int = 0,
    var apkSize: Long = 0,
    var targetSdkCode: Int = 0,
    var targetSdkVersionName: String = "",
    var minSdkVersionCode: Int = 0,
    var minSdkVersionName: String = "",
    var isUserApp: Boolean = false,
    var processName: String = "",
    var nativeLibrary: String = "",
    var firstInstallTime: String = "",
    var lastUpdateTime: String = "",
    var source: String = "",
    var installer: String = ""
)

data class AppCertificateInfo(
    var serialNumber: String = "",
    var validFrom: String = "",
    var validTo: String = "",
    var algorithm: String = "",
    var publicKey: String = "",
    var md5: String = "",
    var issuerName: String = "",
    var issuerOrg: String = "",
    var issuerCountry: String = "",
    var subjectName: String = "",
    var subjectOrg: String = "",
    var subjectCountry: String = ""
)

data class AppPermissionInfoData(
    val perName: String = "",
    val perContent: String = "",
    val perStatus: Boolean = false,
    val perLevel: String = "",
    val describe: String = ""
)