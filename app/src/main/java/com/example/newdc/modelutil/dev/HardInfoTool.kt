package com.example.newdc.modelutil.dev

import android.content.Context
import android.content.pm.PackageManager
import android.os.Build
import com.example.newdc.BaseApplication
import com.example.newdc.R
import com.example.newdc.modelutil.tool.ExtraTool.getSysProFromString
import java.io.*

object HardInfoTool {
    private val gpuFreqList = arrayOf(
        "/sys/class/kgsl/kgsl-3d0/devfreq/cur_freq",
        "/sys/devices/platform/kgsl-3d0.0/kgsl/kgsl-3d0/gpuclk",
        "/sys/class/kgsl/kgsl-3d0/gpuclk",
        "/sys/devices/platform/gpusysfs/gpu_clock",
        "/sys/kernel/tegra_gpu/gpu_rate",
        "/sys/devices/platform/*mali*/clock",
        "/sys/kernel/debug/ged/hal/current_freqency",
        "/sys/devices/fdb00000.qcom,kgsl-3d0/kgsl/kgsl-3d0/gpuclk",
        "/sys/devices/soc.0/1c00000.qcom,kgsl-3d0/kgsl/kgsl-3d0/gpuclk",
        "/sys/devices/platform/omap/pvrsrvkm.0/sgxfreq/frequency",
        "/sys/class/devfreq/dfrgx/cur_freq",
        "/sys/kernel/gpu/gpu_clock",
        "/sys/class/devfreq/gpufreq/cur_freq"
    )


    fun findCpuInfo(context: Context): CpuInfoData {
        val cpuFrequencies = findCpuFreq()
        val cpuFrequencyRange = findCpuFreqRange()
        val supportedABIs = findCpuAbis()
        val cpuHardware = "ro.hardware".getSysProFromString()
        val cpuGovernor = findCpuGovernor(context)
        val processorName = findProcessorName()
        val cpuCores = Runtime.getRuntime().availableProcessors().toString()

        return CpuInfoData(cpuFreq = cpuFrequencies, cpuFreqRange = cpuFrequencyRange, abis = supportedABIs,
            cpuHardware = cpuHardware, cpuGovernor = cpuGovernor,processorName = processorName, cpuCores = cpuCores)
    }

    fun findGpuInfo(context: Context): GpuInfoData{
        val vulkan = findVulkan(context.packageManager)
        val freq = findGpuFreq(context)
        val currFreq = freq

        return GpuInfoData(vulkan, freq, currFreq)
    }

    private fun findVulkan(pkm : PackageManager): String{
        var vulkan = ""
        if (pkm.hasSystemFeature("android.hardware.vulkan.version")) {
            vulkan = "1.0"
            val hasSystemFeature = pkm.hasSystemFeature("android.hardware.vulkan.version", 4198400)
            if (hasSystemFeature) {
                vulkan = "1.1"
            }
        }else{
            vulkan = "not supported"
        }
        return vulkan
    }

    private fun findGpuFreq(context: Context): String {
        var dev = context.getString(R.string.base_unknown)
        gpuFreqList.forEach { cmd ->
            try {
                val file = File(cmd)
                if (file.exists()) {
                    dev = getFileFrequency(file,1000000)
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
        return dev
    }

    private fun findProcessorName(): String {
        var cpuName = ""
        try {
            val process = ProcessBuilder().command("/system/bin/cat", "/proc/cpuinfo").redirectErrorStream(true)
                .start()
            val inputStream = process.inputStream
            val reader = BufferedReader(InputStreamReader(inputStream))
            var line: String? = ""
            while (reader.readLine().also { line = it } != null) {
                if (line?.startsWith("Processor") == true) {
                    if (line?.isNotBlank()!!){
                        cpuName = line?.substring(line!!.indexOf(":") + 1)?.trim() ?: ""
                    }
                    break
                }
            }
            reader.close()
            inputStream.close()
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return cpuName + if (Build.SUPPORTED_64_BIT_ABIS.isNotEmpty()) "64bit" else "32bit"
    }

    private fun findCpuGovernor(context: Context): String {
        if (File("/sys/devices/system/cpu/cpu0/cpufreq/scaling_governor").exists()) {
            try {
                val bufferedReader = BufferedReader(FileReader(File("/sys/devices/system/cpu/cpu0/cpufreq/scaling_governor")))
                val readLine = bufferedReader.readLine()
                bufferedReader.close()
                return readLine
            } catch (e: IOException) {
                e.printStackTrace()
            }
        }
        return context.getString(R.string.base_unknown)
    }

    private fun findCpuAbis(): String {
        val supportedABIS = Build.SUPPORTED_ABIS.toList()
        return supportedABIS.joinToString(separator = ", ")
    }

    private fun findCpuFreqRange(): String {
        val cpuCores = Runtime.getRuntime().availableProcessors()
        val frequencyRanges = mutableListOf<RangeData>()

        for (i in 0 until cpuCores) {
            val minFreqFile = File("/sys/devices/system/cpu/cpu$i/cpufreq/cpuinfo_min_freq")
            val maxFreqFile = File("/sys/devices/system/cpu/cpu$i/cpufreq/cpuinfo_max_freq")
            val minFreq = getFileFrequency(minFreqFile)
            val maxFreq = getFileFrequency(maxFreqFile)

            frequencyRanges.add(RangeData(minFreq, maxFreq))
        }

        val minFrequency = frequencyRanges.minOf { it.minFreq }
        val maxFrequency = frequencyRanges.maxOf { it.maxFreq }

        return "$minFrequency - $maxFrequency"
    }

    private fun findCpuFreq(): String {
        val cpuCores = Runtime.getRuntime().availableProcessors()
        val frequencyRanges = mutableMapOf<RangeData, Int>()

        for (i in 0 until cpuCores) {
            val minFreqFile = File("/sys/devices/system/cpu/cpu$i/cpufreq/cpuinfo_min_freq")
            val maxFreqFile = File("/sys/devices/system/cpu/cpu$i/cpufreq/cpuinfo_max_freq")

            if (minFreqFile.exists() && maxFreqFile.exists()) {
                val minFreq = getFileFrequency(minFreqFile)
                val maxFreq = getFileFrequency(maxFreqFile)
                val frequencyRange = RangeData(minFreq, maxFreq)
                val count = frequencyRanges[frequencyRange] ?: 0
                frequencyRanges[frequencyRange] = count + 1
            }
        }
        val sortedFrequencies = frequencyRanges.entries.sortedBy { it.value }
        val formattedFrequencies = sortedFrequencies.joinToString("\n") {
            val (frequencyRange, count) = it
            "$count x ${frequencyRange.minFreq} - ${frequencyRange.maxFreq}"
        }

        return formattedFrequencies
    }

    fun findCpuCurrentFreqList(): MutableList<String>{
        val count = Runtime.getRuntime().availableProcessors()
        val freqList = mutableListOf<String>()
        for (i in 0 until count){
            val fre = findCurrentCpuFreq(i)
            if (fre != null){
                freqList.add(fre)
            }
        }
        return freqList
    }

    private fun findCurrentCpuFreq(core: Int): String? {
        return try {
            val file = File("/sys/devices/system/cpu/cpu$core/cpufreq/scaling_cur_freq")
            if (file.exists()) {
                getFileFrequency(file)
            } else {
                null
            }
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }

    private fun getFileFrequency(file: File,divValue: Int = 1000): String {
        var frequency = BaseApplication.mAppContext.getString(R.string.base_unknown)
        try {
            BufferedReader(FileReader(file)).use { reader ->
                frequency = reader.readLine()?.toIntOrNull()?.div(divValue)?.toString() + "MHz"
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }

        return frequency
    }
}

data class RangeData(val minFreq: String, val maxFreq: String)

data class CpuInfoData(
    val cpuFreq: String = "",
    val cpuFreqRange: String = "",
    val abis: String = "",
    val cpuHardware: String = "",
    val cpuGovernor: String = "",
    val processorName: String = "",
    val cpuCores: String = ""
)

data class GpuInfoData(
    val vulkan: String = "",
    val freq: String = "",
    val currFreq: String = ""
)