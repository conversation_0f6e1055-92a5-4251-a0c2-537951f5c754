package com.example.newdc.modelutil

import android.content.Context
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.newdc.modelutil.dev.BatteryTool
import kotlinx.coroutines.launch

class DevBatteryViewModel: ViewModel() {
    private val _info_batt_capacity = MutableLiveData<String>()
    private val _info_batt_current = MutableLiveData<String>()
    private val _info_batt_temp = MutableLiveData<String>()
    private val _info_batt_health = MutableLiveData<String>()
    private val _info_batt_source = MutableLiveData<String>()
    private val _info_batt_voltage = MutableLiveData<String>()
    private val _info_batt_tecnology = MutableLiveData<String>()
    private val _info_batt_charge = MutableLiveData<String>()
    private val _info_batt_per = MutableLiveData<String>()

    val getBattCapacity: LiveData<String> = _info_batt_capacity
    val getBattCurrent: LiveData<String> = _info_batt_current
    val getBattTemp: LiveData<String> = _info_batt_temp
    val getBattHealth: LiveData<String> = _info_batt_health
    val getBattSource: LiveData<String> = _info_batt_source
    val getBattVoltage: LiveData<String> = _info_batt_voltage
    val getBattTecnology: LiveData<String> = _info_batt_tecnology
    val getBattCharge: LiveData<String> = _info_batt_charge
    val getBattPer: LiveData<String> = _info_batt_per

    fun findBatteryInfo(context: Context){
        viewModelScope.launch {
            val batteryInfo = BatteryTool.getBatteryInfo(context)
            _info_batt_capacity.postValue(batteryInfo.capability)
            _info_batt_temp.postValue(batteryInfo.temperature)
            _info_batt_current.postValue(batteryInfo.batteryCurrent)
            _info_batt_health.postValue(batteryInfo.healthStatus)
            _info_batt_source.postValue(batteryInfo.pluggedStatus)
            _info_batt_voltage.postValue(batteryInfo.voltage)
            _info_batt_tecnology.postValue(batteryInfo.technology)
            _info_batt_voltage.postValue(batteryInfo.voltage)
            _info_batt_per.postValue(batteryInfo.level)
            _info_batt_charge.postValue(batteryInfo.chargeStatus)
        }
    }

}