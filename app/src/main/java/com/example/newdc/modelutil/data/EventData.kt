package com.example.newdc.modelutil.data

object EventData {
    const val EVENT_HOME_TAB_TRANSLATE = "event_home_tab_translate"
    const val EVENT_FILE_CHANGED = "event_file_changed"
    const val EVENT_DIR_ENTER = "event_dir_enter"
    const val EVENT_FILE_DELETE = "event_file_delete"
    const val EVENT_CONNECT_CHANGE = "event_connect_change"
    const val EVENT_RETURN_BACK = "event_return_back"
    const val EVENT_AD_INIT = "event_ad_init"
}