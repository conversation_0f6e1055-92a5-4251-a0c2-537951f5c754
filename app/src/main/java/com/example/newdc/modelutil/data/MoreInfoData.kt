package com.example.newdc.modelutil.data

import android.content.Context
import android.os.Parcelable
import com.example.newdc.R
import com.example.newdc.content.activity.BaseBrowserActivity
import kotlinx.parcelize.Parcelize

@Parcelize
data class MoreInfoData(
    val type : Int,
    val name : String,
    val iconRes : Int,
    val backgroundColor : Int
): Parcelable {
    companion object{
        fun getHomeMoreInfoData(context: Context) : MutableList<MoreInfoData>{
            return mutableListOf(
                MoreInfoData(1,context.getString(R.string.title_device),R.mipmap.icon_device,R.drawable.bg_item_device),
                MoreInfoData(2,context.getString(R.string.title_system),R.mipmap.icon_system,R.drawable.bg_item_system),
                MoreInfoData(3,context.getString(R.string.title_display),R.mipmap.icon_display,R.drawable.bg_item_display),
                MoreInfoData(4,context.getString(R.string.title_network),R.mipmap.icon_network,R.drawable.bg_item_network),
                MoreInfoData(5,context.getString(R.string.title_cpu),R.mipmap.icon_cpu,R.drawable.bg_item_cpu),
                MoreInfoData(6,context.getString(R.string.title_battery),R.mipmap.icon_battery,R.drawable.bg_item_battery),
                MoreInfoData(7,context.getString(R.string.title_apps),R.mipmap.icon_app,R.drawable.bg_item_app),
                MoreInfoData(8,context.getString(R.string.title_tests),R.mipmap.icon_test,R.drawable.bg_item_test)
            )
        }

        fun getStorageFuncData(context: Context) : MutableList<MoreInfoData>{
            return mutableListOf(
                MoreInfoData(BaseBrowserActivity.TYPE_BROWSER_IMAGE,context.getString(R.string.base_images),R.mipmap.icon_func_image,R.drawable.bg_func_image),
                MoreInfoData(BaseBrowserActivity.TYPE_BROWSER_VIDEO,context.getString(R.string.base_videos),R.mipmap.icon_func_vedio,R.drawable.bg_func_video),
                MoreInfoData(BaseBrowserActivity.TYPE_BROWSER_AUDIO,context.getString(R.string.base_audios),R.mipmap.icon_func_audio,R.drawable.bg_func_audio),
                MoreInfoData(BaseBrowserActivity.TYPE_BROWSER_DOCS,context.getString(R.string.base_docs),R.mipmap.icon_func_doc,R.drawable.bg_func_doc),
                MoreInfoData(BaseBrowserActivity.TYPE_BROWSER_ZIPS,context.getString(R.string.base_zips),R.mipmap.icon_func_zip,R.drawable.bg_func_zip)
            )
        }
    }
}