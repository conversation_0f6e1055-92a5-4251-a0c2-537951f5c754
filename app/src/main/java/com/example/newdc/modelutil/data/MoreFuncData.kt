package com.example.newdc.modelutil.data

import android.os.Parcelable
import com.example.newdc.R
import com.example.newdc.content.activity.BaseBrowserActivity
import kotlinx.parcelize.Parcelize

@Parcelize
data class MoreFuncData(
    val title: Int,
    val description: Int,
    val icon: Int,
    val type: Int
): Parcelable {
    companion object{
        val FUNC_REDUNDANT = MoreFuncData(R.string.title_brow_redundant,R.string.sto_redundant_info,R.mipmap.icon_func_redudant,BaseBrowserActivity.TYPE_BROWSER_REDUNDANT)
        val FUNC_LARGE = MoreFuncData(R.string.title_brow_large,R.string.sto_large_info,R.mipmap.icon_func_large,BaseBrowserActivity.TYPE_BROWSER_LARGE)
        val FUNC_DUPLICATE = MoreFuncData(R.string.title_brow_duplicate,R.string.sto_duplicate_info,R.mipmap.icon_func_dupiicate,BaseBrowserActivity.TYPE_BROWSER_DUPLICATE)
        val FUNC_RECENT = MoreFuncData(R.string.title_brow_recent,R.string.sto_recent_info,R.mipmap.icon_func_recent,BaseBrowserActivity.TYPE_BROWSER_RECENT)
        val FUNC_STORAGE = MoreFuncData(R.string.title_fun_storage,R.string.sto_storage_info,R.mipmap.icon_storage,BaseBrowserActivity.TYPE_FUNC_STORAGE)
        val FUNC_RAM = MoreFuncData(R.string.title_func_ram,R.string.sto_ram_info,R.mipmap.icon_func_ram,BaseBrowserActivity.TYPE_FUNC_RAM)


        fun getCompleteFuncList(): MutableList<MoreFuncData>{
            val funList = mutableListOf<MoreFuncData>().apply {
                add(FUNC_REDUNDANT)
                add(FUNC_LARGE)
                add(FUNC_DUPLICATE)
                add(FUNC_RECENT)
                add(FUNC_STORAGE)
                add(FUNC_RAM)
            }
            funList.shuffle()
            return funList
        }
    }
}