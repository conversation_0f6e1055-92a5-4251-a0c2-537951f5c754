package com.example.newdc.modelutil

import android.content.Context
import android.os.Build
import android.os.Environment
import android.text.format.Formatter
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.newdc.modelutil.dev.DevInfoTool
import com.example.newdc.modelutil.dev.HardInfoTool
import com.example.newdc.modelutil.dev.OtherSysInfoTool
import com.example.newdc.modelutil.dev.PathAndRamUtil
import kotlinx.coroutines.launch

class DevValueViewModel: ViewModel() {
    private val _info_dev_brand = MutableLiveData<String>()
    private val _info_dev_device = MutableLiveData<String>()
    private val _info_dev_model = MutableLiveData<String>()
    private val _info_sys_code = MutableLiveData<String>()
    private val _info_sys_name = MutableLiveData<String>()
    private val _info_sys_api = MutableLiveData<String>()
    private val _status_cpu_rates = MutableLiveData<MutableList<String>>()

    private val _value_storage_per = MutableLiveData<Int>()
    private val _value_sotrage_total = MutableLiveData<String>()
    private val _value_sotrage_used = MutableLiveData<String>()
    private val _value_ram_per = MutableLiveData<Int>()
    private val _value_ram_total = MutableLiveData<String>()
    private val _value_ram_used = MutableLiveData<String>()
    private val _value_ram_avail = MutableLiveData<String>()
    private val _value_ram_raw_used = MutableLiveData<Long>()
    private val _value_ram_raw_avail = MutableLiveData<Long>()
    private val _value_ram_raw_total = MutableLiveData<Long>()

    val getInfoDevBrand: LiveData<String> = _info_dev_brand
    val getInfoDevDevice: LiveData<String> = _info_dev_device
    val getInfoDevModel: LiveData<String> = _info_dev_model
    val getInfoSysCode: LiveData<String> = _info_sys_code
    val getInfoSysName: LiveData<String> = _info_sys_name
    val getInfoSysApi: LiveData<String> = _info_sys_api

    val getValueStoragePercent: LiveData<Int> = _value_storage_per
    val getValueStorageTotal: LiveData<String> = _value_sotrage_total
    val getValueStorageUsed: LiveData<String> = _value_sotrage_used
    val getValueRamPercent: LiveData<Int> = _value_ram_per
    val getValueRamTotal: LiveData<String> = _value_ram_total
    val getValueRamUsed: LiveData<String> = _value_ram_used
    val getValueRamAvail: LiveData<String> = _value_ram_avail
    val getValueRamRawUsed: LiveData<Long> = _value_ram_raw_used
    val getValueRamRawAvail: LiveData<Long> = _value_ram_raw_avail

    val getStatusCpuRates: MutableLiveData<MutableList<String>> = _status_cpu_rates

    fun getHomeInfoBoard(){
        viewModelScope.launch {
            _info_dev_brand.postValue(Build.BRAND)
            _info_dev_device.postValue(DevInfoTool.findDevNameFromManu())
            _info_dev_model.postValue(Build.MODEL)
            val api = Build.VERSION.SDK_INT
            _info_sys_code.postValue("ANDROID ${OtherSysInfoTool.androidVerCode(api)}")
            _info_sys_name.postValue("Android ${OtherSysInfoTool.androidVerCode(api)} ${OtherSysInfoTool.androidVerName(api)}")
            _info_sys_api.postValue("API $api")
        }
    }

    fun getHomeRamAndRomValue(context: Context){
        getStorageValue(context)
        getRamValue(context)
    }

    fun getRealtimeStatus(context: Context){
        getRamValue(context)
        _status_cpu_rates.postValue(HardInfoTool.findCpuCurrentFreqList())
    }

    fun getCpuRate(context: Context){
        _status_cpu_rates.postValue(HardInfoTool.findCpuCurrentFreqList())
    }

    private fun getStorageValue(context: Context){
        viewModelScope.launch {
            val exPath = Environment.getExternalStorageDirectory().absolutePath
            val storageTotal = PathAndRamUtil.getPathTotalBlock(exPath)
            val storageAvail = PathAndRamUtil.getPathAvailBlock(exPath)
            val storageUsed = storageTotal - storageAvail
            val percent = try {
                (storageUsed * 100 / storageTotal).toInt()
            }catch (e: Exception){
                0
            }
            _value_sotrage_total.postValue(Formatter.formatFileSize(context,storageTotal))
            _value_sotrage_used.postValue(Formatter.formatFileSize(context,storageUsed))
            _value_storage_per.postValue(percent)
        }
    }

    private fun getRamValue(context: Context){
        viewModelScope.launch {
            val ramValue = PathAndRamUtil.getRamAvailAndTotal(context)
            val ramTotal = ramValue.second
            val ramAvail = ramValue.first
            val ramUsed = ramTotal - ramAvail
            val percent = try {
                (ramUsed * 100 / ramTotal).toInt()
            }catch (e: Exception){
                0
            }
            _value_ram_total.postValue(Formatter.formatFileSize(context,ramTotal))
            _value_ram_avail.postValue(Formatter.formatFileSize(context,ramAvail))
            _value_ram_used.postValue(Formatter.formatFileSize(context,ramUsed))
            _value_ram_per.postValue(percent)
            _value_ram_raw_used.postValue(ramUsed)
            _value_ram_raw_avail.postValue(ramAvail)
            _value_ram_raw_total.postValue(ramTotal)
        }
    }
}