package com.example.newdc.modelutil

import android.content.Context
import android.os.Build
import android.os.Environment
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.newdc.content.activity.BaseBrowserActivity
import com.example.newdc.modelutil.data.ItemFileData
import com.example.newdc.modelutil.data.ReItemGroupData
import com.example.newdc.modelutil.tool.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.io.File

class FilesStorageViewModel: ViewModel() {
    private var _browserDirList = MutableLiveData<MutableList<ItemFileData>>()
    private var _browserLargeList = MutableLiveData<MutableList<ItemFileData>>()
    private var _browserDuplicateList = MutableLiveData<MutableList<ItemFileData>>()
    private var _browserRecentList = MutableLiveData<MutableList<ItemFileData>>()
    private var _browserMediaList = MutableLiveData<MutableList<ItemFileData>>()
    private var _browserOtherList = MutableLiveData<MutableList<ItemFileData>>()
    private var _browserDocsList = MutableLiveData<MutableList<ItemFileData>>()
    private var _browserRedundantList = MutableLiveData<MutableList<ReItemGroupData>>()

    var getBrowserDirList: LiveData<MutableList<ItemFileData>> = _browserDirList
    var getBrowserLargeList: LiveData<MutableList<ItemFileData>> = _browserLargeList
    var getBrowserDuplicateList: LiveData<MutableList<ItemFileData>> = _browserDuplicateList
    var getBrowserRecentList: LiveData<MutableList<ItemFileData>> = _browserRecentList
    var getBrowserMediaList: LiveData<MutableList<ItemFileData>> = _browserMediaList
    var getBrowserOrderList: LiveData<MutableList<ItemFileData>> = _browserOtherList
    var getBrowserDocsList: LiveData<MutableList<ItemFileData>> = _browserDocsList
    var getBrowserRedundantList: LiveData<MutableList<ReItemGroupData>> = _browserRedundantList


    fun getSelectPathFiles(path: String){
        viewModelScope.launch(Dispatchers.IO) {
            val dirList = MediaStorageTool.findCurrentPathItem(path)
            _browserDirList.postValue(dirList)
        }
    }

    fun getLargeFiles(context: Context,fileSize: Long){
        viewModelScope.launch(Dispatchers.IO) {
            var largeFileList = mutableListOf<ItemFileData>()
            try {
                largeFileList = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R){
                     MediaStorageTool.findLargeFile(context,fileSize)
                }else{
                    FilesTool.findFilesBySize(context,largeFileList, File(Environment.getExternalStorageDirectory().absolutePath),fileSize)
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
            _browserLargeList.postValue(largeFileList)
        }
    }

    fun getRecentFile(context: Context){
        viewModelScope.launch(Dispatchers.IO) {
            val recentList = mutableListOf<ItemFileData>()
            FilesTool.findRecentList(context,recentList,Environment.getExternalStorageDirectory().absolutePath,System.currentTimeMillis())
            _browserRecentList.postValue(recentList)
        }
    }

    fun getDuplicateFile(context: Context){
        viewModelScope.launch(Dispatchers.IO) {
            val duplicateList = DuplicateUtil.findDuplicatesFiles(context)
            _browserDuplicateList.postValue(duplicateList)
        }
    }

    fun getMediaFileData(context: Context,isVideo: Boolean = false){
        viewModelScope.launch(Dispatchers.IO) {
            val mediaList = if (isVideo){
                MediaStorageTool.findVideoFile(context)
            }else{
                MediaStorageTool.findImageFile(context)
            }
            val orderList = OrderTool.mediaOrderByTime(mediaList)
            _browserMediaList.postValue(orderList)
        }
    }

    fun getOtherFileData(context: Context,fileType: Int){
        viewModelScope.launch(Dispatchers.IO) {
            val fileList = when(fileType){
                BaseBrowserActivity.TYPE_BROWSER_AUDIO ->{
                    MediaStorageTool.findAudioFile(context)
                }
                BaseBrowserActivity.TYPE_BROWSER_ZIPS ->{
                    try {
                        FilesTool.findZipFile(context, mutableListOf(),File(Environment.getExternalStorageDirectory().absolutePath))
                    }catch (e: Exception){
                        MediaStorageTool.findZipFile(context)
                    }
                }
                else -> {MediaStorageTool.findAudioFile(context)}
            }
            _browserOtherList.postValue(fileList)
        }
    }

    fun getDocsFileData(context: Context){
        viewModelScope.launch(Dispatchers.IO) {
            val docsList = MediaStorageTool.findDocFile(context)
            _browserDocsList.postValue(docsList)
        }
    }

    fun getRedundantFileData(context: Context){
        viewModelScope.launch(Dispatchers.IO) {
            val redundantList = RedundantTool.startCollectRedundantFile(context)
            _browserRedundantList.postValue(redundantList)
        }
    }
}