package com.example.newdc.modelutil.tool

import android.util.Base64
import com.example.newdc.BuildConfig
import java.nio.ByteBuffer
import java.security.SecureRandom
import javax.crypto.Cipher
import javax.crypto.spec.IvParameterSpec
import javax.crypto.spec.SecretKeySpec

object EncodeTool {
    private const val AES_MODE = "AES/CBC/PKCS5Padding"
    private const val IV_LENGTH = 16
    const val BASE_KEY = BuildConfig.encrype_key
    const val CONFIG_KEY = BuildConfig.config_decrype_key

    fun encrypt(secretKey: String, plainText: String): String {
        var initVector: String? = null
        return try {
            // Check secret length
            if (!isKeyLengthValid(secretKey)) {
                throw Exception("Secret key's length must be 128, 192 or 256 bits")
            }

            // Get random initialization vector
            val secureRandom = SecureRandom()
            var initVectorBytes = ByteArray(IV_LENGTH / 2)
            secureRandom.nextBytes(initVectorBytes)
            initVector = bytesToHex(initVectorBytes)
            initVectorBytes = initVector.toByteArray(charset("UTF-8"))
            val ivParameterSpec = IvParameterSpec(initVectorBytes)
            val secretKeySpec = SecretKeySpec(secretKey.toByteArray(charset("UTF-8")), "AES")
            val cipher: Cipher = Cipher.getInstance(AES_MODE)
            cipher.init(Cipher.ENCRYPT_MODE, secretKeySpec, ivParameterSpec)

            // Encrypt input text
            val encrypted: ByteArray = cipher.doFinal(plainText.toByteArray(charset("UTF-8")))
            val byteBuffer: ByteBuffer =
                ByteBuffer.allocate(initVectorBytes.size + encrypted.size)
            byteBuffer.put(initVectorBytes)
            byteBuffer.put(encrypted)

            // Result is base64-encoded string: initVector + encrypted result
            return Base64.encodeToString(byteBuffer.array(), Base64.DEFAULT).replace("[\\s*\t\n\r]".toRegex(), "")
        } catch (e: Exception) {
            e.printStackTrace()
            ""
        }
    }

    fun decrypt(secretKey: String, cipherText: String?): String {
        return try {
            // Check secret length
            if (!isKeyLengthValid(secretKey)) {
                throw Exception("Secret key's length must be 128, 192 or 256 bits")
            }

            // Get raw encoded data
            val encrypted: ByteArray = Base64.decode(cipherText, Base64.DEFAULT)

            // Slice initialization vector
            val ivParameterSpec = IvParameterSpec(encrypted, 0, IV_LENGTH)
            // Set secret password
            val secretKeySpec = SecretKeySpec(secretKey.toByteArray(charset("UTF-8")), "AES")
            val cipher: Cipher = Cipher.getInstance("AES/CBC/PKCS5Padding")
            cipher.init(Cipher.DECRYPT_MODE, secretKeySpec, ivParameterSpec)

            return String(cipher.doFinal(encrypted, IV_LENGTH, encrypted.size - IV_LENGTH))
        } catch (e: Exception) {
            e.printStackTrace()
            ""
        }
    }

    fun baseDecrypt(key: String, encryptedData: String): String {
        val keySpec = SecretKeySpec(key.toByteArray(), "AES")
        val ivSpec = IvParameterSpec(key.toByteArray())
        val cipher = Cipher.getInstance(AES_MODE)
        cipher.init(Cipher.DECRYPT_MODE, keySpec, ivSpec)
        val originalData = cipher.doFinal(Base64.decode(encryptedData, Base64.DEFAULT))
        return String(originalData)
    }

    private fun isKeyLengthValid(key: String): Boolean {
        return key.length == 16 || key.length == 24 || key.length == 32
    }

    private val hexArray = "0123456789ABCDEF".toCharArray()
    private fun bytesToHex(bytes: ByteArray): String {
        val hexChars = CharArray(bytes.size * 2)
        for (j in bytes.indices) {
            val v = bytes[j].toInt() and 0xFF
            hexChars[j * 2] = hexArray[v ushr 4]
            hexChars[j * 2 + 1] = hexArray[v and 0x0F]
        }
        return String(hexChars)
    }
}