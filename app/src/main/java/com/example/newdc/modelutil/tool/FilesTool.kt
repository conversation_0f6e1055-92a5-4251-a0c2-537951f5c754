package com.example.newdc.modelutil.tool

import android.content.ClipData.Item
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.storage.StorageManager
import android.util.Log
import android.widget.Toast
import androidx.core.content.FileProvider
import com.example.newdc.BaseApplication
import com.example.newdc.R
import com.example.newdc.modelutil.data.ItemFileData
import com.example.newdc.modelutil.tool.MediaStorageTool.findTypeFromMime
import java.io.BufferedReader
import java.io.File
import java.io.InputStreamReader
import java.util.*
import kotlin.collections.ArrayList
import kotlin.math.abs

object FilesTool {

    fun findFilesSize(file: File): Long {
        var size: Long = 0
        if (file.isDirectory) {
            val files = file.listFiles()
            if (files != null) {
                for (child in files) {
                    size += findFilesSize(child)
                }
            }
        } else {
            size = file.length()
        }

        return size
    }

    fun findPathSplit(mPath: String, root: String): Array<String> {
        var path = mPath
        if (path.endsWith("/")) {
            path = path.substring(0, path.length - 1)
        }
        path = path.trim { it <= ' ' }
        val paths = ArrayList<String>()
        var urlPrefix: String? = null
        val splitUri = pathSplitToUri(path)
        if (splitUri != null) {
            urlPrefix = splitUri.first
            path = splitUri.second
        }
        if (!path.startsWith("/")) {
            path = "/$path"
        }
        while (path.isNotEmpty()) {
            if (urlPrefix != null) {
                paths.add(urlPrefix + path)
            } else {
                paths.add(path)
            }
            path = if (path == root) {
                break
            } else if (path.contains("/")) {
                path.substring(0, path.lastIndexOf('/'))
            } else {
                break
            }
        }
        if (urlPrefix != null) {
            paths.add(urlPrefix)
        } else {
            paths.add("/")
        }
        paths.reverse()
        return paths.toTypedArray()
    }

    private fun pathSplitToUri(path: String): Pair<String, String>? {
        val uri = Uri.parse(path)
        return if (uri.scheme != null) {
            val urlPrefix = uri.scheme + "://" + uri.encodedAuthority
            val retPath = path.substring(urlPrefix.length)
            Pair(urlPrefix, retPath)
        } else {
            null
        }
    }

    fun findFolderFromPath(path: String, rootPath: String): MutableList<String> {
        var mPath = ""
        val split = path.split(rootPath.toRegex()).dropLastWhile { it.isEmpty() }
            .toTypedArray()
        mPath = if (split.isNotEmpty()) {
            if (split.size >= 2) split[1] else split[0]
        } else {
            path
        }
        if (!path.endsWith("/")) {
            mPath += "/"
        }
        val splitUri = pathSplitToUri(mPath)
        if (splitUri != null) {
            mPath = splitUri.second
        }
        val tempList = mPath.split("/".toRegex()).dropLastWhile { it.isEmpty() }
            .toTypedArray()
        val pathList = ArrayList<String>()
        for (item in tempList) {
            if (item.trim { it <= ' ' }.isNotEmpty()) {
                pathList.add(item)
            }
        }
        return pathList
    }

    fun findMountName(root: String): String? {
        val mStorageManger = BaseApplication.mAppContext.getSystemService(Context.STORAGE_SERVICE) as StorageManager
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            val volumes = mStorageManger.storageVolumes
            try {
                for (volume in volumes) {
                    var path: String
                    if (Build.VERSION.SDK_INT < 30) {
                        val clazz = Class.forName(volume.javaClass.name)
                        val getPath = clazz.getDeclaredMethod("getPath")
                        getPath.isAccessible = true
                        path = getPath.invoke(volume) as String
                    } else {
                        path = volume.directory!!.absolutePath
                    }
                    if (root == path) {
                        return volume.getDescription(BaseApplication.mAppContext)
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
        return null
    }

    fun getFileMime(context: Context,file: File): String?{
        var mime: String? = null
        try {
            val uri = findFileUri(context, file)
            val resolver = context.contentResolver
            if (uri != null) mime = resolver.getType(uri)
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return mime
    }

    fun findFileUri(context: Context, file: File): Uri? {
        var uri: Uri? = null
        try {
            if (file.exists() && file.isFile) {
                uri = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                    FileProvider.getUriForFile(
                        context,
                        context.packageName + ".fileProvider",
                        file
                    )
                } else {
                    Uri.fromFile(file)
                }
            }
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        }
        return uri
    }

    fun findFilesBySize(context: Context,fileList: MutableList<ItemFileData>, file: File, maxLarge: Long) :MutableList<ItemFileData> {
        if (file.exists()) {
            if (file.isDirectory) {
                val files = file.listFiles()
                if (files != null && files.isNotEmpty()) {
                    for (file1 in files) {
                        if (file1.isDirectory) {
                            findFilesBySize(context,fileList, file1, maxLarge)
                        } else {
                            if (file1.length() > maxLarge) {
                                val size = findFilesSize(file1)
                                val mime = getFileMime(context, file1)
                                val type = findTypeFromMime(mime)
                                val item = ItemFileData(
                                    title = file1.name, fileSize = size, filePath =  file1.absolutePath, modifyDate = file1.lastModified(),
                                    type = type, typeIcon = 0, mime = mime)
                                fileList.add(item)
                            }
                        }
                    }
                }
            } else {
                if (file.length() > maxLarge) {
                    val size = findFilesSize(file)
                    val mime = getFileMime(context, file)
                    val type = findTypeFromMime(mime)
                    val item = ItemFileData(
                        title = file.name, fileSize = size, filePath =  file.absolutePath, modifyDate = file.lastModified(),
                        type = type, typeIcon = 0, mime = mime)
                    fileList.add(item)
                }
            }
        }
        return fileList
    }

    fun findRecentList(context: Context,fileList: MutableList<ItemFileData>, path: String, currentTime: Long): MutableList<ItemFileData> {
        try {
            val pathFile = File(path)
            if (pathFile.exists()) {
                if (pathFile.isDirectory) {
                    val files = pathFile.listFiles()
                    if (files != null && files.isNotEmpty()) {
                        for (file in files) {
                            if (file.isDirectory) {
                                findRecentList(context,fileList,file.absolutePath,currentTime)
                            } else {
                                if (abs(currentTime - file.lastModified()) <= 3 * 24 * 60 * 60 * 1000){
                                    val size = findFilesSize(file)
                                    val mime = getFileMime(context, file)
                                    val type = findTypeFromMime(mime)
                                    val item = ItemFileData(
                                        title = file.name, fileSize = size, filePath =  file.absolutePath, modifyDate = file.lastModified(),
                                        type = type, typeIcon = 0, mime = mime)
                                    fileList.add(item)
                                }
                            }
                        }
                    }
                }else {
                    if (abs(currentTime - pathFile.lastModified()) <= 3 * 24 * 60 * 60 * 1000){
                        val size = findFilesSize(pathFile)
                        val mime = getFileMime(context, pathFile)
                        val type = findTypeFromMime(mime)
                        val item = ItemFileData(
                            title = pathFile.name, fileSize = size, filePath =  pathFile.absolutePath, modifyDate = pathFile.lastModified(),
                            type = type, typeIcon = 0, mime = mime)
                        fileList.add(item)
                    }
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return fileList
    }

    fun findZipFile(context: Context,fileList: MutableList<ItemFileData>, file: File): MutableList<ItemFileData> {
        if (file.exists()) {
            if (file.isDirectory) {
                val files = file.listFiles()
                if (files != null && files.isNotEmpty()) {
                    for (file1 in files) {
                        if (file1.isDirectory) {
                            findZipFile(context,fileList, file1)
                        } else {
                            if (file1.name.lowercase(Locale.getDefault()).endsWith(".zip")) {
                                val size = findFilesSize(file1)
                                val mime = getFileMime(context, file1)
                                val type = findTypeFromMime(mime)
                                val item = ItemFileData(
                                    title = file1.name, fileSize = size, filePath =  file1.absolutePath, modifyDate = file1.lastModified(),
                                    type = type, typeIcon = 0, mime = mime)
                                fileList.add(item)
                            }
                        }
                    }
                }
            } else {
                if (file.name.lowercase(Locale.getDefault()).endsWith(".zip")) {
                    val size = findFilesSize(file)
                    val mime = getFileMime(context, file)
                    val type = findTypeFromMime(mime)
                    val item = ItemFileData(
                        title = file.name, fileSize = size, filePath =  file.absolutePath, modifyDate = file.lastModified(),
                        type = type, typeIcon = 0, mime = mime)
                    fileList.add(item)
                }
            }
        }
        return fileList
    }

    fun deletePath(path: String?): Boolean {
        if (path == null) return false
        try {
            val delFile = File(path)
            if (delFile.exists()) {
                if (delFile.isDirectory) {
                    val files = delFile.listFiles()
                    if (files != null && files.isNotEmpty()) {
                        for (file in files) {
                            if (file.isDirectory) {
                                deletePath(file.absolutePath)
                            } else {
                                return deleteFile(file)
                            }
                        }
                    }
                } else {
                    return deleteFile(delFile)
                }
            }
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        }
        return false
    }

    fun shareFile(context: Context, path: String?) {
        if (path == null) return
        try {
            val shareFile = File(path)
            if (shareFile.exists()) {
                val sIntent = Intent(Intent.ACTION_SEND)
                val uri = findFileUri(context, shareFile)
                val mime = getFileMime(context, shareFile)
                sIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                sIntent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                sIntent.putExtra(Intent.EXTRA_STREAM, uri)
                if (mime != null) {
                    sIntent.type = mime
                } else {
                    sIntent.type = "=*/*"
                }
                if (sIntent.resolveActivity(context.packageManager) != null) {
                    context.startActivity(Intent.createChooser(sIntent, "share"))
                }
            }
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        }
    }

    private fun deleteFile(file: File): Boolean {
        return if (file.exists()) {
            file.delete()
        } else false
    }

    fun filePathRename(file: File, nName: String?): Boolean {
        var status = false
        val oldPath = file.path
        val prePath = oldPath.substring(0, oldPath.lastIndexOf("/") + 1)
        if (file.exists() && nName != null) {
            status = file.renameTo(File(prePath + nName))

        }
        return status
    }


    fun getJsonFromAssets(context: Context, fileName: String): String {
        val assetManager = context.assets
        var json: String? = null
        try {
            val inputStream = assetManager.open(fileName)
            val inputStreamReader = InputStreamReader(inputStream)
            val bufferedReader = BufferedReader(inputStreamReader)
            var line: String?
            val stringBuilder = StringBuilder()
            while (bufferedReader.readLine().also { line = it } != null) {
                stringBuilder.append(line)
            }
            json = stringBuilder.toString()
            inputStream.close()
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return json ?: ""
    }

}