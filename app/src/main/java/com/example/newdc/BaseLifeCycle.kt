package com.example.newdc

import android.app.Activity
import android.app.Application
import android.app.NotificationManager
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import androidx.fragment.app.FragmentActivity
import com.additional.biservice.BiServiceClass
import com.additional.biservice.ParamConstants
import com.additional.firebase.FirebaseEventPost
import com.additional.max.MaxIntersAdManager
import com.example.newdc.content.activity.MainActivity
import com.example.newdc.content.activity.StarterActivity
import com.example.newdc.modelutil.data.StoreKeyData
import com.example.newdc.modelutil.tool.ExtraTool.isLocked
import com.example.newdc.modelutil.tool.ExtraTool.isScreenOn
import com.example.newdc.modelutil.tool.PermissionTool
import com.example.newdc.service.notify.NotifyConfig
import com.example.newdc.service.notify.PostHeadHandle
import com.example.newdc.service.notify.TopConstantService

class BaseLifeCycle: Application.ActivityLifecycleCallbacks {
    companion object{
        private var layoutCount = 0
        private var firstUp = true
        var splashFlag = false

        const val BUNDLE_NOTIFY_ID = "bundle_notify_id"
        const val BUNDLE_NOTIFY_INTENT = "bundle_notify_intent"
        const val BUNDLE_NOTIFY_ENTER_CODE = "bundle_notify_enter_code"
        const val BUNDLE_TRANS_HOME = "bundle_trans_home"
    }



    override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {
        val enterType = activity.intent?.getStringExtra(BUNDLE_NOTIFY_INTENT)
        val id = activity.intent?.getIntExtra(BUNDLE_NOTIFY_ID, -1)
        if (!enterType.isNullOrBlank()){
            val notifyManager = activity.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notifyManager.cancel(id!!)
            PostHeadHandle.getInstance(BaseApplication.mAppContext)?.sendEmptyMessage(PostHeadHandle.POST_CLOSE_MSG)
            when(enterType){
                NotifyConfig.TYPE_NOTY_RAM ->{FirebaseEventPost.event("type_click_ram")}
                NotifyConfig.TYPE_NOTY_TEMP ->{FirebaseEventPost.event("type_click_temp")}
                NotifyConfig.TYPE_NOTY_BATTERY ->{FirebaseEventPost.event("type_click_battery")}
                NotifyConfig.TYPE_NOTY_STORAGE ->{FirebaseEventPost.event("type_click_storage")}
                NotifyConfig.TYPE_NOTY_INFO ->{FirebaseEventPost.event("type_click_info")}
                NotifyConfig.TYPE_NOTY_REDUNDANT ->{FirebaseEventPost.event("type_click_redundant")}
                NotifyConfig.TYPE_NOTY_DUPLICATION ->{FirebaseEventPost.event("type_click_duplication")}
                NotifyConfig.TYPE_NOTY_LARGE ->{FirebaseEventPost.event("type_click_large")}
                NotifyConfig.TYPE_NOTY_RECENT ->{FirebaseEventPost.event("type_click_recent")}
                else -> {FirebaseEventPost.event("type_click_home")}
            }
            FirebaseEventPost.event("type_click_all")
        }

        installEventDetect()

    }

    override fun onActivityStarted(activity: Activity) {
        BiServiceClass().postStartEvent(ParamConstants.POST_EVENT_START,activity)
        if (!StoreKeyData.getBoolean(StoreKeyData.BI_RECORD_EVENT)){
            BiServiceClass().postCreateEvent()
        }

        if (StoreKeyData.recordTime()){
            StoreKeyData.restTodayConfig()
        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU && activity !is MainActivity
            && ( activity is StarterActivity && !StoreKeyData.getBoolean(StoreKeyData.BASE_KEY_AGREE_PRIVACY))) {
            (activity as? FragmentActivity)?.let { PermissionTool.requestNotifyPost(it) }
        }

        if (!firstUp && !splashFlag) {
            if (layoutCount <= 0
                && !BaseApplication.mAppContext.isLocked() && BaseApplication.mAppContext.isScreenOn()
                && ignoreActivity(activity) && !adHasShow(activity)
            ) {
                val intent = Intent(activity, StarterActivity::class.java).apply {
                    flags = Intent.FLAG_ACTIVITY_NEW_TASK
                    putExtra(BUNDLE_TRANS_HOME,true)
                }
                activity.startActivity(intent)
            }
        } else {
            if (ignoreActivity(activity)){
                firstUp = false
                splashFlag = false
            }
        }

        Handler(Looper.getMainLooper()).postDelayed({
            if ((Build.VERSION.SDK_INT < Build.VERSION_CODES.TIRAMISU || PermissionTool.checkPostNotifyPermission(BaseApplication.mAppContext))
                && !TopConstantService.serviceIsRunning(BaseApplication.mAppContext)){
                TopConstantService.startConstantNotify(BaseApplication.mAppContext)
            }
        },3000)

        ++ layoutCount
    }

    override fun onActivityResumed(activity: Activity) {

    }

    override fun onActivityPaused(activity: Activity) {
    }

    override fun onActivityStopped(activity: Activity) {
        -- layoutCount
    }

    override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {

    }

    override fun onActivityDestroyed(activity: Activity) {

    }

    @Synchronized
    private fun installEventDetect(){
        Handler(Looper.getMainLooper()).postDelayed({
            if (!StoreKeyData.getBoolean(StoreKeyData.BI_INSTALL_HAS)){
                BiServiceClass().postStartEvent(ParamConstants.POST_EVENT_INSTALL,null)
            }
        },2000)
    }

    private fun ignoreActivity(activity: Activity): Boolean{
        return activity.componentName.className.startsWith(BaseApplication.mAppContext.packageName,true)
                && activity !is StarterActivity
    }

    private fun adHasShow(activity: Activity): Boolean{
        if (activity !is StarterActivity){
            val intersManager = MaxIntersAdManager.adMaxInstance
            if (intersManager != null && intersManager.getAdIsShow()) return true
        }
        return false
    }
}