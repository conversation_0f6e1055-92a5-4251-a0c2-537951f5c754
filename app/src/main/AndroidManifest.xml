<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
          xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE"/>
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE"
                     tools:ignore="ScopedStorage"/>
    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES"
                     tools:ignore="QueryAllPackagesPermission" />
    <uses-permission android:name="com.google.android.gms.permission.AD_ID"/>

    <uses-permission android:name="android.permission.VIBRATE"/>
    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS"/>
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS"/>
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED"/>
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE"/>
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC"/>


    <application
            android:name=".BaseApplication"
            android:allowBackup="true"
            android:dataExtractionRules="@xml/data_extraction_rules"
            android:fullBackupContent="@xml/backup_rules"
            android:icon="@mipmap/ic_launcher"
            android:label="@string/app_name"
            android:roundIcon="@mipmap/ic_launcher_round"
            android:supportsRtl="true"
            android:theme="@style/Theme.NewDc"
            android:networkSecurityConfig="@xml/network_security_config"
            tools:replace="android:fullBackupContent,android:dataExtractionRules"
            tools:targetApi="31">

        <meta-data
                android:name="com.google.android.gms.ads.APPLICATION_ID"
                android:value="ca-app-pub-3940256099942544~3347511713"/>

        <activity
                android:name=".content.activity.MainActivity"
                android:exported="true"
                android:launchMode="singleTask">
        </activity>

        <activity android:name=".content.activity.StarterActivity"
                  android:exported="true"
                  android:launchMode="singleTop">
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>

                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
        </activity>

        <activity android:name=".content.activity.StorageActivity"
                  android:exported="false"
                  android:launchMode="singleTask"/>

        <activity android:name=".content.activity.RealtimeActivity"
                  android:exported="false"
                  android:launchMode="singleTask"/>

        <activity android:name=".content.fragment.info.ActivityBaseInfo"
                  android:exported="false"
                  android:launchMode="singleTask"/>

        <activity android:name=".content.activity.BaseBrowserActivity"
                  android:exported="false"
                  android:launchMode="singleTask"/>

        <activity android:name=".content.activity.FileRedundantActivity"
                  android:exported="false"
                  android:launchMode="singleTask"/>

        <activity android:name=".content.activity.AnimTransActivity"
                  android:exported="false"
                  android:launchMode="singleTask"/>

        <activity android:name=".content.activity.CompleteActivity"
                  android:exported="false"
                  android:launchMode="singleTask"/>

        <activity android:name=".content.activity.AppBrowActivity"
                  android:exported="false"
                  android:launchMode="singleTask"/>

        <activity android:name=".content.activity.ApplicationInfoActivity"
                  android:exported="false"
                  android:launchMode="singleTask"/>

        <activity android:name=".content.activity.ActivityTestDetail"
                  android:exported="false"
                  android:launchMode="singleTask"/>

        <activity android:name=".content.activity.TestActivity"
                  android:exported="false"
                  android:launchMode="singleTask"/>

        <activity android:name=".content.activity.PermissionManagerActivity"
                  android:exported="false"
                  android:launchMode="singleTask"/>

        <activity android:name=".content.activity.GuideNotifyActivity"
                  android:exported="false"
                  android:launchMode="singleTask"/>

        <service
                android:name="com.additional.firebase.MsgFCMService"
                android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>

        <service android:name=".service.FlashService"
                 android:exported="false"/>
        <service android:name=".service.notify.TopConstantService"
                 android:foregroundServiceType="dataSync"
                 android:exported="true"/>

        <receiver
                android:name=".service.receiver.StartReceiver"
                android:exported="false">
            <intent-filter>
                <action android:name="android.intent.action.USER_PRESENT" />
                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
                <action android:name="android.intent.action.BOOT_COMPLETED" />
            </intent-filter>
        </receiver>

        <provider
                android:name="androidx.core.content.FileProvider"
                android:authorities="${applicationId}.fileProvider"
                android:exported="false"
                android:grantUriPermissions="true">
            <meta-data
                    android:name="android.support.FILE_PROVIDER_PATHS"
                    android:resource="@xml/path_file" />
        </provider>
    </application>

</manifest>