{"v": "5.6.10", "fr": 30, "ip": 0, "op": 90, "w": 720, "h": 1520, "nm": "Large Files-scanning", "ddd": 0, "assets": [{"id": "image_0", "w": 240, "h": 120, "u": "images/", "p": "gaizi.png", "e": 0}, {"id": "image_1", "w": 144, "h": 110, "u": "images/", "p": "v1.png", "e": 0}, {"id": "image_2", "w": 144, "h": 110, "u": "images/", "p": "i1.png", "e": 0}, {"id": "image_3", "w": 180, "h": 59, "u": "images/", "p": "b1.png", "e": 0}, {"id": "image_4", "w": 156, "h": 59, "u": "images/", "p": "b2.png", "e": 0}, {"id": "image_5", "w": 130, "h": 58, "u": "images/", "p": "b3.png", "e": 0}, {"id": "image_6", "w": 204, "h": 150, "u": "images/", "p": "di.png", "e": 0}, {"id": "comp_0", "layers": [{"ddd": 0, "ind": 1, "ty": 2, "nm": "v1.png", "cl": "png", "refId": "image_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [64, 47, 0], "ix": 2}, "a": {"a": 0, "k": [72, 55, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 150, "st": 0, "bm": 0}]}, {"id": "comp_1", "layers": [{"ddd": 0, "ind": 1, "ty": 2, "nm": "v1.png", "cl": "png", "refId": "image_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [64, 47, 0], "ix": 2}, "a": {"a": 0, "k": [72, 55, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 150, "st": 0, "bm": 0}]}, {"id": "comp_2", "layers": [{"ddd": 0, "ind": 1, "ty": 2, "nm": "v1.png", "cl": "png", "refId": "image_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [64, 47, 0], "ix": 2}, "a": {"a": 0, "k": [72, 55, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 150, "st": 0, "bm": 0}]}, {"id": "comp_3", "layers": [{"ddd": 0, "ind": 1, "ty": 2, "nm": "i1.png", "cl": "png", "refId": "image_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [64, 47, 0], "ix": 2}, "a": {"a": 0, "k": [72, 55, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 150, "st": 0, "bm": 0}]}, {"id": "comp_4", "layers": [{"ddd": 0, "ind": 1, "ty": 2, "nm": "i1.png", "cl": "png", "refId": "image_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [64, 47, 0], "ix": 2}, "a": {"a": 0, "k": [72, 55, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 150, "st": 0, "bm": 0}]}, {"id": "comp_5", "layers": [{"ddd": 0, "ind": 1, "ty": 2, "nm": "i1.png", "cl": "png", "refId": "image_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [64, 47, 0], "ix": 2}, "a": {"a": 0, "k": [72, 55, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 150, "st": 0, "bm": 0}]}, {"id": "comp_6", "layers": [{"ddd": 0, "ind": 1, "ty": 2, "nm": "gaizi.png", "cl": "png", "refId": "image_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [120, 125.5, 0], "to": [0, 7.083, 0], "ti": [0, -7.083, 0]}, {"t": 10, "s": [120, 168, 0]}], "ix": 2}, "a": {"a": 0, "k": [120, 60, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ef": [{"ty": 34, "nm": "操控", "np": 6, "mn": "ADBE FreePin3", "ix": 1, "en": 1, "ef": [{"ty": 7, "nm": "人偶引擎", "mn": "ADBE FreePin3 Puppet Engine", "ix": 1, "v": {"a": 0, "k": 2, "ix": 1}}, {"ty": 0, "nm": "网格旋转调整", "mn": "ADBE FreePin3 Auto Rotate Pins", "ix": 2, "v": {"a": 0, "k": 20, "ix": 2}}, {"ty": 7, "nm": "在透明背景上", "mn": "ADBE FreePin3 On Transparent", "ix": 3, "v": {"a": 0, "k": 0, "ix": 3}}, {"ty": 30, "nm": "arap", "np": 3, "mn": "ADBE FreePin3 ARAP Group", "ix": 4, "en": 1, "ef": [{"ty": 6, "nm": "自动追踪形状", "mn": "ADBE FreePin3 Outlines", "ix": 1, "v": 0}, {"ty": 1, "nm": "网格", "np": 1, "mn": "ADBE FreePin3 Mesh Group", "ix": 2, "en": 1, "ef": []}]}]}], "ip": 0, "op": 150, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 2, "nm": "b1.png", "cl": "png", "refId": "image_3", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 5, "s": [0]}, {"t": 15, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [120, 120, 0], "ix": 2}, "a": {"a": 0, "k": [90, 29.5, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 5, "op": 155, "st": 5, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 2, "nm": "b2.png", "cl": "png", "refId": "image_4", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 9, "s": [0]}, {"t": 19, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [120, 98.5, 0], "ix": 2}, "a": {"a": 0, "k": [78, 29.5, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 9, "op": 159, "st": 9, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 2, "nm": "b3.png", "cl": "png", "refId": "image_5", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 13, "s": [0]}, {"t": 23, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [120, 83, 0], "ix": 2}, "a": {"a": 0, "k": [65, 29, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 13, "op": 163, "st": 13, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 2, "nm": "di.png", "cl": "png", "refId": "image_6", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [120, 87, 0], "ix": 2}, "a": {"a": 0, "k": [102, 75, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 150, "st": 0, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "矩形", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [120, 120, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [120, 120], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 4}, "nm": "矩形路径 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [200, 200], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "矩形", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 150, "st": 0, "bm": 0}]}], "layers": [{"ddd": 0, "ind": 1, "ty": 2, "nm": "gaizi.png", "cl": "png", "refId": "image_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [360, 808, 0], "ix": 2}, "a": {"a": 0, "k": [120, 60, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 20, "op": 170, "st": 20, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 0, "nm": "▽ v1", "refId": "comp_0", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 47, "s": [0]}, {"t": 51, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 54, "s": [200, 485, 0], "to": [29.917, 56.5, 0], "ti": [-29.917, -56.5, 0]}, {"t": 74, "s": [379.5, 824, 0]}], "ix": 2}, "a": {"a": 0, "k": [64, 47, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 2.667]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 54, "s": [100, 100, 100]}, {"t": 74, "s": [90, 90, 100]}], "ix": 6}}, "ao": 0, "w": 128, "h": 94, "ip": 47, "op": 197, "st": 47, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 0, "nm": "▽ v2", "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 47, "s": [0]}, {"t": 51, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 57, "s": [184, 469, 0], "to": [29.917, 56.5, 0], "ti": [-29.917, -56.5, 0]}, {"t": 77, "s": [363.5, 808, 0]}], "ix": 2}, "a": {"a": 0, "k": [64, 47, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 2.667]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 57, "s": [100, 100, 100]}, {"t": 77, "s": [90, 90, 100]}], "ix": 6}}, "ao": 0, "w": 128, "h": 94, "ip": 47, "op": 197, "st": 47, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 0, "nm": "▽ v3", "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 47, "s": [0]}, {"t": 51, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 60, "s": [168, 453, 0], "to": [29.917, 56.5, 0], "ti": [-29.917, -56.5, 0]}, {"t": 80, "s": [347.5, 792, 0]}], "ix": 2}, "a": {"a": 0, "k": [64, 47, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 2.667]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 60, "s": [100, 100, 100]}, {"t": 80, "s": [90, 90, 100]}], "ix": 6}}, "ao": 0, "w": 128, "h": 94, "ip": 47, "op": 197, "st": 47, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 0, "nm": "▽ i1", "refId": "comp_3", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 20, "s": [0]}, {"t": 24, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 27, "s": [520, 485, 0], "to": [-29, 56.667, 0], "ti": [29, -56.667, 0]}, {"t": 47, "s": [346, 825, 0]}], "ix": 2}, "a": {"a": 0, "k": [64, 47, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, -1.083]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 27, "s": [100, 100, 100]}, {"t": 47, "s": [90, 90, 100]}], "ix": 6}}, "ao": 0, "w": 128, "h": 94, "ip": 20, "op": 170, "st": 20, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 0, "nm": "▽ i2", "refId": "comp_4", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 20, "s": [0]}, {"t": 24, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 29, "s": [536, 469, 0], "to": [-29, 56.667, 0], "ti": [29, -56.667, 0]}, {"t": 49, "s": [362, 809, 0]}], "ix": 2}, "a": {"a": 0, "k": [64, 47, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, -1.083]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 29, "s": [100, 100, 100]}, {"t": 49, "s": [90, 90, 100]}], "ix": 6}}, "ao": 0, "w": 128, "h": 94, "ip": 20, "op": 170, "st": 20, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 0, "nm": "▽ i3", "refId": "comp_5", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 20, "s": [0]}, {"t": 24, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 32, "s": [552, 453, 0], "to": [-29, 56.667, 0], "ti": [29, -56.667, 0]}, {"t": 52, "s": [378, 793, 0]}], "ix": 2}, "a": {"a": 0, "k": [64, 47, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, -1.083]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 32, "s": [100, 100, 100]}, {"t": 52, "s": [90, 90, 100]}], "ix": 6}}, "ao": 0, "w": 128, "h": 94, "ip": 20, "op": 170, "st": 20, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 0, "nm": "▽ icon/storage/Large Files", "refId": "comp_6", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [360, 760, 0], "ix": 2}, "a": {"a": 0, "k": [120, 120, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 240, "h": 240, "ip": 0, "op": 150, "st": 0, "bm": 0}], "markers": []}