pluginManagement {
    repositories {
        google()
        mavenCentral()
        gradlePluginPortal()
        maven (url = "https://www.jitpack.io")
        maven ("https://artifacts.applovin.com/android" )
    }
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        google()
        mavenCentral()
        maven (url = "https://www.jitpack.io")
        maven ("https://dl-maven-android.mintegral.com/repository/mbridge_android_sdk_oversea")
        maven ("https://artifact.bytedance.com/repository/pangle")
    }
}

rootProject.name = "newDc"
include(":app")
