// Top-level build file where you can add configuration options common to all sub-projects/modules.
buildscript {
    repositories {
        dependencies {
            classpath("com.applovin.quality:AppLovinQualityServiceGradlePlugin:+")
        }
    }
}

plugins {
    id("com.android.application") version "8.2.0" apply false
    id("org.jetbrains.kotlin.android") version "1.9.24" apply false
    id("com.google.gms.google-services") version "4.3.15" apply false
}
